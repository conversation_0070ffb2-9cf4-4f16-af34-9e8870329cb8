# RoxyF.py - 基于 HAR 分析的 Augment Code 自动化注册脚本

## 概述

RoxyF.py 是基于实际网络流量 HAR 包分析开发的全新 Augment Code 自动化注册脚本。相比之前的版本，RoxyF 具有更高的成功率、更好的错误处理和更智能的网络监控功能。

## 主要特性

### 🚀 基于 HAR 分析的优化
- 根据实际网络请求流程优化
- 智能检测页面状态和错误
- 基于真实用户行为模式设计

### 🛡️ 增强的反检测机制
- 完整的浏览器指纹伪装
- 人类化的操作模式
- 随机延迟和行为模拟

### 📊 智能网络监控
- 实时网络请求记录
- 自动检测认证相关请求
- 完整的 HAR 格式日志保存

### 🔧 模块化设计
- `AugmentRegistrationBot`: 主要注册流程
- `NetworkMonitor`: 网络监控
- `TurnstileHandler`: Turnstile 验证码处理
- `EmailVerificationHandler`: 邮箱验证处理

## 安装要求

```bash
pip install selenium requests
```

## 配置

### 1. RoxyClient 配置
确保 RoxyClient 正确配置：
- 端口: 50000
- Token: 76b402c778930dd44bacc693cb2fb8d0
- 浏览器 ID: 1c42949de80fb5f609dc1fcee8fddde3

### 2. 验证码服务配置
确保验证码求解服务运行在：
- URL: http://************:5000
- 支持 Turnstile 验证码求解

### 3. 邮箱服务配置
确保邮箱服务正常工作：
- `get_email()` 函数可用
- `get_email_data()` 函数可用

## 使用方法

### 单次运行
```bash
python RoxyF.py
```

### 持续运行
```bash
python RoxyF.py --continuous
```

## 输出文件

### 1. 认证信息
- 位置: `auth_data/`
- 格式: `auth_info_{email}_{timestamp}.json`
- 内容: localStorage, sessionStorage, cookies

### 2. 网络日志
- 位置: `network_logs/`
- 格式: `roxyf_network_{email}_{timestamp}.json`
- 内容: 页面访问记录和资源信息

### 3. 系统日志
- 位置: `logs/`
- 格式: `roxyf_log_{timestamp}.txt`
- 内容: 详细的执行日志

## 配置文件

可以创建 `roxyf_config.json` 来自定义配置：

```json
{
  "login_url": "https://login.augmentcode.com/u/login/identifier",
  "auth_domain": "login.augmentcode.com",
  "app_domain": "app.augmentcode.com",
  "turnstile_sitekey": "0x4AAAAAAAQFNSW6xordsuIq",
  "captcha_service_url": "http://************:5000",
  "page_load_timeout": 15,
  "verification_timeout": 30,
  "dashboard_timeout": 45,
  "max_email_attempts": 10,
  "max_registration_attempts": 3
}
```

## 流程说明

### 1. 初始化阶段
- 启动 RoxyClient 浏览器
- 应用反检测设置
- 启动网络监控

### 2. 邮箱注册阶段
- 获取临时邮箱
- 导航到登录页面
- 输入邮箱地址

### 3. 邮箱验证阶段
- 等待验证码邮件
- 自动获取验证码
- 输入并提交验证码

### 4. 验证码处理阶段
- 检测 Turnstile 验证码
- 调用求解服务
- 注入验证码 token

### 5. 成功检测阶段
- 智能检测页面状态
- 验证成功指标
- 提取认证信息

## 错误处理

### 常见错误及解决方案

1. **浏览器启动失败**
   - 检查 RoxyClient 服务状态
   - 确认浏览器 ID 正确
   - 检查端口是否被占用

2. **邮箱验证失败**
   - 检查邮箱服务状态
   - 确认 JWT token 有效
   - 增加验证码获取重试次数

3. **Turnstile 验证失败**
   - 检查验证码服务状态
   - 确认 sitekey 正确
   - 检查网络连接

4. **页面检测失败**
   - 检查目标网站是否有变化
   - 更新页面检测规则
   - 增加等待时间

## 监控和调试

### 1. 实时监控
- 控制台输出详细日志
- 网络请求实时记录
- 页面状态智能检测

### 2. 日志分析
- 查看 `logs/` 目录下的日志文件
- 分析网络请求模式
- 检查错误原因

### 3. 数据分析
- 认证信息提取结果
- 网络监控数据
- 成功率统计

## 性能优化

### 1. 网络优化
- 智能的页面加载检测
- 最小化不必要的等待
- 并行处理验证步骤

### 2. 错误恢复
- 多层错误检测机制
- 智能重试策略
- 降级处理方案

### 3. 资源管理
- 自动清理浏览器资源
- 内存使用优化
- 文件系统管理

## 版本历史

- **v1.0**: 基于 HAR 分析的初始版本
- 完整的模块化设计
- 智能网络监控
- 增强的错误处理

## 注意事项

1. **合规使用**: 仅用于测试和学习目的
2. **频率控制**: 避免过于频繁的请求
3. **资源清理**: 确保正确清理浏览器资源
4. **日志安全**: 注意保护敏感信息

## 技术支持

如有问题，请检查：
1. 依赖服务是否正常运行
2. 配置文件是否正确
3. 网络连接是否稳定
4. 日志文件中的错误信息
