2025-08-10 10:38:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58858,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:38:44 - INFO - <EMAIL>
2025-08-10 10:38:44 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:39:01 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:39:05 - INFO - 找到 Turnstile...
2025-08-10 10:39:07 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:39:11 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:39:11 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:39:11 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:39:11 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:39:11 - INFO - 检测到 'Verifying you are human...'（首次），使用网络节流模式
2025-08-10 10:39:11 - INFO - 检测到人机验证，执行 CDP 网络节流 10 秒
2025-08-10 10:39:11 - INFO - 执行网络节流前的拟人操作...
2025-08-10 10:39:11 - INFO - 已执行拟人滚动: 63px
2025-08-10 10:39:12 - INFO - 已执行拟人操作完成，总停顿: 1.11秒
2025-08-10 10:39:20 - INFO - CDP: 已将网络设为节流模式（延迟8687ms），保持 11.0 秒……
2025-08-10 10:40:14 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 10:40:14 - INFO - 已执行拟人滚动: -142px
2025-08-10 10:40:15 - INFO - 已执行页面焦点操作
2025-08-10 10:40:15 - INFO - 已执行拟人操作完成，总停顿: 0.88秒
2025-08-10 10:40:15 - INFO - CDP: 网络节流已恢复为正常状态。
2025-08-10 10:40:15 - INFO - 成功！当前在订阅页面。
2025-08-10 10:40:15 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:40:15 - INFO - 代码信息: {
  "codeVerifier": "axuuakrrZ74kLT_3i74lmSMBA_CJkFL28u9giNUOJvw",
  "code_challenge": "LVHL8S_nqq0XAGmIjOWXYlqe3OsiFOlWPh1NHThHcX8",
  "state": "6ab478f9-0b7f-4a65-acd6-34129372b472"
}
2025-08-10 10:40:15 - INFO - ==================================================
2025-08-10 10:40:16 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_14003caac2ae06eb07688e88dd0fc4da&state=6ab478f9-0b7f-4a65-acd6-34129372b472&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-10 10:40:18 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:40:18 - INFO - 添加session: <EMAIL>   71531c2194e106c398cda449526f30071e415bae90d3bb5a4add1cb42ec65142   https://d8.api.augmentcode.com/  2025-08-17T02:39:27Z
2025-08-10 10:40:18 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mCKnemDlX9CWjqYRlpIeSRG_XcJceHy3txz7gu6mUtymfMK7Vo2rmBwKY7PLrvE0AJUcI87578cw9xtC5cuhqPg5OL4VsYaodAz8jWgDYNX0iDaY56n3B-k1mgtChQNSSIhrazgtJyCQzTubuVGE2or6SpuS3JlDVP_4HKZ8hgzw484fxtvHTWsaiIXamRStUeFdS-U6cloIk_w-QJgEERJ.aJgGkQ.WhnnxeO_4br3uX0eIaJe85mWwgs
2025-08-10 10:40:18 - INFO - 
自动化流程成功完成！
2025-08-10 10:40:18 - INFO - 添加第1个
2025-08-10 10:40:43 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59058,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:40:44 - INFO - <EMAIL>
2025-08-10 10:40:44 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:41:00 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:41:04 - INFO - 找到 Turnstile...
2025-08-10 10:41:06 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:41:09 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:41:18 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:41:18 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:41:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:41:18 - INFO - 检测到 'Verifying you are human...'（第2次），使用网络离线模式
2025-08-10 10:41:18 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 10:41:18 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 10:41:18 - INFO - 已执行拟人滚动: -47px
2025-08-10 10:41:27 - INFO - 已执行页面焦点操作
2025-08-10 10:41:28 - INFO - 已执行拟人操作完成，总停顿: 1.43秒
2025-08-10 10:41:28 - INFO - CDP: 已将网络设为离线，保持 9.0 秒……
2025-08-10 10:41:37 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 10:41:37 - INFO - 已执行拟人滚动: 46px
2025-08-10 10:41:38 - INFO - 已执行页面焦点操作
2025-08-10 10:41:38 - INFO - 已执行拟人操作完成，总停顿: 0.99秒
2025-08-10 10:41:38 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 10:41:38 - INFO - 成功！当前在订阅页面。
2025-08-10 10:41:38 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:41:38 - INFO - 代码信息: {
  "codeVerifier": "VGbF5CxOG5SGVB3dkQFFNb6req-NQg3EhZif0ua4KXc",
  "code_challenge": "xj8iyKekflxh4AVfFaMxEPZ8z79v90Km9mFMbSZbRnM",
  "state": "de4d7d65-b9e0-4964-bdc4-b2416e9e6c77"
}
2025-08-10 10:41:38 - INFO - ==================================================
2025-08-10 10:41:39 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_406c84aaf1c818a21b4fdbbe423bcb26&state=de4d7d65-b9e0-4964-bdc4-b2416e9e6c77&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-10 10:41:41 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:41:41 - INFO - 添加session: <EMAIL>   857a4b183c15b592a6ccea830edac2112b955215b957fccd173e5448a8013a46   https://d5.api.augmentcode.com/  2025-08-17T02:41:25Z
2025-08-10 10:41:41 - INFO - email:<EMAIL> === cookie:.eJxNjc2OwjAQg99lzu1qkuaXE29ShWaCIppplbZIq2XfnQg4cLMt-_MfjCvVEph4h9NeD-oghZLn35FDITgBdHDNd-Ivn-M6HhvVMccWUAl5fhjnHZpJkiIZlY_p0oxwqdV54aktB7RSWo_aW9TaaeFEBy_Mi9BIPC_HKqxW1g9msOethLrHZbpR_Vl4zkzwWbyP0aRo0PRGpKFX6LEPEW1T2osgvTKO4P8Jx21Ehw.aJgG5Q.T4I8_oOa4eJpS4lrr_e5gpCClWo
2025-08-10 10:41:41 - INFO - 
自动化流程成功完成！
2025-08-10 10:41:41 - INFO - 添加第2个
2025-08-10 10:41:57 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59197,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:41:57 - INFO - <EMAIL>
2025-08-10 10:41:57 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:42:13 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:42:17 - INFO - 找到 Turnstile...
2025-08-10 10:42:19 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:42:22 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:42:23 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:42:23 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:42:23 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:42:23 - INFO - 检测到 'Verifying you are human...'（第3次），使用网络离线模式
2025-08-10 10:42:23 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 10:42:23 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 10:42:23 - INFO - 已执行拟人滚动: 16px
2025-08-10 10:42:24 - INFO - 已执行拟人操作完成，总停顿: 1.14秒
2025-08-10 10:42:31 - INFO - CDP: 已将网络设为离线，保持 7.6 秒……
2025-08-10 10:42:40 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 10:42:40 - INFO - 已执行拟人滚动: 98px
2025-08-10 10:42:41 - INFO - 已执行拟人操作完成，总停顿: 1.13秒
2025-08-10 10:42:41 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 10:42:46 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-10 10:42:55 - INFO - Traceback (most recent call last):

2025-08-10 10:42:55 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m966[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-10 10:42:55 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m934[0m, in [35mmain[0m
    result = attempt_signup_with_retry(driver)

2025-08-10 10:42:55 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m799[0m, in [35mattempt_signup_with_retry[0m
    [31mtime.sleep[0m[1;31m(30)[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^[0m

2025-08-10 10:42:55 - INFO - [1;35mKeyboardInterrupt[0m

