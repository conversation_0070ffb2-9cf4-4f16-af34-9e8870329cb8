{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-15T04:12:48.362081Z", "time": 0, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "132236"}], "cookies": [], "content": {"size": 132236, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 132236}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "工作台"}, {"startedDateTime": "2025-08-15T04:12:48.366134Z", "time": 5.800000011920929, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DWNmc_MY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21479, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21479}, "cache": {}, "timings": {"send": 0, "wait": 5.800000011920929, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:12:48.366144Z", "time": 5.9000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-BKOrM9G3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 146848, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 146848}, "cache": {}, "timings": {"send": 0, "wait": 5.9000000059604645, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366147Z", "time": 8, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-H8ttwM1S.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 474, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 474}, "cache": {}, "timings": {"send": 0, "wait": 8, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366150Z", "time": 8.099999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-B9PrHQl7.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4414, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4414}, "cache": {}, "timings": {"send": 0, "wait": 8.099999994039536, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366153Z", "time": 13, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DjWl3JGw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 786361, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 786361}, "cache": {}, "timings": {"send": 0, "wait": 13, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366156Z", "time": 8.700000017881393, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CIEDqDGY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 468, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 468}, "cache": {}, "timings": {"send": 0, "wait": 8.700000017881393, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366159Z", "time": 11.700000017881393, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BN5KR6Qs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5129, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5129}, "cache": {}, "timings": {"send": 0, "wait": 11.700000017881393, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366161Z", "time": 12.300000011920929, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtKobjF2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14336, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14336}, "cache": {}, "timings": {"send": 0, "wait": 12.300000011920929, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366164Z", "time": 14.200000017881393, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BRMOCcEH.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 130305, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 130305}, "cache": {}, "timings": {"send": 0, "wait": 14.200000017881393, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366167Z", "time": 13.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-R6XwfvJN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3351, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3351}, "cache": {}, "timings": {"send": 0, "wait": 13.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366170Z", "time": 14.600000023841858, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DeQidb8Q.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6357, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6357}, "cache": {}, "timings": {"send": 0, "wait": 14.600000023841858, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366173Z", "time": 14.900000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-kZT51Bed.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2762, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2762}, "cache": {}, "timings": {"send": 0, "wait": 14.900000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366176Z", "time": 15, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-Bd2HX4Pl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5330, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5330}, "cache": {}, "timings": {"send": 0, "wait": 15, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366179Z", "time": 15.399999976158142, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_user_key-BQXKMoNP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2168, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2168}, "cache": {}, "timings": {"send": 0, "wait": 15.399999976158142, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366182Z", "time": 8.299999982118607, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-C_thiKp4.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3318, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 3318}, "cache": {}, "timings": {"send": 0, "wait": 8.299999982118607, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-15T04:12:48.366186Z", "time": 8.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DwV2T02E.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 50384, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 50384}, "cache": {}, "timings": {"send": 0, "wait": 8.5, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-15T04:12:48.366189Z", "time": 10.099999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-ntgrtH_u.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1564, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 1564}, "cache": {}, "timings": {"send": 0, "wait": 10.099999994039536, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-15T04:12:48.366193Z", "time": 10.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtkFttLu.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 42774, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 42774}, "cache": {}, "timings": {"send": 0, "wait": 10.599999994039536, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-15T04:12:48.366196Z", "time": 10.900000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DDWJuWk8.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 9043, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 9043}, "cache": {}, "timings": {"send": 0, "wait": 10.900000005960464, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-15T04:12:48.366200Z", "time": 2, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/app/config", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 408, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 408}, "cache": {}, "timings": {"send": 0, "wait": 2, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-15T04:12:48.366204Z", "time": 1.600000023841858, "request": {"method": "GET", "url": "http://127.0.0.1:45535/zh-CN-DTI-qNBp.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94467, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 94467}, "cache": {}, "timings": {"send": 0, "wait": 1.600000023841858, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:12:48.366208Z", "time": 3.600000023841858, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/list?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6466, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 6466}, "cache": {}, "timings": {"send": 0, "wait": 3.600000023841858, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-15T04:12:48.366212Z", "time": 6, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/proxy/detection?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 655, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 655}, "cache": {}, "timings": {"send": 0, "wait": 6, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-15T04:12:48.366216Z", "time": 5.699999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/core-info?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 406, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 406}, "cache": {}, "timings": {"send": 0, "wait": 5.699999988079071, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-15T04:12:48.366220Z", "time": 2.199999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_spin-Dat2nEi2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 822, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 822}, "cache": {}, "timings": {"send": 0, "wait": 2.199999988079071, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:12:48.366224Z", "time": 2.5999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob2-CEoTd5f3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1157, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1157}, "cache": {}, "timings": {"send": 0, "wait": 2.5999999940395355, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:12:48.366228Z", "time": 2.699999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob1-CCAm7wau.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1143, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1143}, "cache": {}, "timings": {"send": 0, "wait": 2.699999988079071, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:12:48.366232Z", "time": 24.899999976158142, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/JetBrains%20Mono/JetBrainsMono-Bold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 278128, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 278128}, "cache": {}, "timings": {"send": 0, "wait": 24.899999976158142, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-15T04:12:48.366236Z", "time": 26.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Regular.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 407356, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 407356}, "cache": {}, "timings": {"send": 0, "wait": 26.599999994039536, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-15T04:12:48.366241Z", "time": 27.099999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Medium.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 411800, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 411800}, "cache": {}, "timings": {"send": 0, "wait": 27.099999994039536, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-15T04:12:48.366245Z", "time": 27.299999982118607, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-SemiBold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 414276, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 414276}, "cache": {}, "timings": {"send": 0, "wait": 27.299999982118607, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-15T04:12:48.366249Z", "time": 6.0999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 285778, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 285778}, "cache": {}, "timings": {"send": 0, "wait": 6.0999999940395355, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-15T04:12:48.366253Z", "time": 2.199999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/US-BXoNwYaC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5807, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5807}, "cache": {}, "timings": {"send": 0, "wait": 2.199999988079071, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:12:48.366257Z", "time": 2.4000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_speed_test-2BHwaLbY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2258, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2258}, "cache": {}, "timings": {"send": 0, "wait": 2.4000000059604645, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.726282Z", "time": 0, "request": {"method": "GET", "url": "https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBfNlZKcDVfRVZsa1RFeWItSWRPOVJtVU9pVk8zc3BpQqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFHMmtfRElkZjA2cDJDRFFxQmlMVG85QW1SNjdSbTJOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "50081"}], "cookies": [], "content": {"size": 50081, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 50081}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Code"}, {"startedDateTime": "2025-08-15T04:13:13.730846Z", "time": 0, "request": {"method": "GET", "url": "https://cdn.auth0.com/ulp/react-components/1.146.2/css/main.cdn.min.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-15T04:13:13.730864Z", "time": 0, "request": {"method": "GET", "url": "https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730874Z", "time": 183.69999998807907, "request": {"method": "GET", "url": "https://www.augmentcode.com/android-chrome-512x512.png", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "image/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 183.69999998807907, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-15T04:13:13.730883Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730890Z", "time": 0.9000000059604645, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/v1/projects/ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg/settings", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0.9000000059604645, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-15T04:13:13.730899Z", "time": 172.80000001192093, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-16732971011&en=page_view&dr=login.augmentcode.com&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge&scrsrc=www.googletagmanager.com&frm=0&rnd=1831587841.1755202390&dt=Augment%20Code&auid=180211618.1755202372&navt=n&npa=0&gtm=45je58d0v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=101509157~103116026~103200004~103233427~104527907~104528500~104684208~104684211~104948813~105033766~105033768~105102050~105103161~105103163~105231383~105231385&tft=1755202390335&tfd=3389&apve=1&apvf=f", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 172.80000001192093, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-15T04:13:13.730908Z", "time": 186.09999999403954, "request": {"method": "GET", "url": "https://googleads.g.doubleclick.net/pagead/viewthroughconversion/16732971011/?random=1755202390333&cv=11&fst=1755202390333&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58d0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=101509157~103116026~103200004~103233427~104527907~104528500~104684208~104684211~104948813~105033766~105033768~105102050~105103161~105103163~105231383~105231385&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBfNlZKcDVfRVZsa1RFeWItSWRPOVJtVU9pVk8zc3BpQqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFHMmtfRElkZjA2cDJDRFFxQmlMVG85QW1SNjdSbTJOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBfNlZKcDVfRVZsa1RFeWItSWRPOVJtVU9pVk8zc3BpQqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFHMmtfRElkZjA2cDJDRFFxQmlMVG85QW1SNjdSbTJOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=180211618.1755202372&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=4", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2536, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2536}, "cache": {}, "timings": {"send": 0, "wait": 186.09999999403954, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730917Z", "time": 192.30000001192093, "request": {"method": "GET", "url": "https://analytics.google.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58d0v9191852910za200zd9191852910&_p=1755202390296&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=101509157~103116026~103200004~103233427~104527907~104528500~104684208~104684211~104948813~105033766~105033768~105102050~105103161~105103163~105231383~105231385&cid=191486972.1755202372&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&_s=1&sid=1755202371&sct=1&seg=1&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBfNlZKcDVfRVZsa1RFeWItSWRPOVJtVU9pVk8zc3BpQqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFHMmtfRElkZjA2cDJDRFFxQmlMVG85QW1SNjdSbTJOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dr=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBfNlZKcDVfRVZsa1RFeWItSWRPOVJtVU9pVk8zc3BpQqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFHMmtfRElkZjA2cDJDRFFxQmlMVG85QW1SNjdSbTJOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dt=Augment%20Code&en=page_view&_ee=1&tfd=3401", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 192.30000001192093, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-15T04:13:13.730925Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/ajs-destination.bundle.8e6b895db75187c55313.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730934Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/schemaFilter.bundle.1b218d13fed021531d4e.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730942Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730951Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730961Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/845/431110629a9fe8297174.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730970Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/facebook-pixel/2.11.5/facebook-pixel.dynamic.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730979Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730988Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.730998Z", "time": 0, "request": {"method": "GET", "url": "https://connect.facebook.net/en_US/fbevents.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.731008Z", "time": 0, "request": {"method": "GET", "url": "https://connect.facebook.net/signals/config/4930228537203160?v=2.9.224&r=stable&domain=login.augmentcode.com&hme=2e9ee56babe122798b967566f46100108daa710154b06378259c746cb66ac325&ex_m=83%2C143%2C124%2C18%2C117%2C58%2C40%2C118%2C64%2C57%2C131%2C72%2C13%2C82%2C26%2C112%2C103%2C62%2C65%2C111%2C128%2C91%2C133%2C7%2C3%2C4%2C6%2C5%2C2%2C73%2C81%2C134%2C208%2C155%2C52%2C213%2C210%2C211%2C45%2C170%2C25%2C61%2C217%2C216%2C158%2C28%2C51%2C8%2C54%2C77%2C78%2C79%2C84%2C107%2C27%2C24%2C110%2C106%2C105%2C125%2C63%2C127%2C126%2C41%2C108%2C50%2C100%2C12%2C130%2C37%2C199%2C201%2C165%2C21%2C22%2C23%2C15%2C16%2C36%2C33%2C34%2C68%2C74%2C76%2C89%2C116%2C119%2C38%2C90%2C19%2C17%2C94%2C59%2C31%2C121%2C120%2C122%2C113%2C20%2C30%2C49%2C88%2C129%2C29%2C180%2C151%2C257%2C197%2C141%2C183%2C176%2C86%2C109%2C67%2C98%2C44%2C39%2C96%2C97%2C102%2C48%2C14%2C104%2C95%2C55%2C43%2C46%2C0%2C80%2C132%2C1%2C101%2C11%2C99%2C9%2C47%2C75%2C53%2C123%2C56%2C93%2C71%2C70%2C42%2C114%2C69%2C66%2C60%2C92%2C85%2C35%2C115%2C32%2C87%2C10%2C135", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.731018Z", "time": 0, "request": {"method": "GET", "url": "https://us.i.posthog.com/static/array.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.731028Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_GIfRcxAmWBSIxniAKUhlM9pQ343wGGnAgC3iBMO1FSN/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:13:13.731038Z", "time": 221.09999999403954, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755202390402&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 221.09999999403954, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-15T04:13:13.731048Z", "time": 179.69999998807907, "request": {"method": "GET", "url": "https://www.google.com/pagead/1p-user-list/16732971011/?random=1755202390333&cv=11&fst=1755201600000&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58d0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=101509157~103116026~103200004~103233427~104527907~104528500~104684208~104684211~104948813~105033766~105033768~105102050~105103161~105103163~105231383~105231385&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBfNlZKcDVfRVZsa1RFeWItSWRPOVJtVU9pVk8zc3BpQqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFHMmtfRElkZjA2cDJDRFFxQmlMVG85QW1SNjdSbTJOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBfNlZKcDVfRVZsa1RFeWItSWRPOVJtVU9pVk8zc3BpQqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFHMmtfRElkZjA2cDJDRFFxQmlMVG85QW1SNjdSbTJOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=180211618.1755202372&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=3&is_vtc=1&cid=CAQSigEA2abss6WjB0qFch7m0DeOUnBRZcjQCDD9WWR8YSLwbiR32NwMcg2V8Ek4_b1I8_bfBgHK5msmG7YXGv4RnNYVseC4l60ZGRYpSSKflX9MzXJzQClzYUyswJr-cXwS8N4CJ7unix3FpPbZ8nWqy_xS63gQR6mwlIC0xlRvZgRcchUnoHkcZ6nE0RM&random=1580004715&rmt_tld=0&ipr=y", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 342, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 342}, "cache": {}, "timings": {"send": 0, "wait": 179.69999998807907, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-15T04:13:13.731058Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-15T04:14:02.268826Z", "time": 0, "request": {"method": "GET", "url": "https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=SbhGYaNIXKug49mwQT6HpqWPPQbsMbl14B-ub_j9v9I&code_challenge=nv9vWiakHXAlvggMjEv4K_VmTSfWgWNhRoOFBA-6Tmo&code_challenge_method=S256", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "10333"}], "cookies": [], "content": {"size": 10333, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 10333}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Login"}, {"startedDateTime": "2025-08-15T04:14:02.272428Z", "time": 0, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css?family=Offside", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-15T04:14:02.272442Z", "time": 0, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css2?family=Inter:wght@500&family=Roboto&display=swap", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-15T04:14:02.272449Z", "time": 0, "request": {"method": "GET", "url": "https://fonts.gstatic.com/s/inter/v19/UcCO3FwrK3iLTeHuS_nVMrMxCp50SjIw2boKoduKmMEVuI6fAZ9hiA.woff2", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "css"}], "websockets": [], "pages": [{"startedDateTime": "2025-08-15T04:14:35.328996Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}