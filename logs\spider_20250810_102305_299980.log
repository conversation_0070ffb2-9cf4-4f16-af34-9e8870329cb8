2025-08-10 10:23:11 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57504,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:23:12 - INFO - <EMAIL>
2025-08-10 10:23:12 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:23:32 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:23:35 - INFO - 找到 Turnstile...
2025-08-10 10:23:37 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:23:40 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:23:41 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:23:41 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:23:41 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:24:00 - INFO -    等待 4.52 秒...
2025-08-10 10:24:10 - INFO - 刷新完成，继续下一次尝试。
2025-08-10 10:24:10 - INFO - 
第 2/5 次尝试注册...
2025-08-10 10:24:10 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:24:10 - INFO - 成功！当前在订阅页面。
2025-08-10 10:24:10 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:24:10 - INFO - 代码信息: {
  "codeVerifier": "zQgS7XEtHyNdFMj85bvDT9GZPE6fc91s9lz_vVWyGEc",
  "code_challenge": "jdul3RutI42UCg7Fj4aIV_ko7QZWTDp4Cb55hPGcxzs",
  "state": "291dacb0-f563-4934-8abd-02c83e398fd5"
}
2025-08-10 10:24:10 - INFO - ==================================================
2025-08-10 10:24:11 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_95f15966c6c1b2fbe3f788e04741d0c1&state=291dacb0-f563-4934-8abd-02c83e398fd5&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-10 10:24:13 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:24:13 - INFO - 添加session: <EMAIL>   b9febd05d7095390f586a224ec555f0958a9fa50b00aadb7e28027400a393405   https://d6.api.augmentcode.com/  2025-08-17T02:23:54Z
2025-08-10 10:24:13 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2CWsttuPfkmpNDF1EDR-pMY9d0lxIPHmXzzzQu6s5Y5ZM032N_KXSsYw5ymZ5fDrLAHqOCYHpr_corn7n7V0qW4FjqHNL2teEETvJKaSD6OvcHWoV3xvORhXZIlFPGOjbcN2wZZKtg0m2E1nUq5LI1jct6w8IEtC7ndMEb4kdshKvYsQ6y9cViTkbbuG7R1sMqxJU9DGOHzBfL0QAE.aJgCzA.n8ko5n9CDTbHIoN1uo4hHctqoi8
2025-08-10 10:24:13 - INFO - 
自动化流程成功完成！
2025-08-10 10:24:13 - INFO - 添加第1个
2025-08-10 10:24:30 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57672,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:24:31 - INFO - <EMAIL>
2025-08-10 10:24:31 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:24:48 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:24:51 - INFO - 找到 Turnstile...
2025-08-10 10:24:53 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:24:56 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:25:06 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:25:06 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:25:06 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:25:06 - INFO - 检测到 'Verifying you are human...'（首次），使用网络节流模式
2025-08-10 10:25:06 - INFO - 检测到人机验证，执行 CDP 网络节流 10 秒
2025-08-10 10:25:06 - INFO - 执行网络节流前的拟人操作...
2025-08-10 10:25:06 - INFO - 已执行拟人滚动: -115px
2025-08-10 10:25:14 - INFO - 已执行页面焦点操作
2025-08-10 10:25:15 - INFO - 已执行拟人操作完成，总停顿: 1.29秒
2025-08-10 10:25:15 - INFO - CDP: 已将网络设为节流模式（延迟9360ms），保持 9.5 秒……
2025-08-10 10:25:26 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 10:25:26 - INFO - 已执行拟人滚动: -74px
2025-08-10 10:25:26 - INFO - 已执行拟人操作完成，总停顿: 0.94秒
2025-08-10 10:25:26 - INFO - CDP: 网络节流已恢复为正常状态。
2025-08-10 10:25:26 - INFO - 成功！当前在订阅页面。
2025-08-10 10:25:26 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:25:26 - INFO - 代码信息: {
  "codeVerifier": "4y10NO2UIE7fcfb9cYJcOldCS6mNasjDJXPfNrsrhHM",
  "code_challenge": "kCgGEwwff9ANi6cLqUmygBx_cRzXZt6z4drZ1c9F15k",
  "state": "75c569bc-e31e-437d-ab5b-d31cb8b9cd25"
}
2025-08-10 10:25:26 - INFO - ==================================================
2025-08-10 10:25:28 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_3513e219ae83ef72be0fa492c0971140&state=75c569bc-e31e-437d-ab5b-d31cb8b9cd25&tenant_url=https%3A%2F%2Fd2.api.augmentcode.com%2F
2025-08-10 10:25:29 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:25:29 - INFO - 添加session: <EMAIL>   c0ed893ace26983ed741d87dd709507b622da3f2ddc1164c9148c49023556f13   https://d2.api.augmentcode.com/  2025-08-17T02:25:13Z
2025-08-10 10:25:29 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgyAQhN9lz9oALsh66psYhKUhEWr8S5q2715reuhxJt9884R-4jm7wmWFbp03riC6nMZHX1xm6AAquKWdy19OYeq3hec-haPg7NL4MpasUFEzsgpIIQ5KO0P2wMu9-GMpUWpJRGhJNYhCalPBqTkNh2ledvay1diSMkZftdEW24uPAX7kefj1DpJ9LQZla2waV5MzpkaPTCzICyHg_QER3EAn.aJgDGQ.8kfxjoQz6S7mMoy7oMqZLDufKO4
2025-08-10 10:25:29 - INFO - 
自动化流程成功完成！
2025-08-10 10:25:29 - INFO - 添加第2个
2025-08-10 10:25:54 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57829,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:25:55 - INFO - <EMAIL>
2025-08-10 10:25:55 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:26:11 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:26:13 - INFO - 找到 Turnstile...
2025-08-10 10:26:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:26:18 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:26:18 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:26:18 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:26:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:26:29 - INFO -    等待 3.14 秒...
2025-08-10 10:26:37 - INFO - 刷新完成，继续下一次尝试。
2025-08-10 10:26:37 - INFO - 
第 2/5 次尝试注册...
2025-08-10 10:26:37 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:26:37 - INFO - 成功！当前在订阅页面。
2025-08-10 10:26:37 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:26:37 - INFO - 代码信息: {
  "codeVerifier": "Y8_-rNln5VtZpaMXq3zWG272CUkyFPixyV3G5AINY-U",
  "code_challenge": "Z4iZzGPwVuWTpSodqjBaI5tCzIwO5zF7rjJ1Yzu7fzA",
  "state": "1d1f788a-ce23-4cc9-b9e9-4f03782e8a00"
}
2025-08-10 10:26:37 - INFO - ==================================================
2025-08-10 10:26:38 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_717e9664be388abfb921c00b8dfa43d9&state=1d1f788a-ce23-4cc9-b9e9-4f03782e8a00&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-10 10:26:40 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:26:40 - INFO - 添加session: <EMAIL>   52f70e3d691222207c59e1b2f1270db2f0afc2b77c93701efb1302c76d95428d   https://d9.api.augmentcode.com/  2025-08-17T02:26:40.848697444Z
2025-08-10 10:26:40 - INFO - email:<EMAIL> === cookie:.eJxNjUkOwjAQBP8y5wQl42XsnPiJ5WWMLCUmhEVi-ztR4MCxW9XVT3AzL5OvXC8wXJYrN5D9VMa7q35iGAAaOJQb179c0uyuZ15cSWvBky_jSxtrOiGJJWOSNuWAwUtUK16PNa5L0wthiLQgEhaN6kg3sGk2w2q6P8L51JOSZJGk3SutjKRdzAl-5PdQ6GzR9632xrYSdWotcmy7wCGKnKNChvcHGk1BEA.aJgDXw.4G-H28qQ3iMZm2ld8YmXRRa4xro
2025-08-10 10:26:40 - INFO - 
自动化流程成功完成！
2025-08-10 10:26:40 - INFO - 添加第3个
2025-08-10 10:27:05 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57974,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:27:05 - INFO - <EMAIL>
2025-08-10 10:27:05 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:27:21 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:27:24 - INFO - 找到 Turnstile...
2025-08-10 10:27:26 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:27:30 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:27:44 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:27:44 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:27:44 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:27:44 - INFO - 检测到 'Verifying you are human...'（首次），使用网络节流模式
2025-08-10 10:27:44 - INFO - 检测到人机验证，执行 CDP 网络节流 10 秒
2025-08-10 10:27:44 - INFO - 执行网络节流前的拟人操作...
2025-08-10 10:27:44 - INFO - 已执行拟人滚动: -145px
2025-08-10 10:27:51 - INFO - 已执行页面焦点操作
2025-08-10 10:27:51 - INFO - 已执行拟人操作完成，总停顿: 1.26秒
2025-08-10 10:27:51 - INFO - CDP: 已将网络设为节流模式（延迟9963ms），保持 9.6 秒……
2025-08-10 10:28:02 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 10:28:02 - INFO - 已执行拟人滚动: -2px
2025-08-10 10:28:03 - INFO - 已执行拟人操作完成，总停顿: 1.30秒
2025-08-10 10:28:03 - INFO - CDP: 网络节流已恢复为正常状态。
2025-08-10 10:28:03 - INFO - 成功！当前在订阅页面。
2025-08-10 10:28:03 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:28:03 - INFO - 代码信息: {
  "codeVerifier": "Gzc93mkt7WOH445RI8geY8rVXxolHmY5CEwsKVvtifs",
  "code_challenge": "PC3Ah0IGE1essNcwLr_qOkLmxAo5KKcMXa1l6XoQl0c",
  "state": "b41c77fe-9757-4f1b-aa2a-49c1c167bdbe"
}
2025-08-10 10:28:03 - INFO - ==================================================
2025-08-10 10:28:04 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_fa7e8873a2caa386c5d4915ca46e6f52&state=b41c77fe-9757-4f1b-aa2a-49c1c167bdbe&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-10 10:28:05 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:28:05 - INFO - 添加session: <EMAIL>   c1fffbcfe062f7fa0393a31e1e97f8e939b2f0651d2537d736992eee1718a75c   https://d13.api.augmentcode.com/  2025-08-17T02:27:49Z
2025-08-10 10:28:05 - INFO - email:<EMAIL> === cookie:.eJxNjUEOwjAMBP_ic4uaxElsTvykCrUDkWhApVRCwN-pKg4cdzU7-4L-ptOYqtYZ9vP00AZyGsvl2dc0KuwBGjiVRetfLnLrH3ed-iJroWMql3cgps5RVlQryJKPrjsaa1e8XuuwLl3ojDeRAvpgTOCI1MCm2QyraVrOaTHRY2RLhg8-eMK4G7LAj9wOCdFbZ1zrLUuLGmPLEqTlIQ1sxWclhM8X-q1AEg.aJgDtQ.xNttKyw-Q_tlAJmt2PnUsV9MEIw
2025-08-10 10:28:05 - INFO - 
自动化流程成功完成！
2025-08-10 10:28:05 - INFO - 添加第4个
2025-08-10 10:28:24 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58121,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:28:24 - INFO - <EMAIL>
2025-08-10 10:28:24 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:28:41 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:28:44 - INFO - 找到 Turnstile...
2025-08-10 10:28:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:28:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:29:02 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:29:02 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:29:02 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:29:02 - INFO - 检测到 'Verifying you are human...'（首次），使用网络节流模式
2025-08-10 10:29:02 - INFO - 检测到人机验证，执行 CDP 网络节流 10 秒
2025-08-10 10:29:02 - INFO - 执行网络节流前的拟人操作...
2025-08-10 10:29:02 - INFO - 已执行拟人滚动: 89px
2025-08-10 10:29:03 - INFO - 已执行页面焦点操作
2025-08-10 10:29:03 - INFO - 已执行拟人操作完成，总停顿: 0.81秒
2025-08-10 10:29:11 - INFO - CDP: 已将网络设为节流模式（延迟8089ms），保持 10.4 秒……
2025-08-10 10:29:23 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 10:29:23 - INFO - 已执行拟人滚动: -26px
2025-08-10 10:29:24 - INFO - 已执行页面焦点操作
2025-08-10 10:29:24 - INFO - 已执行拟人操作完成，总停顿: 1.36秒
2025-08-10 10:29:24 - INFO - CDP: 网络节流已恢复为正常状态。
2025-08-10 10:29:24 - INFO - 成功！当前在订阅页面。
2025-08-10 10:29:24 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:29:24 - INFO - 代码信息: {
  "codeVerifier": "kZGibuHSsz75csi0pzzQsMChLrQiOs_Kbffs45_aU-c",
  "code_challenge": "8u1KZfUJgZ1XQz3fTCoqWgp-45fAXxUAnXQx_sRr83I",
  "state": "a4b1ceb7-65e0-4c65-94ce-829603866353"
}
2025-08-10 10:29:24 - INFO - ==================================================
2025-08-10 10:29:25 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_7b5f35feed3c47427a881f97d2374f25&state=a4b1ceb7-65e0-4c65-94ce-829603866353&tenant_url=https%3A%2F%2Fd14.api.augmentcode.com%2F
2025-08-10 10:29:27 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:29:27 - INFO - 添加session: <EMAIL>   9209dc7363549e5f7a73180fd7f07cad7c63232fbd76a473e97286cc6db19702   https://d14.api.augmentcode.com/  2025-08-17T02:29:10Z
2025-08-10 10:29:27 - INFO - email:<EMAIL> === cookie:.eJxNjcEOwiAQRP9lz60BdlnAk3_SYFkMiaCp1sSo_y5pPHicybw3L5iustTYpN1hf19WGSDHWs7PqcUqsAcY4FQe0v5ySddpvckyldQLqbGc3-yDV5hESEyikPIR2XJ2fd4ube4kezZKGbKITpPS1tkBNs1m6KZHTZdVO0suGB_CwbL15HZzTvBbboeMUYxGHEXNOBIZHo8a55Gd7WgknYOCzxcccT_F.aJgEBg.C881I-WWOWUN4vX75pnoft_-cK0
2025-08-10 10:29:27 - INFO - 
自动化流程成功完成！
2025-08-10 10:29:27 - INFO - 添加第5个
