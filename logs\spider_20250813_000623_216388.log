2025-08-13 00:06:31 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60087,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:06:31 - INFO - <EMAIL>
2025-08-13 00:06:31 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:06:48 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:06:52 - INFO - 找到 Turnstile...
2025-08-13 00:06:54 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:06:58 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:06:58 - INFO - CDP:启用节流模式
2025-08-13 00:07:07 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:07:07 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:07:07 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:07:07 - INFO - ReCAPTCHA Token: 
2025-08-13 00:07:07 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:07:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:07:21 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:07:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:07:21 - INFO - 成功！当前在订阅页面。
2025-08-13 00:07:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:07:21 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:07:24 - INFO - 代码信息: {
  "codeVerifier": "nGuGzKZ1Z58IxeR41-290F8nJH3XSark9yTp4hcEjDY",
  "code_challenge": "zrZG29vUsaJtkHV7QnlA6moUOamKZzCuafusxgNRuF0",
  "state": "231c6a29-8f8f-4aa6-93ec-6e35bb43a58b"
}
2025-08-13 00:07:24 - INFO - ==================================================
2025-08-13 00:07:24 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_df27baa7096409f596e0c261ca89d8d2&state=231c6a29-8f8f-4aa6-93ec-6e35bb43a58b&tenant_url=https%3A%2F%2Fd15.api.augmentcode.com%2F
2025-08-13 00:07:27 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:07:27 - INFO - 添加session: <EMAIL>   db2663be5a137d3ace9924259989225393837b554fcba01cfcf25d4f75632378   https://d15.api.augmentcode.com/  2025-08-19T16:07:13Z
2025-08-13 00:07:27 - INFO - email:<EMAIL> === cookie:.eJxNjktuwzAQQ-8yazuwfqNRVr2JIWvGgRBLDhSnQNH07hWMLrokQT7yG-aHtBKr1AOuR3vJAGssefuaaywCV4ABbvlT6j-d-TG_ntLmzN2QEvP2RgoLYmBrFNOqjWeLvGrq8brX1JvaKh900GgoTKSnYMwAJ-YkdFJrcVuUd25S1pP5eJbYDt7TXdplr1uuAn-Nc9hhwEjIo0vGjXahdewX1Jgo8eTR6qA8_PwCk31EUw.aJtmvA.xN2g5CAq9JkmQonUhi39tX2Acg0
2025-08-13 00:07:27 - INFO - 
自动化流程成功完成！
2025-08-13 00:07:27 - INFO - 添加第1个
2025-08-13 00:07:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60355,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:07:44 - INFO - <EMAIL>
2025-08-13 00:07:44 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:08:08 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:08:13 - INFO - 找到 Turnstile...
2025-08-13 00:08:14 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:08:18 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:08:19 - INFO - CDP:启用节流模式
2025-08-13 00:08:27 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:08:27 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:08:27 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:08:27 - INFO - ReCAPTCHA Token: 
2025-08-13 00:08:27 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:08:50 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:08:50 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:08:50 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:08:50 - INFO - 成功！当前在订阅页面。
2025-08-13 00:08:50 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:08:50 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:08:53 - INFO - 代码信息: {
  "codeVerifier": "9bzodIhldwr7-ioMqjuxjyydARWyVurMrwqeQP5lFyA",
  "code_challenge": "zlb_lhUQUyOJ31PtPSADNlQqrTu3fAljNtTaTkv295g",
  "state": "f62d37a1-4e2e-4acd-8e6e-37fc9c2a81f0"
}
2025-08-13 00:08:53 - INFO - ==================================================
2025-08-13 00:08:54 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_0cc0b565c8c1202acd01272e9463715a&state=f62d37a1-4e2e-4acd-8e6e-37fc9c2a81f0&tenant_url=https%3A%2F%2Fd20.api.augmentcode.com%2F
2025-08-13 00:08:56 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:08:56 - INFO - 添加session: <EMAIL>   0c715d2131284f76c46020f751ec02d3af92e08a080f88c932f36ce45f55afae   https://d20.api.augmentcode.com/  2025-08-19T16:08:31Z
2025-08-13 00:08:56 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mBah75c-SdNoVPTSAdTwMTXv0uIC5f35p5zX-BvVEtg4gVOS12pgRRKHh-eQyE4ATRwyXfiv5zjza8zVZ_jVlAJeXxr63qtKXYoo01HNFGhcVJsc5542EippbaIHTqDVjqNTjWwa3bDZrryM7E0SgnZWWXOcwl1idNwpXqYeMxM8CP245isQCFCi1rbtsNj37qBqHW9SClgTFI4-HwBh4VE3w.aJtnFg.z70Wd11Q_4-FVNRhnzcwxJVzRLY
2025-08-13 00:08:56 - INFO - 
自动化流程成功完成！
2025-08-13 00:08:56 - INFO - 添加第2个
2025-08-13 00:09:17 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60502,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:09:17 - INFO - <EMAIL>
2025-08-13 00:09:17 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:09:33 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:09:37 - INFO - 找到 Turnstile...
2025-08-13 00:09:39 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:09:43 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:09:43 - INFO - CDP:启用节流模式
2025-08-13 00:09:43 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:09:50 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:09:50 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:09:50 - INFO - ReCAPTCHA Token: 
2025-08-13 00:09:50 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:10:00 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:10:00 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:10:00 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:10:00 - INFO - 成功！当前在订阅页面。
2025-08-13 00:10:00 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:10:00 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:10:03 - INFO - 代码信息: {
  "codeVerifier": "O2j6HWBgwG7AwNPXgWoOQXc9wSj7cI9RAT622vncvJg",
  "code_challenge": "XV_2BFOo_5P9Ln5JpuFQ3DmfGqSfo_bUFFSH4rtugas",
  "state": "1b77a759-09b1-42aa-8cce-9ae5c59ef53b"
}
2025-08-13 00:10:03 - INFO - ==================================================
2025-08-13 00:10:04 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_eac1de992cd35614d20567032cfae410&state=1b77a759-09b1-42aa-8cce-9ae5c59ef53b&tenant_url=https%3A%2F%2Fd18.api.augmentcode.com%2F
2025-08-13 00:10:06 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:10:06 - INFO - 添加session: <EMAIL>   5e428fdf0bcbc5497e82d047e8d606ba4a045b057c27e71f7b3e35526da56b5c   https://d18.api.augmentcode.com/  2025-08-19T16:09:54Z
2025-08-13 00:10:06 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAURP_lrsHcvgsr_4TU9qKNtJACJkb9dxviwuVM5px5wbBQSS5T3qDfyk4NjC7F6Tlklwh6gAau8UH5L8ewDPtKZYihFpRcnN7adhdtJJOCBTtyYYJGrwWv8zxnX0krmDEKrRWWdaiMQNnAoTkM1XRLiXZmlEImO4XnNbmyhdnfqZzmPMVM8COO41F6Vo9c2zmOrVQBW8uJWpIWR81d8NrA5wtb7ERd.aJtnWw.jRntLHUh3Nd4aBD5LzXwu7sNaWw
2025-08-13 00:10:06 - INFO - 
自动化流程成功完成！
2025-08-13 00:10:06 - INFO - 添加第3个
2025-08-13 00:10:29 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60675,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:10:29 - INFO - <EMAIL>
2025-08-13 00:10:29 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:10:45 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:10:49 - INFO - 找到 Turnstile...
2025-08-13 00:10:51 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:10:55 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:10:55 - INFO - CDP:启用节流模式
2025-08-13 00:10:55 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:11:03 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:11:03 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:11:03 - INFO - ReCAPTCHA Token: 
2025-08-13 00:11:03 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:11:14 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:11:14 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:11:14 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:11:14 - INFO - 成功！当前在订阅页面。
2025-08-13 00:11:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:11:14 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:11:17 - INFO - 代码信息: {
  "codeVerifier": "Yf8x81j6MF1xXn8rmkq6IMxZYMfBCFQkAGLJ3wZjl5Q",
  "code_challenge": "ovvmfo7rWD2elC3rVH3mkn8KQPBb9DLzpe8CraINYiI",
  "state": "15a5d5b9-c99d-471a-9bae-8f4dbe02696c"
}
2025-08-13 00:11:17 - INFO - ==================================================
2025-08-13 00:11:18 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_fa3145050bbd4223a1eec313530e3512&state=15a5d5b9-c99d-471a-9bae-8f4dbe02696c&tenant_url=https%3A%2F%2Fd20.api.augmentcode.com%2F
2025-08-13 00:11:20 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:11:20 - INFO - 添加session: <EMAIL>   8ef09c89161eaa27578a287cc044e04295e2cb8160bde1e31d479d8ca49e0fd4   https://d20.api.augmentcode.com/  2025-08-19T16:11:07Z
2025-08-13 00:11:20 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgjAQRP9lz2Cg7XYXT_4JKe1qGmnRgkaj_ruEePA4k3lvXtBfpCSXJS-wX8pNKji6FMdnn10S2ANUcIp3yX85hkt_m6X0MayFJBfHt-VusMTO6DbwUWkK1qOgWed5yn4l2WhmJENtR2S0JWUr2DSbYTWdr8Pj0RJi02Kj1GFOrixh8mcpuymPMQv8iO2YO8vola7RoK4NK1tzM3CNngINjThRCj5fjPlENw.aJtnpQ._HBO12NAE41RrEadCgh-p4LHssE
2025-08-13 00:11:20 - INFO - 
自动化流程成功完成！
2025-08-13 00:11:20 - INFO - 添加第4个
2025-08-13 00:11:41 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60835,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:11:42 - INFO - <EMAIL>
2025-08-13 00:11:42 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:11:58 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:12:02 - INFO - 找到 Turnstile...
2025-08-13 00:12:04 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:12:08 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:12:08 - INFO - CDP:启用节流模式
2025-08-13 00:12:08 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:12:08 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:12:17 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:12:17 - INFO - ReCAPTCHA Token: 
2025-08-13 00:12:17 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:12:35 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:12:35 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:12:35 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:12:35 - INFO - 成功！当前在订阅页面。
2025-08-13 00:12:35 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:12:35 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:12:38 - INFO - 代码信息: {
  "codeVerifier": "LPJEDl_tFIpRA-owxQsE2QBREvxy0gFlUcDGVeaihV0",
  "code_challenge": "IwAXfQmtn5m422yVt7m1E1BaXXcNnAyUTFZmkvuFrmE",
  "state": "7c5f5fda-f9ee-46c7-b23f-d83a16bc9e2a"
}
2025-08-13 00:12:38 - INFO - ==================================================
2025-08-13 00:12:39 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_f8a17c21c427adfbe90220e8cf3ec65c&state=7c5f5fda-f9ee-46c7-b23f-d83a16bc9e2a&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-13 00:12:40 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:12:40 - INFO - 添加session: <EMAIL>   41e68bf0e4a314b6f62cc8a3809cae065a59e15efd2aaaca4d7eaab9984077c2   https://d10.api.augmentcode.com/  2025-08-19T16:12:22Z
2025-08-13 00:12:40 - INFO - email:<EMAIL> === cookie:.eJxNjsFuwyAQRP9lz3ZlYIF1TvkTC8NSoZh1RJxKVdJ_L7J66HFGM2_mBcudWw3CcsDlaE8eIIdatu9FQmW4AAzwWb5Y_umS7svzwW0pqRtcQ9nejubV-WTQqERZG588GcO2x2WX2JtGK5oNovNaaXKIZAc4MSehk3bJt1V5aydlp9leHzW0I-3xxu1jl60Iw1_jHEaipDHhaJ2NI8boR9Lkx0x5XqdoYv8EP791Y0RV.aJtn9g.Oqea70JTjJBNzyWvHjTnejvcv3U
2025-08-13 00:12:40 - INFO - 
自动化流程成功完成！
2025-08-13 00:12:40 - INFO - 添加第5个
2025-08-13 00:12:59 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60996,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:13:00 - INFO - <EMAIL>
2025-08-13 00:13:00 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:13:15 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:13:19 - INFO - 找到 Turnstile...
2025-08-13 00:13:19 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:13:23 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:13:23 - INFO - CDP:启用节流模式
2025-08-13 00:13:23 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:13:31 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:13:31 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:13:31 - INFO - ReCAPTCHA Token: 
2025-08-13 00:13:31 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:14:01 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-13 00:14:01 - INFO - 鼠标左键放开操作完成。
2025-08-13 00:14:02 - INFO - 鼠标左键点击页面操作完成。
2025-08-13 00:14:02 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-13 00:14:05 - INFO - 验证成功，已进入目标页面。
2025-08-13 00:14:05 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:14:05 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:14:05 - INFO - 成功！当前在订阅页面。
2025-08-13 00:14:05 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:14:05 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:14:08 - INFO - 代码信息: {
  "codeVerifier": "pjOJJIKWAK1sIi6SuoCCbTzQM5Ji0JxqLJXJZmQjhic",
  "code_challenge": "Ky1wscG6ANkHAHVNYYeKjADBbjB0TJ85lvwnR5gtCRQ",
  "state": "a40d4412-96c0-46bf-941b-64de2b172715"
}
2025-08-13 00:14:08 - INFO - ==================================================
2025-08-13 00:14:09 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_1feb60743485fd01af9a9a8aefccf224&state=a40d4412-96c0-46bf-941b-64de2b172715&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-13 00:14:12 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:14:12 - INFO - 添加session: <EMAIL>   eb0215b3262c911e0fd3b75bb3dc668357dcf3982666e6105aa077d48648a0d1   https://d8.api.augmentcode.com/  2025-08-19T16:13:35Z
2025-08-13 00:14:12 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtV2I1odMVrmJIYtMKtSSDcUpULS9ewwjiyxnMO_NL4yrthKr1g3OW3toB9dY8vwz1lgUzgAd3PK31recZR0fd21jlr3QEvP8F_g0BR6Msyh8HSwJu0ge93ldatrJwAFPlqxDS854w9zBYTkEu-hTllWQvDfokfByL7FtsqQvbR9LnXNVeBHHrw9DMoK-J06hdyrUR6bQU5BpSokMpQj_Tx16RFk.aJtoUA.4wJIViwy_M4pHFEtdudBf92RCOI
2025-08-13 00:14:12 - INFO - 
自动化流程成功完成！
2025-08-13 00:14:12 - INFO - 添加第6个
2025-08-13 00:14:30 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61164,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:14:30 - INFO - <EMAIL>
2025-08-13 00:14:30 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:14:46 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:14:50 - INFO - 找到 Turnstile...
2025-08-13 00:14:52 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:14:56 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:14:56 - INFO - CDP:启用节流模式
2025-08-13 00:15:04 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:15:04 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:15:04 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:15:04 - INFO - ReCAPTCHA Token: 
2025-08-13 00:15:04 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:15:35 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-13 00:15:35 - INFO - 鼠标左键放开操作完成。
2025-08-13 00:15:35 - INFO - 鼠标左键点击页面操作完成。
2025-08-13 00:15:35 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-13 00:15:38 - INFO - 验证成功，已进入目标页面。
2025-08-13 00:15:38 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:15:38 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:15:38 - INFO - 成功！当前在订阅页面。
2025-08-13 00:15:38 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:15:38 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:15:41 - INFO - 代码信息: {
  "codeVerifier": "tRSmxV_cC2kka3OYEIVIaN2tkx1jCt2amoMz7l8DUVg",
  "code_challenge": "eIiudx_cGtP4VysiNDmRuLw3vns1T7Rt_7RFK5wL-4k",
  "state": "9ef949d6-eef1-4ce0-afa2-ad684d8e9ce7"
}
2025-08-13 00:15:41 - INFO - ==================================================
2025-08-13 00:15:42 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_71606376490d306dbe806921be46bc89&state=9ef949d6-eef1-4ce0-afa2-ad684d8e9ce7&tenant_url=https%3A%2F%2Fd16.api.augmentcode.com%2F
2025-08-13 00:15:44 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:15:44 - INFO - 添加session: <EMAIL>   40e8475f694a46cbf38fcec5da4a44214a7303b1e6c955aed911d6f7a65f2de6   https://d16.api.augmentcode.com/  2025-08-19T16:15:11Z
2025-08-13 00:15:44 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3YENixLTvkTC8NSoRgcYTuS2_Tfi9IeqjnNaObNF0wPrtkVLjtc93pwB9HltJxTcZnhCtDBR3py-edTeEzHxnVKoQWcXVpeSHZGMrMaZaA4jCbYERW6Vi9r8W2piJREQ0IYjU0WqYM35k1opPPz7r00WgupB1S3Lbu6h9XfuV7WsqTC8Lf4PZ6VJWTZD9KJXkUbe-sp9oJDVFpy8EbD9w-QJ0Um.aJtorg.5re6u2DeEhz2pN2TLtzQh7w-R-o
2025-08-13 00:15:44 - INFO - 
自动化流程成功完成！
2025-08-13 00:15:44 - INFO - 添加第7个
2025-08-13 00:16:07 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61331,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:16:08 - INFO - <EMAIL>
2025-08-13 00:16:08 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:16:24 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:16:27 - INFO - 找到 Turnstile...
2025-08-13 00:16:29 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:16:33 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:16:33 - INFO - CDP:启用节流模式
2025-08-13 00:16:33 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:16:33 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:16:40 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:16:40 - INFO - ReCAPTCHA Token: 
2025-08-13 00:16:40 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:17:11 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-13 00:17:11 - INFO - 鼠标左键放开操作完成。
2025-08-13 00:17:11 - INFO - 鼠标左键点击页面操作完成。
2025-08-13 00:17:11 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:17:11 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:17:11 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:17:11 - INFO - 成功！当前在订阅页面。
2025-08-13 00:17:11 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:17:11 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:17:14 - INFO - 代码信息: {
  "codeVerifier": "k4pB0odx6edOt5T_P8DvRO3gY13FyMpVG9JCO94Jgr8",
  "code_challenge": "Z1GxomWjVbncb7a9AWoxVq_ewqNq2FoTrfdS9_hSQRA",
  "state": "0fa7a210-ec54-404c-b9ee-a7a9e8f73089"
}
2025-08-13 00:17:14 - INFO - ==================================================
2025-08-13 00:17:15 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_be7825b2a271fe95e7aa10ed8c745c1c&state=0fa7a210-ec54-404c-b9ee-a7a9e8f73089&tenant_url=https%3A%2F%2Fd2.api.augmentcode.com%2F
2025-08-13 00:17:17 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:17:17 - INFO - 添加session: <EMAIL>   5f1e82c5d617cfe447f8b15744d50c1434f09e47f3583f699a5612b241376484   https://d2.api.augmentcode.com/  2025-08-19T16:16:46Z
2025-08-13 00:17:17 - INFO - email:<EMAIL> === cookie:.eJxNjc1OxDAQg99lzi1qfifZE29STZMJimimq2wXCQHvTlRx4GZb9ucvWO_cGwnLCbezP3mCQq3un6tQY7gBTPBWP1j--Zrv6_PBfa15BNyo7t8-xM2HnKxRORRtMJPRCv2oyyFpLK23i0WjtRoCo_IOJ7gwF2GQ5MidFDq3KGe8fn006mc-0jv3l0P2Kgx_i-vYWQw2Bp5Tijhbu20zJea5bKgicSxlYfj5BZaURYw.aJtpCw.osen1I8-qYOFj_eupmid5-Uk80E
2025-08-13 00:17:17 - INFO - 
自动化流程成功完成！
2025-08-13 00:17:17 - INFO - 添加第8个
2025-08-13 00:17:35 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61593,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:17:35 - INFO - <EMAIL>
2025-08-13 00:17:35 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:17:56 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:18:00 - INFO - 找到 Turnstile...
2025-08-13 00:18:02 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:18:06 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:18:06 - INFO - CDP:启用节流模式
2025-08-13 00:18:15 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:18:15 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:18:15 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:18:15 - INFO - ReCAPTCHA Token: 
2025-08-13 00:18:15 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:18:57 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-13 00:18:57 - INFO - 鼠标左键放开操作完成。
2025-08-13 00:18:58 - INFO - 鼠标左键点击页面操作完成。
2025-08-13 00:18:58 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:18:58 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:18:58 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:18:58 - INFO - 成功！当前在订阅页面。
2025-08-13 00:18:58 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:18:58 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:19:01 - INFO - 代码信息: {
  "codeVerifier": "RrP9P-7Fk4AxmzyTMF92D_lKc9_85KhlFQ0MT3lKVhg",
  "code_challenge": "egwbn4KqxQbpOf3UrGvTCMcLCwRr7iJiMDpjFcyREJQ",
  "state": "41f10e47-89a1-47be-8f61-0d6d6e6edc31"
}
2025-08-13 00:19:01 - INFO - ==================================================
2025-08-13 00:19:01 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_57bb61e357f4307f6b855498517abe0c&state=41f10e47-89a1-47be-8f61-0d6d6e6edc31&tenant_url=https%3A%2F%2Fd17.api.augmentcode.com%2F
2025-08-13 00:19:03 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:19:03 - INFO - 添加session: <EMAIL>   729b8565d8a7ae2600dc2fdffe205b731a382c3a05bf325d63b13b4b1634dce9   https://d17.api.augmentcode.com/  2025-08-19T16:18:20Z
2025-08-13 00:19:03 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAQQ-8ya7vQyJKcyao3MfQZFUKtcaI4RdLP3SsYXXRHEuTjFywXbtULyw7nvd15gOxrWZ-L-MpwBhjgrXyw_PMlXZb7jdtSUg-4-rJ-uxMFRxOZCdMp62lOoYuYe102iX1JRpE2qMk5tKRQqQEOygHooOtnfgjO1iq0xtDrrfq2py2-c3vZZC3C8Lc4ftHpqMnjaI21o8E5jx5zHmfLIaYQVPQEP79x7UUx.aJtpdQ.KgO0NOw2Ui0L0IMdkrHDGO-1Y7U
2025-08-13 00:19:03 - INFO - 
自动化流程成功完成！
2025-08-13 00:19:03 - INFO - 添加第9个
2025-08-13 00:19:24 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62097,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:19:24 - INFO - <EMAIL>
2025-08-13 00:19:24 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:20:05 - INFO - 获取验证码失败，重试次数: 1
2025-08-13 00:20:15 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:20:19 - INFO - 找到 Turnstile...
2025-08-13 00:20:20 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:20:25 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:20:25 - INFO - CDP:启用节流模式
2025-08-13 00:20:25 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:20:25 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:20:33 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:20:33 - INFO - ReCAPTCHA Token: 
2025-08-13 00:20:33 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:20:33 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:20:33 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:20:33 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:20:43 - INFO -    等待 3.83 秒...
2025-08-13 00:20:51 - INFO - 刷新完成，继续下一次尝试。
2025-08-13 00:20:51 - INFO - 
第 2/5 次尝试注册...
2025-08-13 00:20:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:20:51 - INFO - 成功！当前在订阅页面。
2025-08-13 00:20:51 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:20:51 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:20:54 - INFO - 代码信息: {
  "codeVerifier": "rX4U9JSmHEAo1KdCztr5B3uEEFvRD3GK-bxQvhJ_iCQ",
  "code_challenge": "biA0Qibty7_uM9ycYas4fNszOPoV9aMoq8tg_HMHt4c",
  "state": "9356d006-c64f-4f56-bc65-1d217a3d8a2f"
}
2025-08-13 00:20:54 - INFO - ==================================================
2025-08-13 00:20:55 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_edc7bf180872faf793cd51c6d9bb37f3&state=9356d006-c64f-4f56-bc65-1d217a3d8a2f&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-13 00:20:56 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:20:56 - INFO - 添加session: <EMAIL>   8f9568763c8f2cb52d09723e3130c57261a50e2622da838a3a01707424bc99de   https://d12.api.augmentcode.com/  2025-08-19T16:20:38Z
2025-08-13 00:20:56 - INFO - email:<EMAIL> === cookie:.eJxNjUuOwjAQRO_S62RE0_50s5qbRE7cRhaxAyYgjWbm7kQRC5ZVqvfqF4arthKq1hVOa3toBymUPP8MNRSFE0AH5_zU-pFzvA6Pu7Yhx63QEvL851hGJxMZwsjpSD5OjAlxm9elThuJRMSWjJA9kEc2Ih3smt2wmdItX57orT2gtZa_7yW0NS7TRdvXUudcFd7EfhxHTnL01DMh9cYi95Kc9OiCJD9G9MbB_wuMGkQt.aJtp5g.iHYqKzn_-LJx2vs2y7i7C6pLV7A
2025-08-13 00:20:56 - INFO - 
自动化流程成功完成！
2025-08-13 00:20:56 - INFO - 添加第10个
2025-08-13 00:21:21 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62267,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:21:21 - INFO - <EMAIL>
2025-08-13 00:21:21 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:21:42 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:21:46 - INFO - 找到 Turnstile...
2025-08-13 00:21:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:21:51 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:21:51 - INFO - CDP:启用节流模式
2025-08-13 00:22:00 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:22:00 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:22:00 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:22:00 - INFO - ReCAPTCHA Token: 
2025-08-13 00:22:00 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:22:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:22:22 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:22:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:22:22 - INFO - 成功！当前在订阅页面。
2025-08-13 00:22:22 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:22:22 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:22:25 - INFO - 代码信息: {
  "codeVerifier": "SDaW4QW-cLqJkl5BssVaEXCzCyo4XH7SElLp6lYqkyI",
  "code_challenge": "frhIIszVDHfYpYVwdVCgYzwbOqH2K8uSpEVjXAkhOfQ",
  "state": "ed267113-1435-4f24-87b2-1b04d9d5bc38"
}
2025-08-13 00:22:25 - INFO - ==================================================
2025-08-13 00:22:26 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_94bb9633cd7a36dec2e6967bfd441bbb&state=ed267113-1435-4f24-87b2-1b04d9d5bc38&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-13 00:22:29 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:22:29 - INFO - 添加session: <EMAIL>   8c89309f925208e7d736307adfe3d2f3f27195c801cab6c8d1fa1b3de641a932   https://d5.api.augmentcode.com/  2025-08-19T16:22:05Z
2025-08-13 00:22:29 - INFO - email:<EMAIL> === cookie:.eJxNjUuOgzAQRO_SaxjZ-NeTVW5iGXcTWYMbZEikKJm7D0KzyLJK9V69IK7cahKWHS57u3MHU6plfkZJleEC0MGtPFg-cqE13jdusdBRcE1lfnv8Hn3SyRpNOA0mEDkOZjjmskg-SKO8dehQWwwaB63QdHBqTsNhmtcimw7OKe18cNetprbTkn-4fS0yF2H4J85jnDIFn7GnrLC3SuceDY39SEH5NLIim-H3D70vRbM.aJtqQg.CxVt1KQueTgYSELr_3xp2r0FocA
2025-08-13 00:22:29 - INFO - 
自动化流程成功完成！
2025-08-13 00:22:29 - INFO - 添加第11个
2025-08-13 00:22:48 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62435,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:22:48 - INFO - <EMAIL>
2025-08-13 00:22:48 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:23:06 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:23:10 - INFO - 找到 Turnstile...
2025-08-13 00:23:11 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:23:15 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:23:15 - INFO - CDP:启用节流模式
2025-08-13 00:23:23 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:23:23 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:23:23 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:23:23 - INFO - ReCAPTCHA Token: 
2025-08-13 00:23:23 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:23:35 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:23:35 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:23:35 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:23:35 - INFO - 成功！当前在订阅页面。
2025-08-13 00:23:35 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:23:35 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:23:38 - INFO - 代码信息: {
  "codeVerifier": "2-BFIA-vwYCu50Jhb0rXkJQ1sYfGUlBuBC8nvlUd5Rw",
  "code_challenge": "2JEG6K4_N9hF2d-6h_JX7pGBObffZuY2S0R1exuCbh8",
  "state": "ed989f61-9de0-465a-9e47-7661349de698"
}
2025-08-13 00:23:38 - INFO - ==================================================
2025-08-13 00:23:39 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_1350aa8ea6994b377844e33025895e0f&state=ed989f61-9de0-465a-9e47-7661349de698&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-13 00:23:41 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:23:41 - INFO - 添加session: <EMAIL>   bcb77d521564e9bce194834766bc2ab2a25f1475a14ad0755dafca132cb24f49   https://d12.api.augmentcode.com/  2025-08-19T16:23:27Z
2025-08-13 00:23:41 - INFO - email:<EMAIL> === cookie:.eJxNjUGOwjAMRe_idYuapHESVtykMo07iiZxUVqQEHB3IpjFSN7Y_7_nB0wXroWEZYfjXq_cwUIl5fskVBiOAB38pBvLvz3Fy3TduE4ptgMXSvmJPpyRkEejol-0cZGNG2ff6rLK3EgfBtMGLWqFLthBYQcfzcfQTFmWnJWzLbIO9WkrVPe4zr9cD6vkJAx_xPfx2SsfF9vjbEM_ktZ90BR6DN4OWo2GTIDXG68zREc.aJtqiw.a4jbneoXp6H3jvCrnB2zRR1U1ok
2025-08-13 00:23:41 - INFO - 
自动化流程成功完成！
2025-08-13 00:23:41 - INFO - 添加第12个
2025-08-13 00:24:06 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62599,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:24:07 - INFO - <EMAIL>
2025-08-13 00:24:07 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:24:25 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:24:29 - INFO - 找到 Turnstile...
2025-08-13 00:24:30 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:24:34 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:24:34 - INFO - CDP:启用节流模式
2025-08-13 00:24:34 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:24:34 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:24:42 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:24:42 - INFO - ReCAPTCHA Token: 
2025-08-13 00:24:42 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:24:51 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:24:51 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:24:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:24:51 - INFO - 成功！当前在订阅页面。
2025-08-13 00:24:51 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:24:51 - INFO - 未检测到验证页面，跳过操作。
2025-08-13 00:24:54 - INFO - 代码信息: {
  "codeVerifier": "hSM0ZF4kyxYaaDE2Hkqoo_rThwSbuBVeNl8XkpbNTD0",
  "code_challenge": "FQzcSbQepiYTKUOWMtjULB6LxHWBuucQiHtqIS52Yqc",
  "state": "b0fcd0d7-1ccc-4a8e-8771-10911ef85519"
}
2025-08-13 00:24:54 - INFO - ==================================================
2025-08-13 00:24:55 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_a54ea1f4784893c04ec840feccc59d3a&state=b0fcd0d7-1ccc-4a8e-8771-10911ef85519&tenant_url=https%3A%2F%2Fd7.api.augmentcode.com%2F
2025-08-13 00:24:57 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:24:57 - INFO - 添加session: <EMAIL>   6f9b297f2793baabbaf1cfd68c5db92f0ae6f8f2093df3a5d4a2b53441ce9117   https://d7.api.augmentcode.com/  2025-08-19T16:24:48Z
2025-08-13 00:24:57 - INFO - email:<EMAIL> === cookie:.eJxNjUsOwjAQQ-8y6xblM_nAiptUSWcKEU2KQkFCwN2JKhbsbMt-fsFw5ZpD4bLCYa137mAKOc3PoYTMcADo4JQeXP58outwv3EdErWAc0jz2_p9tCESakl-UtoRTxGla_WylLEtvdfGaYfOCGuUtlqYDjbMRmikeq7nVTpjhDQexfGWQ11pGS9cd0uZU2H4LbbjaQxsncfeouAePVIfo1K9jGgtYZOk4PMF7BxFVg.aJtq1w.GCSV-MTNYJO5EmsPO6igqH5ysCo
2025-08-13 00:24:57 - INFO - 
自动化流程成功完成！
2025-08-13 00:24:57 - INFO - 添加第13个
2025-08-13 00:25:21 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62749,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:25:21 - INFO - <EMAIL>
2025-08-13 00:25:21 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:25:41 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:25:45 - INFO - 找到 Turnstile...
2025-08-13 00:25:45 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:25:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:25:50 - INFO - CDP:启用节流模式
2025-08-13 00:25:57 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:25:57 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:25:57 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:25:57 - INFO - ReCAPTCHA Token: 
2025-08-13 00:25:57 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:28:05 - INFO - 执行鼠标操作时发生错误: HTTPConnectionPool(host='localhost', port=62756): Read timed out. (read timeout=120)
2025-08-13 00:28:05 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:28:05 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:29:29 - INFO - 在尝试注册时发生未知错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-13 00:29:29 - ERROR - 在尝试注册时发生未知错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-13 00:29:29 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
