2025-08-11 13:43:19 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54834,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 13:43:19 - INFO - <EMAIL>
2025-08-11 13:43:19 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 13:43:35 - INFO - 获取 cap_value 验证码成功...
2025-08-11 13:43:39 - INFO - 找到 Turnstile...
2025-08-11 13:43:41 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 13:43:41 - INFO - 已执行拟人滚动: -132px
2025-08-11 13:43:41 - INFO - 已执行页面焦点操作
2025-08-11 13:43:42 - INFO - 已执行拟人操作完成，总停顿: 0.96秒
2025-08-11 13:43:43 - INFO - 登录后已执行更多拟人操作
2025-08-11 13:43:46 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 13:43:55 - INFO - 验证码已提交，等待跳转...
2025-08-11 13:43:55 - INFO - 已执行拟人滚动: 95px
2025-08-11 13:44:02 - INFO - 已执行页面焦点操作
2025-08-11 13:44:03 - INFO - 已执行拟人操作完成，总停顿: 1.43秒
2025-08-11 13:44:03 - INFO - 
第 1/5 次尝试注册...
2025-08-11 13:44:03 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 13:44:13 - INFO - 已执行拟人滚动: 66px
2025-08-11 13:44:13 - INFO - 已执行页面焦点操作
2025-08-11 13:44:14 - INFO - 已执行拟人操作完成，总停顿: 1.10秒
2025-08-11 13:44:14 - INFO - 成功！当前在订阅页面。
2025-08-11 13:44:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 13:44:19 - INFO - 代码信息: {
  "codeVerifier": "M6CXb2YMosCtKh-NSZAkL3skR8CXIc17kM8y_hBaXPU",
  "code_challenge": "iicEOSMwdGCQOG6ok3J_5sXu3SOC9UasUNhaKTsuMdg",
  "state": "e9c776bf-c019-427e-852b-0d860fe09607"
}
2025-08-11 13:44:19 - INFO - ==================================================
2025-08-11 13:44:19 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_fcfc98d0c5f52d324cff533af9964632&state=e9c776bf-c019-427e-852b-0d860fe09607&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-11 13:44:22 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 13:44:22 - INFO - 添加session: <EMAIL>   1d47f06b6ef0bdd70f1e0805dcab4080cc751c9c648f3398b4f6015383eb011f   https://d13.api.augmentcode.com/  2025-08-18T05:44:01Z
2025-08-11 13:44:22 - INFO - email:<EMAIL> === cookie:.eJxNzUEOgyAQBdC7zFobhEHAVW9iUIaGVNAgmjRt717rpl3Oz_9vntAvlKNNlAp0JW9UgbcxTI8-2UjQAVRwCzulvzu4pd9Wyn1wR0DRhunVamO0YA5F47TnQjnVSpR01NOcxmPJmZTMoGparphohTZYwcmcwiHNW9ldoyRqw4wR1zXaXNw83ilf5jSF9NV-jwdP3CL5uhGearQjrwfSWHMzeCk5QzcIeH8Ah-dFSg.aJmDNQ.KW-7wy7A2hZIgYnl8VNAerjKTuY
2025-08-11 13:44:22 - INFO - 
自动化流程成功完成！
2025-08-11 13:44:22 - INFO - 添加第1个
2025-08-11 13:44:43 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55022,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 13:44:43 - INFO - <EMAIL>
2025-08-11 13:44:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 13:44:53 - INFO - 获取 cap_value 验证码成功...
2025-08-11 13:44:57 - INFO - 找到 Turnstile...
2025-08-11 13:44:57 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 13:44:57 - INFO - 已执行拟人滚动: 69px
2025-08-11 13:44:59 - INFO - 已执行页面焦点操作
2025-08-11 13:44:59 - INFO - 已执行拟人操作完成，总停顿: 0.90秒
2025-08-11 13:45:00 - INFO - 登录后已执行更多拟人操作
2025-08-11 13:45:04 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 13:45:13 - INFO - 验证码已提交，等待跳转...
2025-08-11 13:45:13 - INFO - 已执行拟人滚动: -22px
2025-08-11 13:45:13 - INFO - 已执行页面焦点操作
2025-08-11 13:45:13 - INFO - 已执行拟人操作完成，总停顿: 0.89秒
2025-08-11 13:45:20 - INFO - 
第 1/5 次尝试注册...
2025-08-11 13:45:20 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 13:45:30 - INFO - 已执行拟人滚动: 56px
2025-08-11 13:45:31 - INFO - 已执行拟人操作完成，总停顿: 0.96秒
2025-08-11 13:45:31 - INFO - 成功！当前在订阅页面。
2025-08-11 13:45:31 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 13:45:36 - INFO - 代码信息: {
  "codeVerifier": "Zy6LovTty6MvXRSz_BaINfsiD62E-AeNvF2QjxlQFIY",
  "code_challenge": "srSFBiDm5rWR5DyhCeHA7rH_FsZ5RjrUZRu8JFt8OEg",
  "state": "869edccb-b4c8-4619-9910-ec8fd95a23e5"
}
2025-08-11 13:45:36 - INFO - ==================================================
2025-08-11 13:45:37 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_2a0551910b2690b9a69383ceaf836cfe&state=869edccb-b4c8-4619-9910-ec8fd95a23e5&tenant_url=https%3A%2F%2Fd16.api.augmentcode.com%2F
2025-08-11 13:45:38 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 13:45:38 - INFO - 添加session: <EMAIL>   455e4989acb9b56c3fe79976348ce7fd093d4f66c155a45acce79aa0438942b9   https://d16.api.augmentcode.com/  2025-08-18T05:45:19Z
2025-08-11 13:45:38 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtV2I-kRSVrmJIYtUIMSiA8VpUbS5ewyjiy5nMO_ND0x37i0JywbnrT95gJJaXb4nSY3hDDDAtX6y_MuV7tPzwX2qtBfcUl1-TyHGYNxsDVIo2njys81z3ueySt7J4GLQWpmAymlvg_J6gENzGHYTfW2yoHc2RFTeXx4t9Y3WfOP-scpSheGPOI6L1hlV0iOeKI62GByjVWV0RSFhppKQ4PUGrJVFJg.aJmDgg.LVGXOBB8Ootl2qCs8Y6_5xpXP0Y
2025-08-11 13:45:38 - INFO - 
自动化流程成功完成！
2025-08-11 13:45:38 - INFO - 添加第2个
2025-08-11 13:45:59 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55202,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 13:45:59 - INFO - <EMAIL>
2025-08-11 13:45:59 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 13:47:26 - INFO - Traceback (most recent call last):

2025-08-11 13:47:26 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-11 13:47:26 - INFO - [1;35mConnectionResetError[0m: [35m[WinError 10054] 远程主机强迫关闭了一个现有的连接。[0m

2025-08-11 13:47:26 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-11 13:47:26 - INFO - Traceback (most recent call last):

2025-08-11 13:47:26 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1043[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1000[0m, in [35mmain[0m
    if not [31mlogin[0m[1;31m(driver, email)[0m:
           [31m~~~~~[0m[1;31m^^^^^^^^^^^^^^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m739[0m, in [35mlogin[0m
    [31mdriver.get[0m[1;31m("https://app.augmentcode.com/account")[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m479[0m, in [35mget[0m
    [31mself.execute[0m[1;31m(Command.GET, {"url": url})[0m
    [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m451[0m, in [35mexecute[0m
    response = cast(RemoteConnection, self.command_executor).execute(driver_command, params)

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m404[0m, in [35mexecute[0m
    return [31mself._request[0m[1;31m(command_info[0], url, body=data)[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m428[0m, in [35m_request[0m
    response = self._conn.request(method, url, body=body, headers=headers, timeout=self._client_config.timeout)

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m143[0m, in [35mrequest[0m
    return [31mself.request_encode_body[0m[1;31m([0m
           [31m~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mmethod, url, fields=fields, headers=headers, **urlopen_kw[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m278[0m, in [35mrequest_encode_body[0m
    return [31mself.urlopen[0m[1;31m(method, url, **extra_kw)[0m
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\poolmanager.py"[0m, line [35m459[0m, in [35murlopen[0m
    response = conn.urlopen(method, u.request_uri, **kw)

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-11 13:47:26 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1432[0m, in [35mgetresponse[0m
    [31mself.close[0m[1;31m()[0m
    [31m~~~~~~~~~~[0m[1;31m^^[0m

2025-08-11 13:47:26 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m369[0m, in [35mclose[0m
    def close(self) -> None:
    

2025-08-11 13:47:26 - INFO - [1;35mKeyboardInterrupt[0m

