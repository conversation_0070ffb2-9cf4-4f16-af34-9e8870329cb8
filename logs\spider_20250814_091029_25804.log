2025-08-14 09:10:35 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51408,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:10:35 - INFO - <EMAIL>
2025-08-14 09:10:35 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 09:11:28 - INFO - 获取 cap_value 验证码成功...
2025-08-14 09:11:32 - INFO - 找到 Turnstile...
2025-08-14 09:11:37 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 09:11:40 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 09:11:40 - INFO - CDP:启用节流模式
2025-08-14 09:12:30 - INFO - 验证码已提交，等待跳转...
2025-08-14 09:12:30 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 09:12:30 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 09:12:30 - INFO - ReCAPTCHA Token: 
2025-08-14 09:12:30 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 09:12:32 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:12:32 - INFO - 
第 1/5 次尝试注册...
2025-08-14 09:12:32 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 09:12:35 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 09:13:05 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 09:13:10 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51541,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:13:11 - INFO - <EMAIL>
2025-08-14 09:13:11 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 09:14:00 - INFO - 获取 cap_value 验证码成功...
2025-08-14 09:14:04 - INFO - 找到 Turnstile...
2025-08-14 09:14:09 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 09:14:12 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 09:14:12 - INFO - CDP:启用节流模式
2025-08-14 09:14:59 - INFO - 验证码已提交，等待跳转...
2025-08-14 09:14:59 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 09:14:59 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 09:14:59 - INFO - ReCAPTCHA Token: 
2025-08-14 09:14:59 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 09:15:00 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:15:00 - INFO - 
第 1/5 次尝试注册...
2025-08-14 09:15:00 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 09:15:05 - INFO -    等待 4.82 秒...
2025-08-14 09:15:13 - INFO - 刷新完成，继续下一次尝试。
2025-08-14 09:15:13 - INFO - 
第 2/5 次尝试注册...
2025-08-14 09:15:13 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 09:15:16 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 09:15:46 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 09:15:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51747,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:15:51 - INFO - <EMAIL>
2025-08-14 09:15:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 09:16:39 - INFO - 获取 cap_value 验证码成功...
2025-08-14 09:16:43 - INFO - 找到 Turnstile...
2025-08-14 09:16:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 09:16:51 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 09:16:51 - INFO - CDP:启用节流模式
2025-08-14 09:16:51 - INFO - 验证码已提交，等待跳转...
2025-08-14 09:16:51 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 09:17:25 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 09:17:25 - INFO - ReCAPTCHA Token: 
2025-08-14 09:17:25 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 09:17:29 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:17:29 - INFO - 
第 1/5 次尝试注册...
2025-08-14 09:17:29 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 09:17:32 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 09:18:02 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 09:18:08 - INFO - Traceback (most recent call last):

2025-08-14 09:18:08 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1244[0m, in [35m<module>[0m
    执行注册的最[1;31m后步骤，[0m并包含重试逻辑。
          [1;31m^^^^[0m

2025-08-14 09:18:08 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1198[0m, in [35mmain[0m
    print("检测到人[1;31m类验证页面，开始获取[0m ReCAPTCHA 密钥和 URL。")
               [1;31m^^^^^^^^^^[0m

2025-08-14 09:18:08 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m376[0m, in [35msetup_driver[0m
    '''

2025-08-14 09:18:08 - INFO -   File [35m"e:\Roxyspider\RoxyClient.py"[0m, line [35m140[0m, in [35mbrowser_random_env[0m
    return [31mself._post[0m[1;31m("/browser/random_env", {"workspaceId": workspaceId, "dirId": dirId})[0m.json()
           [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 09:18:08 - INFO -   File [35m"e:\Roxyspider\RoxyClient.py"[0m, line [35m22[0m, in [35m_post[0m
    return [31mrequests.post[0m[1;31m(self.url + path, json=data, headers=self._build_headers())[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 09:18:08 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\api.py"[0m, line [35m115[0m, in [35mpost[0m
    return request("post", url, data=data, json=json, **kwargs)

2025-08-14 09:18:08 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\api.py"[0m, line [35m59[0m, in [35mrequest[0m
    return [31msession.request[0m[1;31m(method=method, url=url, **kwargs)[0m
           [31m~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 09:18:08 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m589[0m, in [35mrequest[0m
    resp = self.send(prep, **send_kwargs)

2025-08-14 09:18:08 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m703[0m, in [35msend[0m
    r = adapter.send(request, **kwargs)

2025-08-14 09:18:08 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\adapters.py"[0m, line [35m667[0m, in [35msend[0m
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )

2025-08-14 09:18:08 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-14 09:18:08 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-14 09:18:08 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-14 09:18:08 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 09:18:08 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 09:18:08 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-14 09:18:08 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-14 09:18:08 - INFO - [1;35mKeyboardInterrupt[0m

