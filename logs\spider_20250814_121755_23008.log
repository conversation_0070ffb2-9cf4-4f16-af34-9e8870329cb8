2025-08-14 12:17:55 - INFO - 测试 HAR 和 WebSocket 功能...
2025-08-14 12:17:55 - INFO - HAR 文件已保存: har_files\test_har_20250814_121755.har
2025-08-14 12:17:55 - INFO - 捕获了 1 个网络请求
2025-08-14 12:17:55 - INFO - 捕获了 1 个 WebSocket 连接
2025-08-14 12:17:55 - INFO -   WebSocket: wss://app.augmentcode.com/ws - 4 个消息帧
2025-08-14 12:17:55 - INFO - WebSocket 摘要已保存: websocket_logs\test_websocket_20250814_121755.json
2025-08-14 12:17:55 - INFO - HAR 和 WebSocket 功能测试成功！
2025-08-14 12:17:55 - INFO - HAR 文件: har_files\test_har_20250814_121755.har
2025-08-14 12:17:55 - INFO - WebSocket 摘要: websocket_logs\test_websocket_20250814_121755.json
2025-08-14 12:17:55 - INFO - WebSocket 测试统计: {'total_connections': 1, 'active_connections': 0, 'closed_connections': 1, 'total_frames': 4, 'sent_frames': 2, 'received_frames': 2}
2025-08-14 12:17:55 - INFO - HAR 和 WebSocket 功能测试完成
2025-08-14 12:17:55 - INFO - 当前网络日志数量: 1
2025-08-14 12:17:55 - INFO - 当前 WebSocket 日志数量: 1
2025-08-14 12:17:55 - INFO - ==================================================
2025-08-14 12:17:58 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51583,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:17:58 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:17:58 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:17:58 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:17:58 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:17:58 - INFO - 记录了 34 个页面资源
2025-08-14 12:17:58 - INFO - 页面支持 WebSocket
2025-08-14 12:17:58 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:17:58 - INFO - <EMAIL>
2025-08-14 12:17:58 - INFO - 网络捕获: 主函数开始
2025-08-14 12:17:58 - INFO - 记录了 34 个页面资源
2025-08-14 12:17:58 - INFO - 页面支持 WebSocket
2025-08-14 12:17:58 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:18:14 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:18:18 - INFO - 找到 Turnstile...
2025-08-14 12:18:19 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:18:22 - INFO - 网络捕获: 登录完成后
2025-08-14 12:18:22 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBwQ05PLUIwb1p6ZVZ1LVE2alJVM1NVTG5maGdDR1Q2Z6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDlkbmJtVlZOVnJ2cTRUU0gyZl9ISTRjakhkcmtEbElPo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:18:22 - INFO - 记录了 20 个页面资源
2025-08-14 12:18:22 - INFO - 页面支持 WebSocket
2025-08-14 12:18:22 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:18:23 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:18:23 - INFO - CDP:启用节流模式
2025-08-14 12:18:23 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:18:23 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:18:37 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=PbzG8ZKp_kKbIW2_haUUE-A3HOv8RfHKeVmpINLuHQo&code_challenge=wGbDMAoqtZvdkII6cZmT7FmEfXvxqEv3mu4dPxCAm3E&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:18:37 - INFO - 记录了 19 个页面资源
2025-08-14 12:18:37 - INFO - 页面支持 WebSocket
2025-08-14 12:18:37 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:18:37 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:18:37 - INFO - ReCAPTCHA Token: 
2025-08-14 12:18:37 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:18:52 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:18:52 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:18:52 - INFO - 记录了 4 个页面资源
2025-08-14 12:18:52 - INFO - 页面支持 WebSocket
2025-08-14 12:18:52 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:18:52 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:18:55 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:18:55 - INFO - 记录了 4 个页面资源
2025-08-14 12:18:55 - INFO - 页面支持 WebSocket
2025-08-14 12:18:55 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:18:55 - INFO - 网络捕获: 定期捕获
2025-08-14 12:18:55 - INFO - 记录了 4 个页面资源
2025-08-14 12:18:55 - INFO - 页面支持 WebSocket
2025-08-14 12:19:25 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:19:25 - INFO - HAR 文件已保存: har_files\error_whjoi1755145075_smartdocker.online_20250814_121925.har
2025-08-14 12:19:25 - INFO - 捕获了 70 个网络请求
2025-08-14 12:19:25 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:19:25 - INFO - WebSocket 摘要已保存: websocket_logs\error_whjoi1755145075_smartdocker.online_20250814_121925_ws.json
2025-08-14 12:19:30 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51705,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:19:30 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:19:30 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:19:30 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:19:30 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:19:30 - INFO - 记录了 34 个页面资源
2025-08-14 12:19:30 - INFO - 页面支持 WebSocket
2025-08-14 12:19:30 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:19:30 - INFO - <EMAIL>
2025-08-14 12:19:30 - INFO - 网络捕获: 主函数开始
2025-08-14 12:19:30 - INFO - 记录了 34 个页面资源
2025-08-14 12:19:30 - INFO - 页面支持 WebSocket
2025-08-14 12:19:30 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:19:40 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:19:44 - INFO - 找到 Turnstile...
2025-08-14 12:19:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:19:49 - INFO - 网络捕获: 登录完成后
2025-08-14 12:19:49 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SB4QzlZZExfRW9vUW5VZUZ3ZnJJVm5FWERNQzdmOEJGSKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEd4azRRSHZSNkUtVmhDVXppakJDR2FDNFl1eVYtNUpoo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:19:49 - INFO - 记录了 21 个页面资源
2025-08-14 12:19:49 - INFO - 页面支持 WebSocket
2025-08-14 12:19:49 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:19:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:19:49 - INFO - CDP:启用节流模式
2025-08-14 12:20:03 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:20:03 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:20:03 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=6QSAObLvOVdkHzd76U9ynyXO_trcThsBYBIrrnoFUUQ&code_challenge=tQAqGjxl4rvLIcI57uUSkVVCNdBK333yKxatbNelVjA&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:20:03 - INFO - 记录了 19 个页面资源
2025-08-14 12:20:03 - INFO - 页面支持 WebSocket
2025-08-14 12:20:03 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:20:03 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:20:03 - INFO - ReCAPTCHA Token: 
2025-08-14 12:20:03 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:20:18 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:20:18 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:20:18 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:20:18 - INFO - 记录了 102 个页面资源
2025-08-14 12:20:18 - INFO - 页面支持 WebSocket
2025-08-14 12:20:18 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:20:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:20:18 - INFO - 成功！当前在订阅页面。
2025-08-14 12:20:18 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:20:18 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:20:21 - INFO - 代码信息: {
  "codeVerifier": "hgbh34udm3HP4kDazG14CtX8iRKHbqSl8546grjkNu4",
  "code_challenge": "rUGWvKdKY1J09ulAoYUhBuxFsJ5zF5pWZsn5j04cCNY",
  "state": "d591287a-d6ec-4444-befb-4fb928f8ff69"
}
2025-08-14 12:20:21 - INFO - ==================================================
2025-08-14 12:20:23 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_01e1a72821eb79ebd7dc0bbc8ffe136f&state=d591287a-d6ec-4444-befb-4fb928f8ff69&tenant_url=https%3A%2F%2Fd20.api.augmentcode.com%2F
2025-08-14 12:20:25 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:20:25 - INFO - 添加session: <EMAIL>   dfa2d9303bdb68387f19af05ef332597045ec889bb54493b1f9938d9dc0416d4   https://d20.api.augmentcode.com/  2025-08-21T04:20:07Z
2025-08-14 12:20:25 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2C60D88-SakZRet0kIqaoz67jbqweNM5vvmAf3CObrEaYXtmi9cwehimO59cpFhC1DBPlw5_eVAS385c-4DlYKjC9NT2450awfZItmxaQ3pUSqhyzzNaSikldIIaRqhEU2HWgtTwUfzMRTT7XCcAxqlsJBG7c7R5ZXm4cR5M6cpJIYf8T0eteu89bVoPNaSpa9tS6r2SGytI9GhgdcbjWZE_g.aJ1kBw.5IHFRHg8Thw9HOwNFGbmMlwPDyw
2025-08-14 12:20:25 - INFO - 
自动化流程成功完成！
2025-08-14 12:20:25 - INFO - 添加第1个
2025-08-14 12:20:39 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51841,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:20:40 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:20:40 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:20:40 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:20:40 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:20:40 - INFO - 记录了 34 个页面资源
2025-08-14 12:20:40 - INFO - 页面支持 WebSocket
2025-08-14 12:20:40 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:20:40 - INFO - <EMAIL>
2025-08-14 12:20:40 - INFO - 网络捕获: 主函数开始
2025-08-14 12:20:40 - INFO - 记录了 34 个页面资源
2025-08-14 12:20:40 - INFO - 页面支持 WebSocket
2025-08-14 12:20:40 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:20:50 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:20:54 - INFO - 找到 Turnstile...
2025-08-14 12:20:55 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:20:58 - INFO - 网络捕获: 登录完成后
2025-08-14 12:20:58 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SAzT1I0c0FscFFuS0pZdVlYMGZkbU9yZ1Q4T1VQS3REbaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIERkQ3dvS2xQX0pRVEIwaXhaWE5zajZJcXlTbmMzeEd3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:20:58 - INFO - 记录了 20 个页面资源
2025-08-14 12:20:58 - INFO - 页面支持 WebSocket
2025-08-14 12:20:58 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:20:59 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:20:59 - INFO - CDP:启用节流模式
2025-08-14 12:20:59 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:20:59 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:20:59 - INFO - 记录了 20 个页面资源
2025-08-14 12:20:59 - INFO - 页面支持 WebSocket
2025-08-14 12:21:13 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:21:13 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:21:13 - INFO - ReCAPTCHA Token: 
2025-08-14 12:21:13 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:21:18 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:21:18 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:21:18 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=oVgNhQICQ4JRteu9UUGh57N5faXy34W1ABPaYfkwfUQ&code_challenge=axlNctR5gYypf9U3xGu4tCURnen7jIKz7SvyDL24WrE&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:21:18 - INFO - 记录了 4 个页面资源
2025-08-14 12:21:18 - INFO - 页面支持 WebSocket
2025-08-14 12:21:18 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:21:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:21:21 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:21:21 - INFO - 记录了 4 个页面资源
2025-08-14 12:21:21 - INFO - 页面支持 WebSocket
2025-08-14 12:21:21 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:21:21 - INFO - 网络捕获: 定期捕获
2025-08-14 12:21:21 - INFO - 记录了 4 个页面资源
2025-08-14 12:21:21 - INFO - 页面支持 WebSocket
2025-08-14 12:21:51 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:21:51 - INFO - HAR 文件已保存: har_files\error_cjles1755145235_smartdocker.online_20250814_122151.har
2025-08-14 12:21:51 - INFO - 捕获了 61 个网络请求
2025-08-14 12:21:51 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:21:51 - INFO - WebSocket 摘要已保存: websocket_logs\error_cjles1755145235_smartdocker.online_20250814_122151_ws.json
2025-08-14 12:21:53 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51967,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:21:54 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:21:54 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:21:54 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:21:54 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:21:54 - INFO - 记录了 34 个页面资源
2025-08-14 12:21:54 - INFO - 页面支持 WebSocket
2025-08-14 12:21:54 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:21:54 - INFO - <EMAIL>
2025-08-14 12:21:54 - INFO - 网络捕获: 主函数开始
2025-08-14 12:21:54 - INFO - 记录了 34 个页面资源
2025-08-14 12:21:54 - INFO - 页面支持 WebSocket
2025-08-14 12:21:54 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:22:04 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:22:08 - INFO - 找到 Turnstile...
2025-08-14 12:22:09 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:22:12 - INFO - 网络捕获: 登录完成后
2025-08-14 12:22:12 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBpanhhUHk0WjA0T0FyOTV5TlVoTEtwWDR5WXVQMWpKU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGQxNnZPbGhlemxyc196bEktdlg4eE1Wa0ttRmROYkFyo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:22:12 - INFO - 记录了 20 个页面资源
2025-08-14 12:22:12 - INFO - 页面支持 WebSocket
2025-08-14 12:22:12 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:22:13 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:22:13 - INFO - CDP:启用节流模式
2025-08-14 12:22:26 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:22:26 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:22:26 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=eqF-yFO3Y0pbJRMdMuHGTdBhC13rUDLZY8jtib1gjTM&code_challenge=_rt65NCLQ9h0SOwwmS8WlkAKxYKTxjvcOientdx2gUU&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:22:26 - INFO - 记录了 19 个页面资源
2025-08-14 12:22:26 - INFO - 页面支持 WebSocket
2025-08-14 12:22:26 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:22:27 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:22:27 - INFO - ReCAPTCHA Token: 
2025-08-14 12:22:27 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:22:57 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 12:22:57 - INFO - 鼠标左键放开操作完成。
2025-08-14 12:22:58 - INFO - 鼠标左键点击页面操作完成。
2025-08-14 12:22:58 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-14 12:23:01 - INFO - 验证成功，已进入目标页面。
2025-08-14 12:23:01 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:23:01 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:23:01 - INFO - 记录了 107 个页面资源
2025-08-14 12:23:01 - INFO - 页面支持 WebSocket
2025-08-14 12:23:01 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:23:01 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:23:01 - INFO - 成功！当前在订阅页面。
2025-08-14 12:23:01 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:23:01 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:23:04 - INFO - 代码信息: {
  "codeVerifier": "YPl_Kd2nViM-LFrFIHuz1FN1rQ4rkddKNQC-VVBfPG4",
  "code_challenge": "DEyBWEBu4khbCoAJ4IWyaDM_5RZAnmxatqmRQWyn4Cs",
  "state": "a42413d8-4574-4977-b789-f6eac519fc5c"
}
2025-08-14 12:23:04 - INFO - ==================================================
2025-08-14 12:23:05 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_af14d6c8495840226b7a4737c1f650f4&state=a42413d8-4574-4977-b789-f6eac519fc5c&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-14 12:23:09 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:23:09 - INFO - 添加session: <EMAIL>   a8bb62357cc2f5db935ed5da253d3391e294ff6507a08e2d6bec49db5b0f5241   https://d13.api.augmentcode.com/  2025-08-21T04:22:30Z
2025-08-14 12:23:09 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAURP_lrsHQd68r_4SU9tZUaSEFTIz67xLiwuVM5px5QT9Tza5QWeG81o0aiC6n8dkXlwnOAA1c04PKX05h7reFap_CXlB2aXxri0FLbqVgwUYuTDAD61Dt8zIVv5PYaVTItVVcai2t4NjAoTkMu8nfRlqYUYpJxYW6LNnVNUz-TvU0lTEVgh9xHPNotNcmtjEybKXvsB3YQK31zjMTHKHt4PMFccZFDA.aJ1kqg.FOl7dbYaRKsJFF8vwFUcjIL-PJ8
2025-08-14 12:23:09 - INFO - 
自动化流程成功完成！
2025-08-14 12:23:09 - INFO - 添加第2个
2025-08-14 12:23:25 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52278,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:23:26 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:23:26 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:23:26 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:23:26 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:23:26 - INFO - 记录了 34 个页面资源
2025-08-14 12:23:26 - INFO - 页面支持 WebSocket
2025-08-14 12:23:26 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:23:26 - INFO - <EMAIL>
2025-08-14 12:23:26 - INFO - 网络捕获: 主函数开始
2025-08-14 12:23:26 - INFO - 记录了 34 个页面资源
2025-08-14 12:23:26 - INFO - 页面支持 WebSocket
2025-08-14 12:23:26 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:23:41 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:23:45 - INFO - 找到 Turnstile...
2025-08-14 12:23:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:23:50 - INFO - 网络捕获: 登录完成后
2025-08-14 12:23:50 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBpVlNGZW9iSXRxYWkxWFVQR2ZmckNPNzRWS1Zub3pPT6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEJieFBSeE9jX3laekdmY1c4WXhJQnprWHljbzMyNGJNo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:23:50 - INFO - 记录了 21 个页面资源
2025-08-14 12:23:50 - INFO - 页面支持 WebSocket
2025-08-14 12:23:50 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:23:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:23:50 - INFO - CDP:启用节流模式
2025-08-14 12:24:05 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:24:05 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:24:05 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=mIJN-SpMvMyrYznWQbtkMu-tNLPreGQHmM2grAxULF4&code_challenge=oVa3t88lornEz7YH-ifFlzjVBJrcsKjjV9uaet1YTfc&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:24:05 - INFO - 记录了 19 个页面资源
2025-08-14 12:24:05 - INFO - 页面支持 WebSocket
2025-08-14 12:24:05 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:24:05 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:24:05 - INFO - ReCAPTCHA Token: 
2025-08-14 12:24:05 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:24:14 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:24:14 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:24:14 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:24:14 - INFO - 记录了 72 个页面资源
2025-08-14 12:24:14 - INFO - 页面支持 WebSocket
2025-08-14 12:24:14 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:24:14 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:24:14 - INFO - 成功！当前在订阅页面。
2025-08-14 12:24:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:24:14 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:24:17 - INFO - 代码信息: {
  "codeVerifier": "323_L3sbNppBGWgbHiNG9Z78Whidt_GeffvcNT6cpUo",
  "code_challenge": "Oo-ubFZ_GGd-_NOXMctS4PBgqYlc2lOvy_Hh5HfcfAM",
  "state": "********-89bc-4e1b-903f-9a444ddf48c2"
}
2025-08-14 12:24:17 - INFO - ==================================================
2025-08-14 12:24:18 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_36fd552a5734b8760b8a5b91d89db1c7&state=********-89bc-4e1b-903f-9a444ddf48c2&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-14 12:24:20 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:24:20 - INFO - 添加session: <EMAIL>   046bd65c798392cecd919d8a775ef4b0ddf3dacd389cb327ca89d71a0f0b54ca   https://d6.api.augmentcode.com/  2025-08-21T04:24:08Z
2025-08-14 12:24:20 - INFO - email:<EMAIL> === cookie:.eJxNjc1ugzAQhN9lz1D5Dy-bU94EGXZpnGCDHJIqSvvutVAPPc5ovm_eMGxSUsiSdzjt5SENzCHF5TXkkAROAA18xqfkfznyNjzuUobItZAU4vLte2Lv2DqruZ-NRe4RzTzWeV7zVElNqJ1SVpHWPRJ5Sw0cmsNQTV_Py_Wiseu065wy53sKZed1ukn5WPMSs8AfcRyT9UQGpxaVcOs6P7aj8dgij4qmuf5bDT-_kAxEYQ.aJ1k8w.raY0n0jXxLV4pvMzPtVIqPnfWEI
2025-08-14 12:24:20 - INFO - 
自动化流程成功完成！
2025-08-14 12:24:20 - INFO - HAR 文件已保存: har_files\success_wvhjh1755145402_smartdocker.online_20250814_122420.har
2025-08-14 12:24:20 - INFO - 捕获了 141 个网络请求
2025-08-14 12:24:20 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:24:20 - INFO - WebSocket 摘要已保存: websocket_logs\success_wvhjh1755145402_smartdocker.online_20250814_122420_ws.json
2025-08-14 12:24:20 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 12:24:20 - INFO - 添加第3个
2025-08-14 12:24:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52439,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:24:40 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:24:40 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:24:40 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:24:40 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:24:40 - INFO - 记录了 34 个页面资源
2025-08-14 12:24:40 - INFO - 页面支持 WebSocket
2025-08-14 12:24:40 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:24:40 - INFO - <EMAIL>
2025-08-14 12:24:40 - INFO - 网络捕获: 主函数开始
2025-08-14 12:24:40 - INFO - 记录了 34 个页面资源
2025-08-14 12:24:40 - INFO - 页面支持 WebSocket
2025-08-14 12:24:40 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:25:02 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:25:07 - INFO - 找到 Turnstile...
2025-08-14 12:25:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:25:11 - INFO - 网络捕获: 登录完成后
2025-08-14 12:25:11 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SB6dWF5UF9nZEFVSlZHMVZqQTJlbGU2MWZubkxFTXZmUqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEdwYWY4WFlra3plLUNSbTR0UUJYTHdPeHRPRjJtbjE3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:25:11 - INFO - 记录了 20 个页面资源
2025-08-14 12:25:13 - INFO - 页面支持 WebSocket
2025-08-14 12:25:13 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:25:14 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:25:14 - INFO - CDP:启用节流模式
2025-08-14 12:25:14 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:25:14 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:25:14 - INFO - 记录了 21 个页面资源
2025-08-14 12:25:14 - INFO - 页面支持 WebSocket
2025-08-14 12:25:14 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:25:14 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:25:28 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:25:29 - INFO - ReCAPTCHA Token: 
2025-08-14 12:25:29 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:25:59 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 12:25:59 - INFO - 鼠标左键放开操作完成。
2025-08-14 12:25:59 - INFO - 鼠标左键点击页面操作完成。
2025-08-14 12:26:00 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-14 12:26:03 - INFO - 验证成功，已进入目标页面。
2025-08-14 12:26:03 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:26:03 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:26:03 - INFO - 记录了 117 个页面资源
2025-08-14 12:26:03 - INFO - 页面支持 WebSocket
2025-08-14 12:26:03 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:26:03 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:26:03 - INFO - 成功！当前在订阅页面。
2025-08-14 12:26:03 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:26:03 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:26:06 - INFO - 代码信息: {
  "codeVerifier": "iPp0NYLCiucSqTZsiWS2ObEui1hIcdNz_hcxWHsB3u0",
  "code_challenge": "k-Om_pg2nvD6LQsK3p3Vr4QS_D8Uqc2rWJ4g2E5-4sk",
  "state": "2a859812-78a0-4ee7-b733-32d87a926f23"
}
2025-08-14 12:26:06 - INFO - ==================================================
2025-08-14 12:26:06 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_e2aa8fba42ab1554847ae8b84eb33f79&state=2a859812-78a0-4ee7-b733-32d87a926f23&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-14 12:26:08 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:26:08 - INFO - 添加session: <EMAIL>   84d5245f48c668b5942634ea60c223f381d12a7444ef34974af1de7410913785   https://d12.api.augmentcode.com/  2025-08-21T04:25:32Z
2025-08-14 12:26:08 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtV1UEvXLqjcxaJMu1Fh0oDgBirZ3r2B00eUM5r35gukmrZKKHnA52kMGWKmW7XNSqgIXgAHey1P0Xy58mx53aVPhXkilsn2HlDl469EZTqt1kROjzNTnuuvSSYwx5YjRx-xfHaKxZoBTcxq6ST_W59VE7w36vnu7V2oH78tV2suuW1GBP-I8ptnaYNGMNrs0IlMYE9t1XFwmpJmCcxF-fgGqVETQ.aJ1lYA.E2u453zzRtInrXLaBI6cdhuVcjg
2025-08-14 12:26:08 - INFO - 
自动化流程成功完成！
2025-08-14 12:26:08 - INFO - HAR 文件已保存: har_files\success_njfvk1755145475_smartdocker.online_20250814_122608.har
2025-08-14 12:26:08 - INFO - 捕获了 163 个网络请求
2025-08-14 12:26:08 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:26:08 - INFO - WebSocket 摘要已保存: websocket_logs\success_njfvk1755145475_smartdocker.online_20250814_122608_ws.json
2025-08-14 12:26:08 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 12:26:08 - INFO - 添加第4个
2025-08-14 12:26:25 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52588,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:26:25 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:26:25 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:26:25 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:26:25 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:26:25 - INFO - 记录了 34 个页面资源
2025-08-14 12:26:25 - INFO - 页面支持 WebSocket
2025-08-14 12:26:25 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:26:25 - INFO - <EMAIL>
2025-08-14 12:26:25 - INFO - 网络捕获: 主函数开始
2025-08-14 12:26:25 - INFO - 记录了 34 个页面资源
2025-08-14 12:26:25 - INFO - 页面支持 WebSocket
2025-08-14 12:26:25 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:26:36 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:26:41 - INFO - 找到 Turnstile...
2025-08-14 12:26:42 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:26:45 - INFO - 网络捕获: 登录完成后
2025-08-14 12:26:45 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBNYkdZcko1bDRnLXI5anVPVC1yZ2V4a3R6dnQ1czc4RKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFB0a2FVZlA4RzZBR1BSTC13YWdGSTVQa2RMRjQ3cURmo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:26:45 - INFO - 记录了 21 个页面资源
2025-08-14 12:26:45 - INFO - 页面支持 WebSocket
2025-08-14 12:26:45 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:26:45 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:26:45 - INFO - CDP:启用节流模式
2025-08-14 12:26:45 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:26:45 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:26:59 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=jVC2rccvAaygvrt1-iVP3NfOz1nwELSwkrgEufYwJ-Q&code_challenge=ERO2uxBD-q_gaOooUMNJLcjXiPwW3KPNIMmB2wFusm8&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:26:59 - INFO - 记录了 19 个页面资源
2025-08-14 12:26:59 - INFO - 页面支持 WebSocket
2025-08-14 12:26:59 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:26:59 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:26:59 - INFO - ReCAPTCHA Token: 
2025-08-14 12:26:59 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:27:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:27:22 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:27:22 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:27:22 - INFO - 记录了 105 个页面资源
2025-08-14 12:27:22 - INFO - 页面支持 WebSocket
2025-08-14 12:27:22 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:27:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:27:22 - INFO - 成功！当前在订阅页面。
2025-08-14 12:27:22 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:27:22 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:27:25 - INFO - 代码信息: {
  "codeVerifier": "krCyRv4QXYDzxiYx6pSjZJ02VWPAojIaxJqjvDufamo",
  "code_challenge": "5I1ET5Y1KhmcgJ1Q-GtN72CDCcFIBnoTEl1mDXFlxms",
  "state": "43da902a-4e7b-42ea-b3c6-994a0f34cfd2"
}
2025-08-14 12:27:25 - INFO - ==================================================
2025-08-14 12:27:26 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_dfb3b4b9e8d3a4a74ca1b00d1d3632ef&state=43da902a-4e7b-42ea-b3c6-994a0f34cfd2&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-14 12:27:28 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:27:28 - INFO - 添加session: <EMAIL>   2bc8bc3b956d938958966a5a8d2e687ae2c2b81a2781d8b0fd700804cba01c77   https://d9.api.augmentcode.com/  2025-08-21T04:27:02Z
2025-08-14 12:27:28 - INFO - email:<EMAIL> === cookie:.eJxNjcFugzAQRP9lzxBhs7Y3OeVPkPEuyAUb4pBKVdp_r4V66HFG8968YdilJJ8lH3A7yksamHyK69eQfRK4ATQwx0_J_3LkfXg9pQyRayHJx_Xb0pWtoR57xTTp3vEVO81Y53nLoZJISlPt0GnU1pJy2MCpOQ3V9LEuj1k5YxQaQ-r-TL4cvIVFymXLa8wCf8R5HCal2ODYdvW7RR18OwZD7SRONHXGsh7h5xdpz0TE.aJ1lrw.oB8ouRt8yprlHZA_rojGcH2sBkE
2025-08-14 12:27:28 - INFO - 
自动化流程成功完成！
2025-08-14 12:27:28 - INFO - HAR 文件已保存: har_files\success_jlkqg1755145581_smartdocker.online_20250814_122728.har
2025-08-14 12:27:28 - INFO - 捕获了 163 个网络请求
2025-08-14 12:27:28 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:27:28 - INFO - WebSocket 摘要已保存: websocket_logs\success_jlkqg1755145581_smartdocker.online_20250814_122728_ws.json
2025-08-14 12:27:28 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 12:27:28 - INFO - 添加第5个
2025-08-14 12:27:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52735,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:27:44 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:27:44 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:27:44 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:27:44 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:27:44 - INFO - 记录了 34 个页面资源
2025-08-14 12:27:44 - INFO - 页面支持 WebSocket
2025-08-14 12:27:44 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:27:44 - INFO - <EMAIL>
2025-08-14 12:27:44 - INFO - 网络捕获: 主函数开始
2025-08-14 12:27:44 - INFO - 记录了 34 个页面资源
2025-08-14 12:27:44 - INFO - 页面支持 WebSocket
2025-08-14 12:27:44 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:27:54 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:27:58 - INFO - 找到 Turnstile...
2025-08-14 12:28:00 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:28:03 - INFO - 网络捕获: 登录完成后
2025-08-14 12:28:03 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SA1TFA5b0U1SDRVYUtWZ29sSzl1cndSQk9pWC1tTE40Z6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGxUSk9kcVNSekh5Z2RTMHRMVjhVWG03RlgzbG5vZ1hho2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:28:03 - INFO - 记录了 21 个页面资源
2025-08-14 12:28:03 - INFO - 页面支持 WebSocket
2025-08-14 12:28:03 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:28:03 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:28:03 - INFO - CDP:启用节流模式
2025-08-14 12:28:17 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:28:17 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:28:17 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=XgjrMeDRGH7cS_Uc8CWQXhfD5wByZga0i1LRVvMWzwU&code_challenge=2IXzzszXM8MqtyVMksoWETGGZ_Q_56WzvXnwEfHq5tc&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:28:17 - INFO - 记录了 19 个页面资源
2025-08-14 12:28:17 - INFO - 页面支持 WebSocket
2025-08-14 12:28:17 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:28:17 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:28:17 - INFO - ReCAPTCHA Token: 
2025-08-14 12:28:17 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:28:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:28:22 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:28:22 - INFO - 记录了 3 个页面资源
2025-08-14 12:28:22 - INFO - 页面支持 WebSocket
2025-08-14 12:28:22 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:28:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:28:25 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:28:25 - INFO - 记录了 3 个页面资源
2025-08-14 12:28:25 - INFO - 页面支持 WebSocket
2025-08-14 12:28:25 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:28:25 - INFO - 网络捕获: 定期捕获
2025-08-14 12:28:25 - INFO - 记录了 3 个页面资源
2025-08-14 12:28:25 - INFO - 页面支持 WebSocket
2025-08-14 12:28:55 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:28:55 - INFO - HAR 文件已保存: har_files\error_jknnt1755145660_smartdocker.online_20250814_122855.har
2025-08-14 12:28:55 - INFO - 捕获了 70 个网络请求
2025-08-14 12:28:55 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:28:55 - INFO - WebSocket 摘要已保存: websocket_logs\error_jknnt1755145660_smartdocker.online_20250814_122855_ws.json
2025-08-14 12:28:58 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52938,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:28:58 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:28:58 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:28:58 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:28:58 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:28:58 - INFO - 记录了 34 个页面资源
2025-08-14 12:28:58 - INFO - 页面支持 WebSocket
2025-08-14 12:28:58 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:28:58 - INFO - <EMAIL>
2025-08-14 12:28:58 - INFO - 网络捕获: 主函数开始
2025-08-14 12:28:58 - INFO - 记录了 34 个页面资源
2025-08-14 12:28:58 - INFO - 页面支持 WebSocket
2025-08-14 12:28:58 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:29:09 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:29:13 - INFO - 找到 Turnstile...
2025-08-14 12:29:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:29:18 - INFO - 网络捕获: 登录完成后
2025-08-14 12:29:18 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBzMGMzZW1zbUdSZnVhby04dnRBcWIyWGgwZXlrc0k2TqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEhYSHpfQS1DOTc5OTRGNXZaLWowR2ptekw0elJoR1dZo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:29:18 - INFO - 记录了 20 个页面资源
2025-08-14 12:29:18 - INFO - 页面支持 WebSocket
2025-08-14 12:29:18 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:29:18 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:29:18 - INFO - CDP:启用节流模式
2025-08-14 12:29:18 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:29:18 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:29:18 - INFO - 记录了 20 个页面资源
2025-08-14 12:29:33 - INFO - 页面支持 WebSocket
2025-08-14 12:29:33 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:29:33 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:29:33 - INFO - ReCAPTCHA Token: 
2025-08-14 12:29:33 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:29:37 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:29:37 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:29:37 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=_y1Pbxj_CImUEjK57CI_WNsgyq5mIz4lzlmD6FN938Q&code_challenge=zBIsM_TLJf78Qw75-F12YUgM07hWg0DjuKInXuT13ok&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:29:37 - INFO - 记录了 3 个页面资源
2025-08-14 12:29:37 - INFO - 页面支持 WebSocket
2025-08-14 12:29:37 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:29:37 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:29:40 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:29:40 - INFO - 记录了 3 个页面资源
2025-08-14 12:29:40 - INFO - 页面支持 WebSocket
2025-08-14 12:29:40 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:29:40 - INFO - 网络捕获: 定期捕获
2025-08-14 12:29:40 - INFO - 记录了 3 个页面资源
2025-08-14 12:29:40 - INFO - 页面支持 WebSocket
2025-08-14 12:30:10 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:30:13 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53078,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:30:14 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:30:14 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:30:14 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:30:14 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:30:14 - INFO - 记录了 34 个页面资源
2025-08-14 12:30:14 - INFO - 页面支持 WebSocket
2025-08-14 12:30:14 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:30:14 - INFO - <EMAIL>
2025-08-14 12:30:14 - INFO - 网络捕获: 主函数开始
2025-08-14 12:30:14 - INFO - 记录了 34 个页面资源
2025-08-14 12:30:14 - INFO - 页面支持 WebSocket
2025-08-14 12:30:14 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:30:24 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:30:28 - INFO - 找到 Turnstile...
2025-08-14 12:30:29 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:30:32 - INFO - 网络捕获: 登录完成后
2025-08-14 12:30:32 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SAxMXRpU1A3R0RodjMwcmtLa0xQT0RRWWlobGxEVUh1OaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGYtVk9ZUThramgwaHg1SGNIWUx2QmxQT0xxd29MelZ0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:30:32 - INFO - 记录了 21 个页面资源
2025-08-14 12:30:32 - INFO - 页面支持 WebSocket
2025-08-14 12:30:32 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:30:32 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:30:33 - INFO - CDP:启用节流模式
2025-08-14 12:30:33 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:30:33 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:30:33 - INFO - 记录了 21 个页面资源
2025-08-14 12:30:33 - INFO - 页面支持 WebSocket
2025-08-14 12:30:46 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:30:46 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:30:46 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:30:46 - INFO - ReCAPTCHA Token: 
2025-08-14 12:30:46 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:31:09 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:31:09 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:31:09 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=dAoaSp1PbW4KRkEDsOXlhWG8WqDX1GL38Rd_guuPlko&code_challenge=Io4S8DQyxjFr-hO0UShwaGtG_8MEdBLpwAtj-fidNiw&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:31:09 - INFO - 记录了 3 个页面资源
2025-08-14 12:31:09 - INFO - 页面支持 WebSocket
2025-08-14 12:31:09 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:31:09 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:31:12 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:31:12 - INFO - 记录了 3 个页面资源
2025-08-14 12:31:12 - INFO - 页面支持 WebSocket
2025-08-14 12:31:12 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:31:12 - INFO - 网络捕获: 定期捕获
2025-08-14 12:31:12 - INFO - 记录了 3 个页面资源
2025-08-14 12:31:12 - INFO - 页面支持 WebSocket
2025-08-14 12:31:42 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:31:45 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53211,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:31:45 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:31:45 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:31:45 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:31:45 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:31:45 - INFO - 记录了 34 个页面资源
2025-08-14 12:31:45 - INFO - 页面支持 WebSocket
2025-08-14 12:31:45 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:31:45 - INFO - <EMAIL>
2025-08-14 12:31:45 - INFO - 网络捕获: 主函数开始
2025-08-14 12:31:45 - INFO - 记录了 34 个页面资源
2025-08-14 12:31:45 - INFO - 页面支持 WebSocket
2025-08-14 12:31:45 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:31:55 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:31:59 - INFO - 找到 Turnstile...
2025-08-14 12:32:00 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:32:03 - INFO - 网络捕获: 登录完成后
2025-08-14 12:32:03 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBZRl9VOUJYSlhoWHNNMTVocWgwbGZCZGZqOGdiWHg4OaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFFCQzVjWktEbWN1YzNVakdJU2pTYmV6YW1JdUZkYThio2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:32:04 - INFO - 记录了 20 个页面资源
2025-08-14 12:32:04 - INFO - 页面支持 WebSocket
2025-08-14 12:32:04 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:32:04 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:32:04 - INFO - CDP:启用节流模式
2025-08-14 12:32:04 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:32:04 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:32:04 - INFO - 记录了 20 个页面资源
2025-08-14 12:32:04 - INFO - 页面支持 WebSocket
2025-08-14 12:32:04 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:32:04 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:32:17 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:32:17 - INFO - ReCAPTCHA Token: 
2025-08-14 12:32:17 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:32:48 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 12:32:48 - INFO - 鼠标左键放开操作完成。
2025-08-14 12:32:48 - INFO - 鼠标左键点击页面操作完成。
2025-08-14 12:32:48 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-14 12:32:51 - INFO - 验证成功，已进入目标页面。
2025-08-14 12:32:51 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:32:51 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:32:58 - INFO - 记录了 110 个页面资源
2025-08-14 12:32:58 - INFO - 页面支持 WebSocket
2025-08-14 12:32:58 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:32:58 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:32:58 - INFO - 成功！当前在订阅页面。
2025-08-14 12:32:58 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:32:58 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:33:01 - INFO - 代码信息: {
  "codeVerifier": "U0--IArdpM2IvyGzlVOW1CmVUeS1eU67EATMfD5V-xQ",
  "code_challenge": "c09LnOqaHp9An054okGQko2hiQFj8rE3nQIPrzHj-Vo",
  "state": "6d49aaf9-1cd7-4055-864f-006c0eb0ebc6"
}
2025-08-14 12:33:01 - INFO - ==================================================
2025-08-14 12:33:01 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_caad9188d9d13e961f7b01d38e8ce9c8&state=6d49aaf9-1cd7-4055-864f-006c0eb0ebc6&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-14 12:33:03 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:33:03 - INFO - 添加session: <EMAIL>   9f9b2392fce9af0435af593878d0c08f0c2fc6e918735ac82799024a59232ee5   https://d8.api.augmentcode.com/  2025-08-21T04:32:21Z
2025-08-14 12:33:03 - INFO - email:<EMAIL> === cookie:.eJxNjsEOwiAQRP9lz60BylLw5J80tCwGLVuDrYlR_13SePA4k5k384LhRiV7Jl7huJaNGog-p_k5sM8ER4AGzulB_KdTuA3bncqQQjUo-zS_jXXBYBC6k8FG1fXBudHKUOO88FSbyhotbe-0UOiUcgr7BnbMTqiky5V5lT2i1GiMON2zL2tYpiuVw8JzYoJfYx-OGmU0JrYUUbZaCGyt6FzbjfXGpKYonYDPF5i2RHk.aJ1m_w.Y2XKJlGGxeXRccTsWhggzK2uETw
2025-08-14 12:33:03 - INFO - 
自动化流程成功完成！
2025-08-14 12:33:03 - INFO - 添加第6个
2025-08-14 12:33:26 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53411,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:33:27 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:33:27 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:33:27 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:33:27 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:33:27 - INFO - 记录了 34 个页面资源
2025-08-14 12:33:27 - INFO - 页面支持 WebSocket
2025-08-14 12:33:27 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:33:27 - INFO - <EMAIL>
2025-08-14 12:33:27 - INFO - 网络捕获: 主函数开始
2025-08-14 12:33:27 - INFO - 记录了 34 个页面资源
2025-08-14 12:33:27 - INFO - 页面支持 WebSocket
2025-08-14 12:33:27 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:33:38 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:33:41 - INFO - 找到 Turnstile...
2025-08-14 12:33:43 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:33:46 - INFO - 网络捕获: 登录完成后
2025-08-14 12:33:46 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBEMHp0c1lJY0FsRWdpZEw4cTBQSkJtaEd4aGxjcl94SKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEZTY2tQLVR1Sm1HUkg0ZnFmUXBDemJDZTJxS0Yzd051o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:33:46 - INFO - 记录了 21 个页面资源
2025-08-14 12:33:46 - INFO - 页面支持 WebSocket
2025-08-14 12:33:46 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:33:46 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:33:46 - INFO - CDP:启用节流模式
2025-08-14 12:34:01 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:34:01 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:34:01 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=Fkr3crfahKKSAp7t-MKCxz3FHYjJ_osJpsZ1HPPVqXQ&code_challenge=-UPW5zDdLppQtQBkELSCA4EIk5V4E_hDEiUnB_Q0I2U&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:34:01 - INFO - 记录了 19 个页面资源
2025-08-14 12:34:01 - INFO - 页面支持 WebSocket
2025-08-14 12:34:01 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:34:01 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:34:01 - INFO - ReCAPTCHA Token: 
2025-08-14 12:34:01 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:34:06 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:34:06 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:34:06 - INFO - 记录了 3 个页面资源
2025-08-14 12:34:06 - INFO - 页面支持 WebSocket
2025-08-14 12:34:06 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:34:06 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:34:09 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:34:09 - INFO - 记录了 3 个页面资源
2025-08-14 12:34:09 - INFO - 页面支持 WebSocket
2025-08-14 12:34:09 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:34:09 - INFO - 网络捕获: 定期捕获
2025-08-14 12:34:09 - INFO - 记录了 3 个页面资源
2025-08-14 12:34:09 - INFO - 页面支持 WebSocket
2025-08-14 12:34:39 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:34:39 - INFO - HAR 文件已保存: har_files\error_gujgo1755146002_smartdocker.online_20250814_123439.har
2025-08-14 12:34:39 - INFO - 捕获了 70 个网络请求
2025-08-14 12:34:39 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:34:39 - INFO - WebSocket 摘要已保存: websocket_logs\error_gujgo1755146002_smartdocker.online_20250814_123439_ws.json
2025-08-14 12:34:42 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53602,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:34:43 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:34:43 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:34:43 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:34:43 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:34:43 - INFO - 记录了 34 个页面资源
2025-08-14 12:34:43 - INFO - 页面支持 WebSocket
2025-08-14 12:34:43 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:34:43 - INFO - <EMAIL>
2025-08-14 12:34:43 - INFO - 网络捕获: 主函数开始
2025-08-14 12:34:43 - INFO - 记录了 34 个页面资源
2025-08-14 12:34:43 - INFO - 页面支持 WebSocket
2025-08-14 12:34:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:34:53 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:34:57 - INFO - 找到 Turnstile...
2025-08-14 12:34:59 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:35:02 - INFO - 网络捕获: 登录完成后
2025-08-14 12:35:02 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBkY05UUGE0QndvSExyZWxqUDgxSUo4SXNoWjNMYzJob6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDJFSzV5MG41VWN0WHdRbmlvb2VzSXRwTmNGZ254SU9to2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:35:02 - INFO - 记录了 21 个页面资源
2025-08-14 12:35:02 - INFO - 页面支持 WebSocket
2025-08-14 12:35:02 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:35:02 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:35:02 - INFO - CDP:启用节流模式
2025-08-14 12:35:17 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:35:17 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:35:17 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=aDKZ_-GqDR9fV4GyTU1EVlkNFAEwlNoW5jrgW8nL5Tw&code_challenge=1rzoVQW7OnLrZLqxRRhBJYk5VkXi19j0ljTFl4z3kek&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:35:17 - INFO - 记录了 19 个页面资源
2025-08-14 12:35:17 - INFO - 页面支持 WebSocket
2025-08-14 12:35:17 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:35:17 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:35:17 - INFO - ReCAPTCHA Token: 
2025-08-14 12:35:17 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:35:25 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:35:25 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:35:25 - INFO - 记录了 3 个页面资源
2025-08-14 12:35:25 - INFO - 页面支持 WebSocket
2025-08-14 12:35:25 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:35:25 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:35:28 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:35:28 - INFO - 记录了 3 个页面资源
2025-08-14 12:35:28 - INFO - 页面支持 WebSocket
2025-08-14 12:35:28 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:35:28 - INFO - 网络捕获: 定期捕获
2025-08-14 12:35:28 - INFO - 记录了 3 个页面资源
2025-08-14 12:35:28 - INFO - 页面支持 WebSocket
2025-08-14 12:35:58 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:36:01 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53754,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:36:02 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:36:02 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:36:02 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:36:02 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:36:02 - INFO - 记录了 34 个页面资源
2025-08-14 12:36:02 - INFO - 页面支持 WebSocket
2025-08-14 12:36:02 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:36:02 - INFO - <EMAIL>
2025-08-14 12:36:02 - INFO - 网络捕获: 主函数开始
2025-08-14 12:36:02 - INFO - 记录了 34 个页面资源
2025-08-14 12:36:02 - INFO - 页面支持 WebSocket
2025-08-14 12:36:02 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:36:11 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:36:15 - INFO - 找到 Turnstile...
2025-08-14 12:36:16 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:36:19 - INFO - 网络捕获: 登录完成后
2025-08-14 12:36:19 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBpU29hYzBHeWNwZzdNVXh3Z0hlbnRUY3I5V2Zyd01wcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDVJQnQxNXNrVm81Z1h4a25pZUF2SkZjTlFNcmNjU1VLo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:36:19 - INFO - 记录了 21 个页面资源
2025-08-14 12:36:19 - INFO - 页面支持 WebSocket
2025-08-14 12:36:19 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:36:20 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:36:20 - INFO - CDP:启用节流模式
2025-08-14 12:36:34 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:36:34 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:36:34 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=_bdwrcMztnTBp3qz2d4j6uFN6N0pbaqpbFq4nIwmCps&code_challenge=34SLZM-_l3y5fQtc_M4yYT3Aia50XmFiMP3yCudVrIc&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:36:34 - INFO - 记录了 19 个页面资源
2025-08-14 12:36:34 - INFO - 页面支持 WebSocket
2025-08-14 12:36:34 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:36:34 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:36:34 - INFO - ReCAPTCHA Token: 
2025-08-14 12:36:34 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:36:36 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:36:36 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:36:36 - INFO - 记录了 3 个页面资源
2025-08-14 12:36:36 - INFO - 页面支持 WebSocket
2025-08-14 12:36:36 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:36:36 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:36:39 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:36:39 - INFO - 记录了 3 个页面资源
2025-08-14 12:36:39 - INFO - 页面支持 WebSocket
2025-08-14 12:36:39 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:36:39 - INFO - 网络捕获: 定期捕获
2025-08-14 12:36:39 - INFO - 记录了 3 个页面资源
2025-08-14 12:36:39 - INFO - 页面支持 WebSocket
2025-08-14 12:37:09 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:37:12 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53895,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:37:12 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:37:12 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:37:12 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:37:12 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:37:12 - INFO - 记录了 34 个页面资源
2025-08-14 12:37:12 - INFO - 页面支持 WebSocket
2025-08-14 12:37:12 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:37:12 - INFO - <EMAIL>
2025-08-14 12:37:12 - INFO - 网络捕获: 主函数开始
2025-08-14 12:37:12 - INFO - 记录了 34 个页面资源
2025-08-14 12:37:12 - INFO - 页面支持 WebSocket
2025-08-14 12:37:12 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:37:22 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:37:26 - INFO - 找到 Turnstile...
2025-08-14 12:37:28 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:37:31 - INFO - 网络捕获: 登录完成后
2025-08-14 12:37:31 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SB3amJRNVVSVXMtN1lwMlVvUllxZTNmQk14QXFWVkQxaKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEwyODlySjlJSFdtRWV0dlNIVGxtRkJZRnNFLWRzY1RCo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:37:31 - INFO - 记录了 21 个页面资源
2025-08-14 12:37:31 - INFO - 页面支持 WebSocket
2025-08-14 12:37:31 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:37:31 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:37:31 - INFO - CDP:启用节流模式
2025-08-14 12:37:31 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:37:31 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:37:31 - INFO - 记录了 21 个页面资源
2025-08-14 12:37:31 - INFO - 页面支持 WebSocket
2025-08-14 12:37:31 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:37:31 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:37:46 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:37:46 - INFO - ReCAPTCHA Token: 
2025-08-14 12:37:46 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:38:05 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:38:05 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:38:05 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=S7aud8zJrAiVy4L9oqIIdmPw8fL6caxfC0G7wmhhsCw&code_challenge=1m0ZbRGLtf5Myj9V0FbK5ekTWG2G6HCXqhiFxtNFW78&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:38:05 - INFO - 记录了 3 个页面资源
2025-08-14 12:38:05 - INFO - 页面支持 WebSocket
2025-08-14 12:38:05 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:38:05 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:38:08 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:38:08 - INFO - 记录了 3 个页面资源
2025-08-14 12:38:08 - INFO - 页面支持 WebSocket
2025-08-14 12:38:08 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:38:08 - INFO - 网络捕获: 定期捕获
2025-08-14 12:38:08 - INFO - 记录了 3 个页面资源
2025-08-14 12:38:08 - INFO - 页面支持 WebSocket
2025-08-14 12:38:38 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:38:41 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54141,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:38:41 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:38:41 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:38:41 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:38:41 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:38:41 - INFO - 记录了 34 个页面资源
2025-08-14 12:38:41 - INFO - 页面支持 WebSocket
2025-08-14 12:38:41 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:38:41 - INFO - <EMAIL>
2025-08-14 12:38:41 - INFO - 网络捕获: 主函数开始
2025-08-14 12:38:41 - INFO - 记录了 34 个页面资源
2025-08-14 12:38:42 - INFO - 页面支持 WebSocket
2025-08-14 12:38:42 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:38:52 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:38:55 - INFO - 找到 Turnstile...
2025-08-14 12:38:57 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:39:00 - INFO - 网络捕获: 登录完成后
2025-08-14 12:39:00 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBWaTdOdFNJeEstWG4zYmNTVUVKVXJ1WmZCUzdkZHRvVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGI1NlhNX1NSR1FpX0FXSG8yY29XMnFuNlM0QzJsQnlRo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:39:00 - INFO - 记录了 21 个页面资源
2025-08-14 12:39:00 - INFO - 页面支持 WebSocket
2025-08-14 12:39:00 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:39:00 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:39:00 - INFO - CDP:启用节流模式
2025-08-14 12:39:00 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:39:00 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:39:00 - INFO - 记录了 21 个页面资源
2025-08-14 12:39:00 - INFO - 页面支持 WebSocket
2025-08-14 12:39:14 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:39:14 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:39:14 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:39:14 - INFO - ReCAPTCHA Token: 
2025-08-14 12:39:14 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:39:25 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:39:25 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:39:25 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:39:25 - INFO - 记录了 77 个页面资源
2025-08-14 12:39:33 - INFO - 页面支持 WebSocket
2025-08-14 12:39:33 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:39:33 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:39:33 - INFO - 成功！当前在订阅页面。
2025-08-14 12:39:33 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:39:33 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:39:36 - INFO - 代码信息: {
  "codeVerifier": "lBOQt_ha2kA237RtiFnfpMP9nJWp1OTboN2hAhpgxMw",
  "code_challenge": "AW2FVJc0OuDFPZ1MDFiXlgMG6WwhoDq1w3-QVpUWbf8",
  "state": "330bb920-c680-459e-99dc-97cf95c3c47f"
}
2025-08-14 12:39:36 - INFO - ==================================================
2025-08-14 12:39:37 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_a24e7ebb8df654ab3d620bcd3c71c975&state=330bb920-c680-459e-99dc-97cf95c3c47f&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-14 12:39:39 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:39:39 - INFO - 添加session: <EMAIL>   c60dfa2cf35ac6c971d6df72a99b392ff02b7606d82d13819ab80bc96b221941   https://d10.api.augmentcode.com/  2025-08-21T04:39:17Z
2025-08-14 12:39:39 - INFO - email:<EMAIL> === cookie:.eJxNjUEOgyAURO_y19rABwW66k0IytfQChrUJk3bu5e6aXczk5k3T7AL5egSpQ3OW96pgsHFMD1scpHgDFDBGO6U_nzwi91Xyjb4ElB0YXq12vhWoZKCez2gUL7DRjMq9TSnviwbIzQziimpjcEisIKDcgAKaNyv48xV03DZMoaXNbq8-bm_UT7NaQrpC_v9sh5dJzSvNR-olsIMdccl1q5FRlIJiczD-wNCDEP7.aJ1oig.2A2MlSsQ7krvR6TtQC2hNcXjb7A
2025-08-14 12:39:39 - INFO - 
自动化流程成功完成！
2025-08-14 12:39:39 - INFO - 添加第7个
2025-08-14 12:40:00 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54391,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:40:00 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:40:00 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:40:00 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:40:00 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:40:00 - INFO - 记录了 34 个页面资源
2025-08-14 12:40:00 - INFO - 页面支持 WebSocket
2025-08-14 12:40:00 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:40:00 - INFO - <EMAIL>
2025-08-14 12:40:00 - INFO - 网络捕获: 主函数开始
2025-08-14 12:40:00 - INFO - 记录了 34 个页面资源
2025-08-14 12:40:00 - INFO - 页面支持 WebSocket
2025-08-14 12:40:00 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:40:11 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:40:15 - INFO - 找到 Turnstile...
2025-08-14 12:40:17 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:40:20 - INFO - 网络捕获: 登录完成后
2025-08-14 12:40:20 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBvY1VMYTh4WHVHcmg3WHltdkF5bnZ5UEZYUXh1aUFNRKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFhZVzF6SDZocEQzZUVxcFBDRmNyenVRNUtKd29zRXVKo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:40:20 - INFO - 记录了 21 个页面资源
2025-08-14 12:40:20 - INFO - 页面支持 WebSocket
2025-08-14 12:40:20 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:40:20 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:40:20 - INFO - CDP:启用节流模式
2025-08-14 12:40:20 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:40:20 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:40:20 - INFO - 记录了 21 个页面资源
2025-08-14 12:40:20 - INFO - 页面支持 WebSocket
2025-08-14 12:40:20 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:40:20 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:40:35 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:40:35 - INFO - ReCAPTCHA Token: 
2025-08-14 12:40:35 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:40:43 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:40:43 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:40:43 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=RQvj54WmAlyGkWMlaueczn5ouFHZTC9115Y14xkJ7FQ&code_challenge=bpaaQD_FusiPlrihG0plGQP0Ru3tAiwUNBHWqQ61kf0&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:40:43 - INFO - 记录了 3 个页面资源
2025-08-14 12:40:43 - INFO - 页面支持 WebSocket
2025-08-14 12:40:43 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:40:43 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:40:46 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:40:46 - INFO - 记录了 3 个页面资源
2025-08-14 12:40:46 - INFO - 页面支持 WebSocket
2025-08-14 12:40:46 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:40:46 - INFO - 网络捕获: 定期捕获
2025-08-14 12:40:46 - INFO - 记录了 3 个页面资源
2025-08-14 12:40:46 - INFO - 页面支持 WebSocket
2025-08-14 12:41:16 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:41:16 - INFO - HAR 文件已保存: har_files\error_nguhr1755146396_smartdocker.online_20250814_124116.har
2025-08-14 12:41:16 - INFO - 捕获了 61 个网络请求
2025-08-14 12:41:16 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:41:16 - INFO - WebSocket 摘要已保存: websocket_logs\error_nguhr1755146396_smartdocker.online_20250814_124116_ws.json
2025-08-14 12:41:19 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54536,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:41:20 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:41:20 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:41:20 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:41:20 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:41:20 - INFO - 记录了 34 个页面资源
2025-08-14 12:41:20 - INFO - 页面支持 WebSocket
2025-08-14 12:41:20 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:41:20 - INFO - <EMAIL>
2025-08-14 12:41:20 - INFO - 网络捕获: 主函数开始
2025-08-14 12:41:20 - INFO - 记录了 34 个页面资源
2025-08-14 12:41:20 - INFO - 页面支持 WebSocket
2025-08-14 12:41:20 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:41:30 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:41:35 - INFO - 找到 Turnstile...
2025-08-14 12:41:36 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:41:39 - INFO - 网络捕获: 登录完成后
2025-08-14 12:41:39 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBONzlkQ1FKNzFnU1N3cGtraXJ5NlRXbDBPWGtSelUzaKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE9Wdzdfb09sTW5YU2VyZ3htVTUwOFlBRmwzREVDQ2FDo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:41:39 - INFO - 记录了 21 个页面资源
2025-08-14 12:41:39 - INFO - 页面支持 WebSocket
2025-08-14 12:41:39 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:41:39 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:41:40 - INFO - CDP:启用节流模式
2025-08-14 12:41:53 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:41:53 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:41:53 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=cNoLjj16oM0kp_fhYaTh7tPiuok5UimAhFGh7LSB6hA&code_challenge=u5Muur9r4IvFpzfADqP7jtbRRViUAOzj7x3pKm1Lz4Y&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:41:53 - INFO - 记录了 19 个页面资源
2025-08-14 12:41:53 - INFO - 页面支持 WebSocket
2025-08-14 12:41:53 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:41:53 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:41:53 - INFO - ReCAPTCHA Token: 
2025-08-14 12:41:53 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:42:34 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 12:42:34 - INFO - 鼠标左键放开操作完成。
2025-08-14 12:42:34 - INFO - 鼠标左键点击页面操作完成。
2025-08-14 12:42:34 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-14 12:42:37 - INFO - 验证成功，已进入目标页面。
2025-08-14 12:42:37 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:42:37 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:42:37 - INFO - 记录了 110 个页面资源
2025-08-14 12:42:37 - INFO - 页面支持 WebSocket
2025-08-14 12:42:37 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:42:37 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:42:37 - INFO - 成功！当前在订阅页面。
2025-08-14 12:42:37 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:42:37 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:42:40 - INFO - 代码信息: {
  "codeVerifier": "LAgqByaMDNwxocQ8xmkyQT4AnlOI1BlwuXRvyrKR6U0",
  "code_challenge": "SyE1u9wv5qkdLV6CPIUbkJUgUCZrL9Uwa1bBGv7bTfQ",
  "state": "7d37bbbf-9fb6-4700-8965-3197bc5b0268"
}
2025-08-14 12:42:40 - INFO - ==================================================
2025-08-14 12:42:43 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_afffdbfa973c607eceef132705e69c38&state=7d37bbbf-9fb6-4700-8965-3197bc5b0268&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-14 12:42:45 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:42:45 - INFO - 添加session: <EMAIL>   1ba2898b9964e419159d4da5b0f799859bef05d1cac53a25eeca3b645cede1e5   https://d6.api.augmentcode.com/  2025-08-21T04:41:57Z
2025-08-14 12:42:45 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3ZlDCxsTvkTC8ySopp1ROxKVdt_L0p76G1mNPPmE5Y7txqE5YDL0U4eIIdato9FQmW4AAxwK-8s_3xJ9-V8cFtK6gHXULYv9JTQR2W0Sj7P2qU1q4zY67LL2pfeWI2zn2gmqxxZ62mAJ-ZJ6CS5na9NOWuVQU14fdTQjrSvb9xedtmKMPwtfo8xYHApjhQnPRqFXRHmMVpt3GQ0hWjh-wfB0UT0.aJ1pQg.0DWAVAs7S0SZbU0g0t3RT0_NVUs
2025-08-14 12:42:45 - INFO - 
自动化流程成功完成！
2025-08-14 12:42:45 - INFO - 添加第8个
2025-08-14 12:43:07 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54743,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:43:08 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:43:08 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:43:08 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:43:08 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:43:08 - INFO - 记录了 34 个页面资源
2025-08-14 12:43:08 - INFO - 页面支持 WebSocket
2025-08-14 12:43:08 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:43:08 - INFO - <EMAIL>
2025-08-14 12:43:08 - INFO - 网络捕获: 主函数开始
2025-08-14 12:43:08 - INFO - 记录了 34 个页面资源
2025-08-14 12:43:08 - INFO - 页面支持 WebSocket
2025-08-14 12:43:08 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:43:18 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:43:22 - INFO - 找到 Turnstile...
2025-08-14 12:43:24 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:43:27 - INFO - 网络捕获: 登录完成后
2025-08-14 12:43:27 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SByOEE1TDB4M2dKUllLUUdzSkVYb2dYTEhmenpnX1JQYqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVOTUJmOWZVNkE3QVQtNFYzVkF0eWFlcUc2MEYtazFOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:43:27 - INFO - 记录了 21 个页面资源
2025-08-14 12:43:27 - INFO - 页面支持 WebSocket
2025-08-14 12:43:27 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:43:27 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:43:27 - INFO - CDP:启用节流模式
2025-08-14 12:43:42 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:43:42 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:43:42 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=4YmSr-nZb3g-2MUvQfkYO_7kWDnbonQqfIo9UqOjuD0&code_challenge=DDqki53V8iLMiUVF3mPCQ6nrq2Klnl7OpO-GpliBw_g&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:43:42 - INFO - 记录了 19 个页面资源
2025-08-14 12:43:42 - INFO - 页面支持 WebSocket
2025-08-14 12:43:42 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:43:42 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:43:42 - INFO - ReCAPTCHA Token: 
2025-08-14 12:43:42 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:43:54 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:43:54 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:43:54 - INFO - 记录了 4 个页面资源
2025-08-14 12:43:54 - INFO - 页面支持 WebSocket
2025-08-14 12:43:54 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:43:54 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:43:57 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:43:57 - INFO - 记录了 4 个页面资源
2025-08-14 12:43:57 - INFO - 页面支持 WebSocket
2025-08-14 12:43:57 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:43:57 - INFO - 网络捕获: 定期捕获
2025-08-14 12:43:57 - INFO - 记录了 4 个页面资源
2025-08-14 12:43:57 - INFO - 页面支持 WebSocket
2025-08-14 12:44:27 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:44:27 - INFO - HAR 文件已保存: har_files\error_lkiod1755146584_smartdocker.online_20250814_124427.har
2025-08-14 12:44:27 - INFO - 捕获了 71 个网络请求
2025-08-14 12:44:27 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:44:27 - INFO - WebSocket 摘要已保存: websocket_logs\error_lkiod1755146584_smartdocker.online_20250814_124427_ws.json
2025-08-14 12:44:30 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54889,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:44:30 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:44:30 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:44:31 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:44:31 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:44:31 - INFO - 记录了 34 个页面资源
2025-08-14 12:44:31 - INFO - 页面支持 WebSocket
2025-08-14 12:44:31 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:44:31 - INFO - <EMAIL>
2025-08-14 12:44:31 - INFO - 网络捕获: 主函数开始
2025-08-14 12:44:31 - INFO - 记录了 34 个页面资源
2025-08-14 12:44:31 - INFO - 页面支持 WebSocket
2025-08-14 12:44:31 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:44:40 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:44:44 - INFO - 找到 Turnstile...
2025-08-14 12:44:45 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:44:48 - INFO - 网络捕获: 登录完成后
2025-08-14 12:44:48 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBBbHZtNzZJZ0xnVnU3alhoUE02b0x5UHRfRjNxR1hRWqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGUybS1POTF2MWplSWR3U25DU0IwVllucXU1ZDQ0UmdGo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:44:48 - INFO - 记录了 20 个页面资源
2025-08-14 12:44:48 - INFO - 页面支持 WebSocket
2025-08-14 12:44:48 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:44:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:44:49 - INFO - CDP:启用节流模式
2025-08-14 12:44:49 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:44:49 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:44:49 - INFO - 记录了 20 个页面资源
2025-08-14 12:44:49 - INFO - 页面支持 WebSocket
2025-08-14 12:44:49 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:45:03 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:45:03 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:45:03 - INFO - ReCAPTCHA Token: 
2025-08-14 12:45:03 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:45:17 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:45:17 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:45:17 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=3rwahRdHtkndI0tF15OZ13tjk6vBdo2oKHsF8hHEUAk&code_challenge=_n6dY6eDi58fyPiQ-csA4ppz1-ucRW0C2067XBYoVew&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:45:17 - INFO - 记录了 3 个页面资源
2025-08-14 12:45:17 - INFO - 页面支持 WebSocket
2025-08-14 12:45:17 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:45:17 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:45:20 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:45:20 - INFO - 记录了 3 个页面资源
2025-08-14 12:45:20 - INFO - 页面支持 WebSocket
2025-08-14 12:45:20 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:45:20 - INFO - 网络捕获: 定期捕获
2025-08-14 12:45:20 - INFO - 记录了 3 个页面资源
2025-08-14 12:45:20 - INFO - 页面支持 WebSocket
2025-08-14 12:45:50 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:45:53 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55097,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:45:54 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:45:54 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:45:54 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:45:54 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:45:54 - INFO - 记录了 34 个页面资源
2025-08-14 12:45:54 - INFO - 页面支持 WebSocket
2025-08-14 12:45:54 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:45:54 - INFO - <EMAIL>
2025-08-14 12:45:54 - INFO - 网络捕获: 主函数开始
2025-08-14 12:45:54 - INFO - 记录了 34 个页面资源
2025-08-14 12:45:54 - INFO - 页面支持 WebSocket
2025-08-14 12:45:54 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:46:10 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:46:14 - INFO - 找到 Turnstile...
2025-08-14 12:46:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:46:18 - INFO - 网络捕获: 登录完成后
2025-08-14 12:46:18 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBzOTVscjFEeGltUVdXZk5FZGZRQlE4UUVfcTRCUE1KOaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE96d3gzMmVFQU9SOEJRZ1JzSlVIdFNJQ3dXLTFxX2xFo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:46:18 - INFO - 记录了 20 个页面资源
2025-08-14 12:46:18 - INFO - 页面支持 WebSocket
2025-08-14 12:46:18 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:46:19 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:46:19 - INFO - CDP:启用节流模式
2025-08-14 12:46:19 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:46:19 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:46:19 - INFO - 记录了 20 个页面资源
2025-08-14 12:46:33 - INFO - 页面支持 WebSocket
2025-08-14 12:46:33 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:46:33 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:46:33 - INFO - ReCAPTCHA Token: 
2025-08-14 12:46:33 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:46:54 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:46:54 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:46:54 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=uAurNUQT1qNaDND615mmc-qrAECqm7q8OOi2Uwxgw7A&code_challenge=-BjutqKMtxGR_OhWUHd84is5d179BrhE1YxAgx7XCAk&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:46:54 - INFO - 记录了 3 个页面资源
2025-08-14 12:46:54 - INFO - 页面支持 WebSocket
2025-08-14 12:46:54 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:46:54 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:46:57 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:46:57 - INFO - 记录了 3 个页面资源
2025-08-14 12:46:57 - INFO - 页面支持 WebSocket
2025-08-14 12:46:57 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:46:57 - INFO - 网络捕获: 定期捕获
2025-08-14 12:46:57 - INFO - 记录了 3 个页面资源
2025-08-14 12:46:57 - INFO - 页面支持 WebSocket
2025-08-14 12:47:27 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:47:30 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55367,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:47:31 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:47:31 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:47:31 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:47:31 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:47:31 - INFO - 记录了 34 个页面资源
2025-08-14 12:47:31 - INFO - 页面支持 WebSocket
2025-08-14 12:47:31 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:47:31 - INFO - <EMAIL>
2025-08-14 12:47:31 - INFO - 网络捕获: 主函数开始
2025-08-14 12:47:31 - INFO - 记录了 34 个页面资源
2025-08-14 12:47:31 - INFO - 页面支持 WebSocket
2025-08-14 12:47:31 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:47:41 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:47:45 - INFO - 找到 Turnstile...
2025-08-14 12:47:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:47:49 - INFO - 网络捕获: 登录完成后
2025-08-14 12:47:49 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBZYmQ1d0ZzelBEY0lRcnhEQU5MOHNpcjhGdnBPYXZUOaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDlsSTl0a3Zjd215M0FvNTJHLUMwNzdyWG9RN3daQm9ao2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:47:49 - INFO - 记录了 21 个页面资源
2025-08-14 12:47:49 - INFO - 页面支持 WebSocket
2025-08-14 12:47:49 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:47:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:47:50 - INFO - CDP:启用节流模式
2025-08-14 12:47:50 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:47:50 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:47:50 - INFO - 记录了 21 个页面资源
2025-08-14 12:48:04 - INFO - 页面支持 WebSocket
2025-08-14 12:48:04 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:48:04 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:48:04 - INFO - ReCAPTCHA Token: 
2025-08-14 12:48:04 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:48:34 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 12:48:34 - INFO - 鼠标左键放开操作完成。
2025-08-14 12:48:35 - INFO - 鼠标左键点击页面操作完成。
2025-08-14 12:48:35 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-14 12:48:38 - INFO - 验证成功，已进入目标页面。
2025-08-14 12:48:38 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:48:38 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=sY2bL3Q94lz5PJ2xpHEdJN8StYDPQmbmWtIqCdNGt_8&code_challenge=TXGfxMDNnffUgr08Sq_Nuj3bz27TqRyr1KadsVwwEvU&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:48:38 - INFO - 记录了 3 个页面资源
2025-08-14 12:48:38 - INFO - 页面支持 WebSocket
2025-08-14 12:48:38 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:48:38 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:48:41 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:48:41 - INFO - 记录了 3 个页面资源
2025-08-14 12:48:41 - INFO - 页面支持 WebSocket
2025-08-14 12:48:41 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:48:41 - INFO - 网络捕获: 定期捕获
2025-08-14 12:48:41 - INFO - 记录了 3 个页面资源
2025-08-14 12:48:41 - INFO - 页面支持 WebSocket
2025-08-14 12:49:11 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:49:14 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55517,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:49:14 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:49:14 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:49:14 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:49:14 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:49:14 - INFO - 记录了 34 个页面资源
2025-08-14 12:49:14 - INFO - 页面支持 WebSocket
2025-08-14 12:49:14 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:49:14 - INFO - <EMAIL>
2025-08-14 12:49:14 - INFO - 网络捕获: 主函数开始
2025-08-14 12:49:14 - INFO - 记录了 34 个页面资源
2025-08-14 12:49:14 - INFO - 页面支持 WebSocket
2025-08-14 12:49:14 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:49:25 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:49:29 - INFO - 找到 Turnstile...
2025-08-14 12:49:31 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:49:34 - INFO - 网络捕获: 登录完成后
2025-08-14 12:49:34 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SB6cFlOa053TmU4UFhPdlFlUmh0eEZfY3Q4eTRmSXVjUqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG1JLUd0TzFYQldJQ0JiRXF2OUk4LWU2RFBOWVk5dWdIo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:49:34 - INFO - 记录了 20 个页面资源
2025-08-14 12:49:34 - INFO - 页面支持 WebSocket
2025-08-14 12:49:34 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:49:35 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:49:35 - INFO - CDP:启用节流模式
2025-08-14 12:49:49 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:49:49 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:49:49 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=uqaWXSCQo8gqt19mHI2U5GRGCbUsxxCM72-0DJ4VYoE&code_challenge=4mFu184k488mKw66q77jEkSny9hQOV-_xQy3oK0WlLM&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:49:49 - INFO - 记录了 19 个页面资源
2025-08-14 12:49:49 - INFO - 页面支持 WebSocket
2025-08-14 12:49:49 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:49:49 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:49:49 - INFO - ReCAPTCHA Token: 
2025-08-14 12:49:49 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:49:58 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:49:58 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:49:58 - INFO - 记录了 3 个页面资源
2025-08-14 12:49:58 - INFO - 页面支持 WebSocket
2025-08-14 12:49:58 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:49:58 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:50:01 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:50:01 - INFO - 记录了 3 个页面资源
2025-08-14 12:50:01 - INFO - 页面支持 WebSocket
2025-08-14 12:50:01 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:50:01 - INFO - 网络捕获: 定期捕获
2025-08-14 12:50:01 - INFO - 记录了 3 个页面资源
2025-08-14 12:50:01 - INFO - 页面支持 WebSocket
2025-08-14 12:50:31 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:50:35 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55701,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:50:35 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:50:35 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:50:35 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:50:35 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:50:35 - INFO - 记录了 34 个页面资源
2025-08-14 12:50:35 - INFO - 页面支持 WebSocket
2025-08-14 12:50:35 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:50:35 - INFO - <EMAIL>
2025-08-14 12:50:35 - INFO - 网络捕获: 主函数开始
2025-08-14 12:50:35 - INFO - 记录了 34 个页面资源
2025-08-14 12:50:35 - INFO - 页面支持 WebSocket
2025-08-14 12:50:35 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:50:46 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:50:49 - INFO - 找到 Turnstile...
2025-08-14 12:50:51 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:50:54 - INFO - 网络捕获: 登录完成后
2025-08-14 12:50:54 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SA2WTlPWTdSb3ZSWHpaSHlISUpYbm5qTDV6NHJiRm55d6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHM1ZTV5MW42UzA3dHFWRGhmazF5aGluN3Zzc0tJMkZSo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:50:54 - INFO - 记录了 20 个页面资源
2025-08-14 12:50:54 - INFO - 页面支持 WebSocket
2025-08-14 12:50:54 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:50:54 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:50:54 - INFO - CDP:启用节流模式
2025-08-14 12:51:08 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:51:08 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:51:09 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=as2bbNI_uz2ftMwCJhwrlpM6imNh9zVCcUG2hVdHZ0g&code_challenge=dnU0lhzB2KgjXNmziYnEyV6hps-moFu-9A1R65AwRbk&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:51:09 - INFO - 记录了 19 个页面资源
2025-08-14 12:51:09 - INFO - 页面支持 WebSocket
2025-08-14 12:51:09 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:51:09 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:51:09 - INFO - ReCAPTCHA Token: 
2025-08-14 12:51:09 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:51:25 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:51:25 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:51:25 - INFO - 记录了 3 个页面资源
2025-08-14 12:51:25 - INFO - 页面支持 WebSocket
2025-08-14 12:51:25 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:51:25 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:51:28 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:51:28 - INFO - 记录了 3 个页面资源
2025-08-14 12:51:28 - INFO - 页面支持 WebSocket
2025-08-14 12:51:28 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:51:28 - INFO - 网络捕获: 定期捕获
2025-08-14 12:51:28 - INFO - 记录了 3 个页面资源
2025-08-14 12:51:28 - INFO - 页面支持 WebSocket
2025-08-14 12:51:58 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:52:01 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55924,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:52:01 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:52:01 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:52:01 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:52:01 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:52:01 - INFO - 记录了 34 个页面资源
2025-08-14 12:52:01 - INFO - 页面支持 WebSocket
2025-08-14 12:52:01 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:52:01 - INFO - <EMAIL>
2025-08-14 12:52:01 - INFO - 网络捕获: 主函数开始
2025-08-14 12:52:01 - INFO - 记录了 34 个页面资源
2025-08-14 12:52:01 - INFO - 页面支持 WebSocket
2025-08-14 12:52:01 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:52:12 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:52:17 - INFO - 找到 Turnstile...
2025-08-14 12:52:18 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:52:21 - INFO - 网络捕获: 登录完成后
2025-08-14 12:52:21 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBmeVBXYUtOQkZUTDM5aVVUeFptZEV2NzVUM3o5cDFYN6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEluRVpUbnp4dkJMSHUtT1BxSUtLbXBSNllyQnpDcVZ2o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:52:21 - INFO - 记录了 21 个页面资源
2025-08-14 12:52:21 - INFO - 页面支持 WebSocket
2025-08-14 12:52:21 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:52:22 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:52:22 - INFO - CDP:启用节流模式
2025-08-14 12:52:22 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:52:22 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:52:22 - INFO - 记录了 21 个页面资源
2025-08-14 12:52:22 - INFO - 页面支持 WebSocket
2025-08-14 12:52:36 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:52:36 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:52:36 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:52:36 - INFO - ReCAPTCHA Token: 
2025-08-14 12:52:36 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:53:03 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:53:03 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:53:03 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=A3wSxE1jqrMF7QtShhvF0GMHaksKjScqsCxhkIqUalY&code_challenge=3pStOzuO5GtB4et_hNDAuoZN_epVRAm3PNXm9hNmdSQ&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 12:53:03 - INFO - 记录了 3 个页面资源
2025-08-14 12:53:03 - INFO - 页面支持 WebSocket
2025-08-14 12:53:03 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:53:03 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:53:06 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:53:06 - INFO - 记录了 3 个页面资源
2025-08-14 12:53:06 - INFO - 页面支持 WebSocket
2025-08-14 12:53:06 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:53:06 - INFO - 网络捕获: 定期捕获
2025-08-14 12:53:06 - INFO - 记录了 3 个页面资源
2025-08-14 12:53:06 - INFO - 页面支持 WebSocket
2025-08-14 12:53:36 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:53:38 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56071,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:53:39 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:53:39 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:53:39 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:53:39 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:53:39 - INFO - 记录了 34 个页面资源
2025-08-14 12:53:39 - INFO - 页面支持 WebSocket
2025-08-14 12:53:39 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:53:39 - INFO - <EMAIL>
2025-08-14 12:53:39 - INFO - 网络捕获: 主函数开始
2025-08-14 12:53:39 - INFO - 记录了 34 个页面资源
2025-08-14 12:53:39 - INFO - 页面支持 WebSocket
2025-08-14 12:53:39 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:53:49 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:53:53 - INFO - 找到 Turnstile...
2025-08-14 12:53:53 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:53:56 - INFO - 网络捕获: 登录完成后
2025-08-14 12:53:56 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBTLVJRZmhBRml4eW9CZVVHUUpUNnBrOVV2bTRWY093QaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDBUZk1GTjliRzZjOGJFZFJXYnVQSjRPMjVYWm13MHN5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:53:56 - INFO - 记录了 21 个页面资源
2025-08-14 12:53:56 - INFO - 页面支持 WebSocket
2025-08-14 12:53:56 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:53:57 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:53:57 - INFO - CDP:启用节流模式
2025-08-14 12:54:12 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:54:12 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:54:12 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=PNfr7i4LMXkO05txWJ4gJyo2dmSrzoyFJbxEvE84Ufc&code_challenge=zRJ-e3atwJFr_7YBSN_LUfTREnC5_qwiN_RoYVfNNhY&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:54:26 - INFO - 记录了 19 个页面资源
2025-08-14 12:54:26 - INFO - 页面支持 WebSocket
2025-08-14 12:54:26 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:54:36 - INFO - 在验证页面上找不到 ReCAPTCHA 令牌，可能已跳转到下一个页面: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="g-recaptcha-response"]"}
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff6784b0256]
	(No symbol) [0x0x7ff6784b050c]
	(No symbol) [0x0x7ff678503887]
	(No symbol) [0x0x7ff6784d84af]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:54:36 - INFO - 执行鼠标操作时发生错误: Message: javascript error: Cannot read properties of null (reading 'value')
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff678461789]
	(No symbol) [0x0x7ff678464b71]
	(No symbol) [0x0x7ff6785019cb]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:54:36 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:54:36 - INFO - 记录了 3 个页面资源
2025-08-14 12:54:36 - INFO - 页面支持 WebSocket
2025-08-14 12:54:36 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:54:36 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:54:39 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:54:39 - INFO - 记录了 3 个页面资源
2025-08-14 12:54:39 - INFO - 页面支持 WebSocket
2025-08-14 12:54:39 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:54:39 - INFO - 网络捕获: 定期捕获
2025-08-14 12:54:39 - INFO - 记录了 3 个页面资源
2025-08-14 12:54:39 - INFO - 页面支持 WebSocket
2025-08-14 12:55:09 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:55:12 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56216,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:55:12 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:55:12 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:55:12 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:55:12 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:55:12 - INFO - 记录了 34 个页面资源
2025-08-14 12:55:12 - INFO - 页面支持 WebSocket
2025-08-14 12:55:12 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:55:12 - INFO - <EMAIL>
2025-08-14 12:55:12 - INFO - 网络捕获: 主函数开始
2025-08-14 12:55:12 - INFO - 记录了 34 个页面资源
2025-08-14 12:55:12 - INFO - 页面支持 WebSocket
2025-08-14 12:55:12 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:55:28 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:55:32 - INFO - 找到 Turnstile...
2025-08-14 12:55:33 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:55:36 - INFO - 网络捕获: 登录完成后
2025-08-14 12:55:36 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SB4Uk4xaDdEWkFtajlYcjNzaVg1X1V0eFg3YjJPTFpvbaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE9kX2dzcUE4ei10THFSMk5vSjNVT0YwQ0JkNnB4cXVzo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:55:36 - INFO - 记录了 20 个页面资源
2025-08-14 12:55:36 - INFO - 页面支持 WebSocket
2025-08-14 12:55:36 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:55:37 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:55:37 - INFO - CDP:启用节流模式
2025-08-14 12:55:51 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:55:51 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:55:51 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=T_h-Ysijy2nfGz_godSY_l0KfBJB42C96uWw98l9uEc&code_challenge=r0cFuXOCodBR8eGps4LyDR3xfSqQBVLch7P6u-xDfS8&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:55:51 - INFO - 记录了 19 个页面资源
2025-08-14 12:55:51 - INFO - 页面支持 WebSocket
2025-08-14 12:55:51 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:55:51 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:55:51 - INFO - ReCAPTCHA Token: 
2025-08-14 12:55:51 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:56:13 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:56:13 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:56:13 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 12:56:13 - INFO - 记录了 105 个页面资源
2025-08-14 12:56:13 - INFO - 页面支持 WebSocket
2025-08-14 12:56:13 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:56:13 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:56:13 - INFO - 成功！当前在订阅页面。
2025-08-14 12:56:13 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 12:56:31 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 12:56:34 - INFO - 代码信息: {
  "codeVerifier": "6D_frGAL1dQ-ZYfMV_l9rc4dukM-mq3TmDp1it5QA5U",
  "code_challenge": "nL2eF4rriKGfu3VvHezKC3105k7AJvl4Sj44gy5hQOA",
  "state": "63124f03-fa13-4b4d-93fc-46a096df3205"
}
2025-08-14 12:56:34 - INFO - ==================================================
2025-08-14 12:56:35 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_c88b9ae75ddcdd4bbb55823682d715df&state=63124f03-fa13-4b4d-93fc-46a096df3205&tenant_url=https%3A%2F%2Fd1.api.augmentcode.com%2F
2025-08-14 12:56:37 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 12:56:37 - INFO - 添加session: <EMAIL>   0cf0792b2abeb738595bf7062950f592d8a4c322c92a8e7afe775bc9d574c965   https://d1.api.augmentcode.com/  2025-08-21T04:55:55Z
2025-08-14 12:56:37 - INFO - email:<EMAIL> === cookie:.eJxNjUuOwjAQRO_S6wT57zaruUlk2p2RldhBJoyEgLtjZWYxyyrVe_WE6cqtxMp1h_Pe7jzAHEteH1ONheEMMMB3_uH6L-d0ne43blNOveAS8_pyGJILjoyWCWelfUqEAec-r1ulTgYhlRICjQpCW2lRiAEOzWHopnXJW5LeWmmcRfN1K7HtaaOF22mra64Mf8TvsWcizWEktHo0yskRqUdNl4tAT-S1hfcHt8lFLg.aJ1shA.BJz4nHZNxcTs5wfjRsJxLZYoKcA
2025-08-14 12:56:37 - INFO - 
自动化流程成功完成！
2025-08-14 12:56:37 - INFO - 添加第9个
2025-08-14 12:56:55 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56409,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:56:56 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:56:56 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:56:56 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:56:56 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:56:56 - INFO - 记录了 34 个页面资源
2025-08-14 12:56:56 - INFO - 页面支持 WebSocket
2025-08-14 12:56:56 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:56:56 - INFO - <EMAIL>
2025-08-14 12:56:56 - INFO - 网络捕获: 主函数开始
2025-08-14 12:56:56 - INFO - 记录了 34 个页面资源
2025-08-14 12:56:56 - INFO - 页面支持 WebSocket
2025-08-14 12:56:56 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:57:06 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:57:10 - INFO - 找到 Turnstile...
2025-08-14 12:57:12 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:57:15 - INFO - 网络捕获: 登录完成后
2025-08-14 12:57:15 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SA2Ym16emtLWTRzR3NmZDAzOVIzQnY0VXhKcHB6RWIwNKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFlVQ2NDWGhrd2d4MmdZM3d2cVV0aDRKeXhBdW5OQXBZo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:57:15 - INFO - 记录了 21 个页面资源
2025-08-14 12:57:15 - INFO - 页面支持 WebSocket
2025-08-14 12:57:15 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:57:15 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:57:15 - INFO - CDP:启用节流模式
2025-08-14 12:57:29 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:57:29 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:57:29 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=MFeft1LmlpmCBqNzv_Asel7fqEB8SJDg0WQzdWf-bvQ&code_challenge=_d3Lu0nPX476HdNP38F_uAtuz1TV2Mm1kGyH0kGVeVs&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:57:29 - INFO - 记录了 18 个页面资源
2025-08-14 12:57:29 - INFO - 页面支持 WebSocket
2025-08-14 12:57:29 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:57:29 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:57:29 - INFO - ReCAPTCHA Token: 
2025-08-14 12:57:29 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:57:59 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:57:59 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:57:59 - INFO - 记录了 3 个页面资源
2025-08-14 12:57:59 - INFO - 页面支持 WebSocket
2025-08-14 12:57:59 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:57:59 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:58:02 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:58:02 - INFO - 记录了 3 个页面资源
2025-08-14 12:58:02 - INFO - 页面支持 WebSocket
2025-08-14 12:58:02 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:58:02 - INFO - 网络捕获: 定期捕获
2025-08-14 12:58:02 - INFO - 记录了 3 个页面资源
2025-08-14 12:58:02 - INFO - 页面支持 WebSocket
2025-08-14 12:58:32 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:58:32 - INFO - HAR 文件已保存: har_files\error_qobpz1755147412_smartdocker.online_20250814_125832.har
2025-08-14 12:58:32 - INFO - 捕获了 70 个网络请求
2025-08-14 12:58:32 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 12:58:32 - INFO - WebSocket 摘要已保存: websocket_logs\error_qobpz1755147412_smartdocker.online_20250814_125832_ws.json
2025-08-14 12:58:35 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56595,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:58:35 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:58:35 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:58:35 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:58:35 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:58:35 - INFO - 记录了 34 个页面资源
2025-08-14 12:58:35 - INFO - 页面支持 WebSocket
2025-08-14 12:58:35 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:58:35 - INFO - <EMAIL>
2025-08-14 12:58:35 - INFO - 网络捕获: 主函数开始
2025-08-14 12:58:35 - INFO - 记录了 34 个页面资源
2025-08-14 12:58:35 - INFO - 页面支持 WebSocket
2025-08-14 12:58:35 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:58:46 - INFO - 获取 cap_value 验证码成功...
2025-08-14 12:58:49 - INFO - 找到 Turnstile...
2025-08-14 12:58:51 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 12:58:54 - INFO - 网络捕获: 登录完成后
2025-08-14 12:58:54 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBCdjFHYi1FTldhZzhYZ3hmdHBkSlJsUjJwOE5LOFJLRqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHpBVC1IM20yeWxGMlRwQVN5SkEyMFF2YWh6RHBNUjE3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 12:58:54 - INFO - 记录了 20 个页面资源
2025-08-14 12:58:54 - INFO - 页面支持 WebSocket
2025-08-14 12:58:54 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 12:58:54 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 12:58:54 - INFO - CDP:启用节流模式
2025-08-14 12:59:08 - INFO - 验证码已提交，等待跳转...
2025-08-14 12:59:08 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 12:59:08 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=_ePom7M3XCkca0vB1m1yr6M1eJus12uOCh1LI5ZqIP4&code_challenge=QjFD47HaF1wlc0KO-ohF_odfi73nprTXCtAq2wO5bNA&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 12:59:08 - INFO - 记录了 18 个页面资源
2025-08-14 12:59:08 - INFO - 页面支持 WebSocket
2025-08-14 12:59:08 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 12:59:08 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 12:59:08 - INFO - ReCAPTCHA Token: 
2025-08-14 12:59:08 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 12:59:11 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 12:59:11 - INFO - 网络捕获: 人类验证过程中
2025-08-14 12:59:11 - INFO - 记录了 3 个页面资源
2025-08-14 12:59:11 - INFO - 页面支持 WebSocket
2025-08-14 12:59:11 - INFO - 
第 1/5 次尝试注册...
2025-08-14 12:59:11 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 12:59:14 - INFO - 网络捕获: 注册尝试后
2025-08-14 12:59:14 - INFO - 记录了 3 个页面资源
2025-08-14 12:59:14 - INFO - 页面支持 WebSocket
2025-08-14 12:59:14 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 12:59:14 - INFO - 网络捕获: 定期捕获
2025-08-14 12:59:14 - INFO - 记录了 3 个页面资源
2025-08-14 12:59:14 - INFO - 页面支持 WebSocket
2025-08-14 12:59:44 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 12:59:47 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56740,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 12:59:47 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 12:59:47 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 12:59:47 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 12:59:47 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 12:59:47 - INFO - 记录了 34 个页面资源
2025-08-14 12:59:47 - INFO - 页面支持 WebSocket
2025-08-14 12:59:47 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 12:59:47 - INFO - <EMAIL>
2025-08-14 12:59:47 - INFO - 网络捕获: 主函数开始
2025-08-14 12:59:47 - INFO - 记录了 34 个页面资源
2025-08-14 12:59:47 - INFO - 页面支持 WebSocket
2025-08-14 12:59:48 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 12:59:57 - INFO - 获取 cap_value 验证码成功...
2025-08-14 13:00:01 - INFO - 找到 Turnstile...
2025-08-14 13:00:03 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 13:00:06 - INFO - 网络捕获: 登录完成后
2025-08-14 13:00:06 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBZQ2JubEUtMExBVkcwbUtrbWZ0VkNQbTYzdjA4WjJBb6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFyNk1WdnJkdzdGcF9qczc0YWhjUkhuR1NwLUJmdVVRo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 13:00:06 - INFO - 记录了 20 个页面资源
2025-08-14 13:00:06 - INFO - 页面支持 WebSocket
2025-08-14 13:00:06 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 13:00:06 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 13:00:06 - INFO - CDP:启用节流模式
2025-08-14 13:00:07 - INFO - 验证码已提交，等待跳转...
2025-08-14 13:00:07 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 13:00:07 - INFO - 记录了 20 个页面资源
2025-08-14 13:00:07 - INFO - 页面支持 WebSocket
2025-08-14 13:00:07 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 13:00:07 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 13:00:21 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 13:00:21 - INFO - ReCAPTCHA Token: 
2025-08-14 13:00:21 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 13:00:25 - INFO - Traceback (most recent call last):

2025-08-14 13:00:25 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-14 13:00:25 - INFO - [1;35mConnectionResetError[0m: [35m[WinError 10054] 远程主机强迫关闭了一个现有的连接。[0m

2025-08-14 13:00:25 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-14 13:00:25 - INFO - Traceback (most recent call last):

2025-08-14 13:00:25 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m2271[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m2045[0m, in [35mmain[0m
    [31mbypass_human_verification[0m[1;31m(driver, duration=30)[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1101[0m, in [35mbypass_human_verification[0m
    [31mactions.move_by_offset(next_x - start_x, next_y - start_y).perform[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\action_chains.py"[0m, line [35m93[0m, in [35mperform[0m
    [31mself.w3c_actions.perform[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\actions\action_builder.py"[0m, line [35m168[0m, in [35mperform[0m
    [31mself.driver.execute[0m[1;31m(Command.W3C_ACTIONS, enc)[0m
    [31m~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m451[0m, in [35mexecute[0m
    response = cast(RemoteConnection, self.command_executor).execute(driver_command, params)

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m404[0m, in [35mexecute[0m
    return [31mself._request[0m[1;31m(command_info[0], url, body=data)[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m428[0m, in [35m_request[0m
    response = self._conn.request(method, url, body=body, headers=headers, timeout=self._client_config.timeout)

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m143[0m, in [35mrequest[0m
    return [31mself.request_encode_body[0m[1;31m([0m
           [31m~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mmethod, url, fields=fields, headers=headers, **urlopen_kw[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m278[0m, in [35mrequest_encode_body[0m
    return [31mself.urlopen[0m[1;31m(method, url, **extra_kw)[0m
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\poolmanager.py"[0m, line [35m459[0m, in [35murlopen[0m
    response = conn.urlopen(method, u.request_uri, **kw)

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-14 13:00:25 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1432[0m, in [35mgetresponse[0m
    [31mself.close[0m[1;31m()[0m
    [31m~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 13:00:25 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m369[0m, in [35mclose[0m
    def close(self) -> None:
    

2025-08-14 13:00:25 - INFO - [1;35mKeyboardInterrupt[0m

