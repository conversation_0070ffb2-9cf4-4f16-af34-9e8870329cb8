from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.service import Service
from RoxyClient import RoxyClient
from main import get_captcha,get_email,get_email_data
from time import sleep
import random

def wait_for_page_load(driver, timeout=30):
    try:
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
    except TimeoutException:
        print(f"页面在{timeout}秒内未完全加载")


def augment_signout():
    driver.get("https://app.augmentcode.com/account/subscription")
def click_ok(retry=10):
    print(retry)

    if retry > 0:
        for i in range(random.randint(3, 5)):
            driver.refresh()
            sleep(3)
        sleep(random.randint(1, 3))
        wait_for_page_load(driver, 60)
        driver.find_element(By.CLASS_NAME, "c-checkbox--mark").click()
        sleep(random.randint(1, 3))
        driver.find_element(By.ID, "signup-button").click()
        #wait_for_page_load(driver, 60)
        sleep(5)
        if "Sign-up rejected" in driver.find_element(By.XPATH, "/html/body/div/div/div/div").text:
            driver.back()
            sleep(5)
            #wait_for_page_load(driver, 60)
                #wait_for_page_load(driver, 60)
            print(f"重试click_ok 倒数{retry}")
            sleep(5)
            retry -= 1
            return click_ok(retry)
        if "Oops!, something went wrong" in driver.page_source:
            print("存在Oops!, something went wrong 终止")
        if driver.current_url == "https://app.augmentcode.com/account":
            print("在首页")
            return True
    return False

# 使用方式
if __name__ == "__main__":
    brwoser_id = "1c42949de80fb5f609dc1fcee8fddde3"
    # 初始化客户端
    client = RoxyClient(port=50000,token="76b402c778930dd44bacc693cb2fb8d0")
    client.browser_local_cache([brwoser_id])
    client.browser_server_cache(22854,[brwoser_id])
    client.browser_random_env(22854,[brwoser_id])
    # 打开浏览器
    rsp = client.browser_open(brwoser_id)
    if rsp.get("code") != 0:
        print("浏览器打开失败:",rsp)
        exit(0)
    # 获取selenium的连接信息
    debuggerAddress = rsp.get("data").get("http")
    driverPath = rsp.get("data").get("driver")
    print(f"浏览器打开成功,debuggerAddress:{debuggerAddress},driverPath:{driverPath}")

    # selenium 连接代码
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_experimental_option("debuggerAddress", debuggerAddress)

    chrome_service = Service(driverPath)
    driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
    driver.implicitly_wait(10)
    #driver.get('https://ip123.in/')
    driver.get('https://app.augmentcode.com/account')
    print(driver.title)
    #driver.get("https://app.augmentcode.com/account")
    wait_for_page_load(driver, 60)
    login_input = driver.find_element(By.ID, "username")
    login_button = driver.find_element(By.NAME, "action")
    capvalue = get_captcha(driver.current_url)
    print("获取capvalue...")
    ej = get_email()
    #ej = "<EMAIL>", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************.uAVYfjrOGXRkG_z_DNlsiYhRxlHz9vBgQ3lAMhrK8k8"
    print("获取email...")
    if ej and login_input:
        email, jwt = ej
        login_input.send_keys(email)
        captchainbpx = driver.find_element(By.NAME,'captcha')
        if captchainbpx and login_button:
            print(f"找到 Turnstile...")
            driver.execute_script("arguments[0].value = '{}';".format(capvalue), captchainbpx)
            login_button.click()
            sleep(3)
            wait_for_page_load(driver, 60)
            email_code = driver.find_element(By.ID, "code")
            code = get_email_data(jwt)
            verify_button = driver.find_element(By.NAME, "action")
            if email_code and code and verify_button:
                email_code.send_keys(code)
                verify_button.click()
                sleep(3)

                print(click_ok(2))

                    # if driver.current_url == "https://app.augmentcode.com/account":
                    #     break

                    # else:
                    #     break
                    #retry -= 1









    #client.browser_close(brwoser_id)