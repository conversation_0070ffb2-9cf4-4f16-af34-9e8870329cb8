2025-08-20 23:27:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57087,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:27:44 - INFO - 127.0.0.1:57087
2025-08-20 23:27:45 - INFO - <EMAIL>
2025-08-20 23:27:45 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:28:02 - INFO - 获取 cap_value 验证码成功...
2025-08-20 23:28:04 - INFO - 找到 Turnstile...
2025-08-20 23:28:08 - INFO - 
主流程中发生严重错误: Message: stale element reference: stale element not found
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#staleelementreferenceexception
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608490224]
	(No symbol) [0x0x7ff60848ecf3]
	(No symbol) [0x0x7ff6084824e9]
	(No symbol) [0x0x7ff60848039f]
	(No symbol) [0x0x7ff60848482c]
	(No symbol) [0x0x7ff6084848ff]
	(No symbol) [0x0x7ff6084ca08b]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff6084c2c16]
	(No symbol) [0x0x7ff6084f8680]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 23:28:08 - INFO - 准备重启流程...
2025-08-20 23:28:11 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57170,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:28:11 - INFO - 127.0.0.1:57170
2025-08-20 23:28:11 - INFO - <EMAIL>
2025-08-20 23:28:11 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:28:32 - INFO - 获取 cap_value 验证码成功...
2025-08-20 23:28:34 - INFO - 找到 Turnstile...
2025-08-20 23:28:37 - INFO - 
主流程中发生严重错误: Message: stale element reference: stale element not found
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#staleelementreferenceexception
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608490224]
	(No symbol) [0x0x7ff60848ecf3]
	(No symbol) [0x0x7ff6084824e9]
	(No symbol) [0x0x7ff60848039f]
	(No symbol) [0x0x7ff60848482c]
	(No symbol) [0x0x7ff6084848ff]
	(No symbol) [0x0x7ff6084ca08b]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff6084c2c16]
	(No symbol) [0x0x7ff6084f8680]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 23:28:37 - INFO - 准备重启流程...
2025-08-20 23:28:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57247,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:28:40 - INFO - 127.0.0.1:57247
2025-08-20 23:28:41 - INFO - <EMAIL>
2025-08-20 23:28:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:28:57 - INFO - 获取 cap_value 验证码成功...
2025-08-20 23:28:59 - INFO - 找到 Turnstile...
2025-08-20 23:29:02 - INFO - 
主流程中发生严重错误: Message: stale element reference: stale element not found
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#staleelementreferenceexception
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608490224]
	(No symbol) [0x0x7ff60848ecf3]
	(No symbol) [0x0x7ff6084824e9]
	(No symbol) [0x0x7ff608482663]
	(No symbol) [0x0x7ff60848039f]
	(No symbol) [0x0x7ff608484b71]
	(No symbol) [0x0x7ff6085219cb]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 23:29:02 - INFO - 准备重启流程...
2025-08-20 23:29:05 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57320,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:29:05 - INFO - 127.0.0.1:57320
2025-08-20 23:29:06 - INFO - <EMAIL>
2025-08-20 23:29:06 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:29:26 - INFO - 获取 cap_value 验证码成功...
2025-08-20 23:29:37 - INFO - 找到 Turnstile...
2025-08-20 23:29:40 - INFO - 
主流程中发生严重错误: Message: stale element reference: stale element not found
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#staleelementreferenceexception
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608490224]
	(No symbol) [0x0x7ff60848ecf3]
	(No symbol) [0x0x7ff6084824e9]
	(No symbol) [0x0x7ff60848039f]
	(No symbol) [0x0x7ff60848482c]
	(No symbol) [0x0x7ff6084848ff]
	(No symbol) [0x0x7ff6084ca08b]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff6084c2c16]
	(No symbol) [0x0x7ff6084f8680]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 23:29:40 - INFO - 准备重启流程...
2025-08-20 23:29:43 - INFO - Traceback (most recent call last):

2025-08-20 23:29:43 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\runpy.py"[0m, line [35m198[0m, in [35m_run_module_as_main[0m
    return _run_code(code, main_globals, None,
                     "__main__", mod_spec)

2025-08-20 23:29:43 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\runpy.py"[0m, line [35m88[0m, in [35m_run_code[0m
    [31mexec[0m[1;31m(code, run_globals)[0m
    [31m~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\launcher/../..\debugpy\__main__.py"[0m, line [35m71[0m, in [35m<module>[0m
    [31mcli.main[0m[1;31m()[0m
    [31m~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\launcher/../..\debugpy/..\debugpy\server\cli.py"[0m, line [35m501[0m, in [35mmain[0m
    [31mrun[0m[1;31m()[0m
    [31m~~~[0m[1;31m^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\launcher/../..\debugpy/..\debugpy\server\cli.py"[0m, line [35m351[0m, in [35mrun_file[0m
    [31mrunpy.run_path[0m[1;31m(target, run_name="__main__")[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py"[0m, line [35m310[0m, in [35mrun_path[0m
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)

2025-08-20 23:29:43 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py"[0m, line [35m127[0m, in [35m_run_module_code[0m
    [31m_run_code[0m[1;31m(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)[0m
    [31m~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py"[0m, line [35m118[0m, in [35m_run_code[0m
    [31mexec[0m[1;31m(code, run_globals)[0m
    [31m~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m946[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m898[0m, in [35mmain[0m
    driver = setup_driver()

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m176[0m, in [35msetup_driver[0m
    rsp = client.browser_open(brwoser_id)

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\RoxyClient.py"[0m, line [35m121[0m, in [35mbrowser_open[0m
    return [31mself._post[0m[1;31m("/browser/open", {"dirId": dirId, "args": args})[0m.json()
           [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\RoxyClient.py"[0m, line [35m22[0m, in [35m_post[0m
    return [31mrequests.post[0m[1;31m(self.url + path, json=data, headers=self._build_headers())[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\requests\api.py"[0m, line [35m115[0m, in [35mpost[0m
    return request("post", url, data=data, json=json, **kwargs)

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\requests\api.py"[0m, line [35m59[0m, in [35mrequest[0m
    return [31msession.request[0m[1;31m(method=method, url=url, **kwargs)[0m
           [31m~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m589[0m, in [35mrequest[0m
    resp = self.send(prep, **send_kwargs)

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m703[0m, in [35msend[0m
    r = adapter.send(request, **kwargs)

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\requests\adapters.py"[0m, line [35m667[0m, in [35msend[0m
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-20 23:29:43 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-20 23:29:43 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-20 23:29:43 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-20 23:29:43 - INFO - [1;35mKeyboardInterrupt[0m

