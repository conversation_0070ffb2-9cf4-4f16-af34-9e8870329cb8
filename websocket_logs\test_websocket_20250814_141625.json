{"captureTime": "2025-08-14T14:16:25.618388Z", "totalConnections": 1, "connections": [{"url": "wss://app.augmentcode.com/ws", "status": "closed", "createdDateTime": "2025-08-14T14:16:25.612345Z", "closedDateTime": "2025-08-14T14:16:25.612352Z", "totalFrames": 4, "sentFrames": 2, "receivedFrames": 2, "handshakeResponse": {"status": 101, "statusText": "Switching Protocols", "headers": {"Upgrade": "websocket", "Connection": "Upgrade", "Sec-WebSocket-Accept": "test-accept-key"}, "timestamp": 1755152185.6123483}, "frames": [{"type": "sent", "timestamp": 1755152185.6123493, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"auth\",\"token\":\"test-token\"}", "payloadLength": 35}, {"type": "received", "timestamp": 1755152185.71235, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"auth_success\",\"user_id\":\"12345\"}", "payloadLength": 40}, {"type": "sent", "timestamp": 1755152186.6123507, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"ping\"}", "payloadLength": 15}, {"type": "received", "timestamp": 1755152186.7123513, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"pong\"}", "payloadLength": 15}]}]}