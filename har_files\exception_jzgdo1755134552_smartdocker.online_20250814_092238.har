{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-14T09:22:32.643544Z", "time": 1000, "request": {"method": "GET", "url": "https://app.augmentcode.com/account", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Test Browser"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html"}], "cookies": [], "content": {"size": 1024, "mimeType": "text/html", "text": "<html><body>Test</body></html>"}, "redirectURL": "", "headersSize": -1, "bodySize": 1024}, "cache": {}, "timings": {"send": 0, "wait": 1000, "receive": 0}}], "websockets": [{"requestId": "test-ws-001", "url": "wss://app.augmentcode.com/ws", "initiator": {"type": "script"}, "createdDateTime": "2025-08-14T09:22:32.643557Z", "status": "closed", "handshakeResponse": {"status": 101, "statusText": "Switching Protocols", "headers": {"Upgrade": "websocket", "Connection": "Upgrade", "Sec-WebSocket-Accept": "test-accept-key"}, "timestamp": **********.6435602}, "frames": [{"type": "sent", "timestamp": **********.6435606, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"auth\",\"token\":\"test-token\"}", "payloadLength": 35}, {"type": "received", "timestamp": **********.7435613, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"auth_success\",\"user_id\":\"12345\"}", "payloadLength": 40}, {"type": "sent", "timestamp": 1755134553.6435626, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"ping\"}", "payloadLength": 15}, {"type": "received", "timestamp": 1755134553.743563, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"pong\"}", "payloadLength": 15}], "closedDateTime": "2025-08-14T09:22:32.643563Z"}], "pages": [{"startedDateTime": "2025-08-14T09:22:38.099370Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}