{"captureTime": "2025-08-14T09:44:24.832001Z", "totalConnections": 1, "connections": [{"url": "wss://app.augmentcode.com/ws", "status": "closed", "createdDateTime": "2025-08-14T09:44:24.830880Z", "closedDateTime": "2025-08-14T09:44:24.830887Z", "totalFrames": 4, "sentFrames": 2, "receivedFrames": 2, "handshakeResponse": {"status": 101, "statusText": "Switching Protocols", "headers": {"Upgrade": "websocket", "Connection": "Upgrade", "Sec-WebSocket-Accept": "test-accept-key"}, "timestamp": 1755135864.8308837}, "frames": [{"type": "sent", "timestamp": 1755135864.8308847, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"auth\",\"token\":\"test-token\"}", "payloadLength": 35}, {"type": "received", "timestamp": 1755135864.9308853, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"auth_success\",\"user_id\":\"12345\"}", "payloadLength": 40}, {"type": "sent", "timestamp": 1755135865.8308861, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"ping\"}", "payloadLength": 15}, {"type": "received", "timestamp": 1755135865.9308867, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"pong\"}", "payloadLength": 15}]}]}