2025-08-14 11:14:03 - INFO - 测试 HAR 和 WebSocket 功能...
2025-08-14 11:14:03 - INFO - HAR 文件已保存: har_files\test_har_20250814_111403.har
2025-08-14 11:14:03 - INFO - 捕获了 1 个网络请求
2025-08-14 11:14:03 - INFO - 捕获了 1 个 WebSocket 连接
2025-08-14 11:14:03 - INFO -   WebSocket: wss://app.augmentcode.com/ws - 4 个消息帧
2025-08-14 11:14:03 - INFO - WebSocket 摘要已保存: websocket_logs\test_websocket_20250814_111403.json
2025-08-14 11:14:03 - INFO - HAR 和 WebSocket 功能测试成功！
2025-08-14 11:14:03 - INFO - HAR 文件: har_files\test_har_20250814_111403.har
2025-08-14 11:14:03 - INFO - WebSocket 摘要: websocket_logs\test_websocket_20250814_111403.json
2025-08-14 11:14:03 - INFO - WebSocket 测试统计: {'total_connections': 1, 'active_connections': 0, 'closed_connections': 1, 'total_frames': 4, 'sent_frames': 2, 'received_frames': 2}
2025-08-14 11:14:03 - INFO - HAR 和 WebSocket 功能测试完成
2025-08-14 11:14:03 - INFO - 当前网络日志数量: 1
2025-08-14 11:14:03 - INFO - 当前 WebSocket 日志数量: 1
2025-08-14 11:14:03 - INFO - ==================================================
2025-08-14 11:14:07 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62544,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:14:07 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:14:07 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:14:07 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:14:07 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:14:07 - INFO - 记录了 34 个页面资源
2025-08-14 11:14:07 - INFO - 页面支持 WebSocket
2025-08-14 11:14:07 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:14:07 - INFO - <EMAIL>
2025-08-14 11:14:07 - INFO - 网络捕获: 主函数开始
2025-08-14 11:14:07 - INFO - 记录了 34 个页面资源
2025-08-14 11:14:07 - INFO - 页面支持 WebSocket
2025-08-14 11:14:07 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:14:17 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:14:21 - INFO - 找到 Turnstile...
2025-08-14 11:14:22 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:14:25 - INFO - 网络捕获: 登录完成后
2025-08-14 11:14:25 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBOcVR0RXA1MUhUNWpPMFhmYnUzUllGS0JZV1VVMUF4Q6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHRDQUZJQUlDdHViOWtBbTRyZ3VpRl9UcHlWTFNsRG43o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:14:25 - INFO - 记录了 20 个页面资源
2025-08-14 11:14:25 - INFO - 页面支持 WebSocket
2025-08-14 11:14:25 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:14:26 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:14:26 - INFO - CDP:启用节流模式
2025-08-14 11:14:26 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:14:26 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:14:26 - INFO - 记录了 20 个页面资源
2025-08-14 11:14:26 - INFO - 页面支持 WebSocket
2025-08-14 11:14:40 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:14:40 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:14:40 - INFO - ReCAPTCHA Token: 
2025-08-14 11:14:40 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:15:10 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 11:15:10 - INFO - 鼠标左键放开操作完成。
2025-08-14 11:15:11 - INFO - 鼠标左键点击页面操作完成。
2025-08-14 11:15:11 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-14 11:15:14 - INFO - 验证成功，已进入目标页面。
2025-08-14 11:15:14 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:15:14 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:15:14 - INFO - 记录了 106 个页面资源
2025-08-14 11:15:14 - INFO - 页面支持 WebSocket
2025-08-14 11:15:14 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:15:14 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:15:14 - INFO - 成功！当前在订阅页面。
2025-08-14 11:15:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:15:14 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:15:17 - INFO - 代码信息: {
  "codeVerifier": "ZfiAEuahuW3fJnZt0N4W7yEL55g4gmuvOFnmZZqlo5c",
  "code_challenge": "LxP2wsEG4Ed0Jbxx9CKFEUqYviRryBZ1iV3ffOES5yc",
  "state": "620b6408-f1e6-4f25-b574-9b92461fefd8"
}
2025-08-14 11:15:17 - INFO - ==================================================
2025-08-14 11:15:18 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_2f92303b185823915743fe1a6a5c8ca3&state=620b6408-f1e6-4f25-b574-9b92461fefd8&tenant_url=https%3A%2F%2Fd17.api.augmentcode.com%2F
2025-08-14 11:15:20 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:15:20 - INFO - 添加session: <EMAIL>   8d314568f153c3cc4f8400dfb130da3f4b4f6a54e359b31d1e99187fd83f563a   https://d17.api.augmentcode.com/  2025-08-21T03:14:44Z
2025-08-14 11:15:20 - INFO - email:<EMAIL> === cookie:.eJxNjUFuwzAMBP_Cs11YESWaOeUnhizShVBLThQnQNH27xWMHHLcxc7sD0xXrTkULTuc9_rQDpaQ0_o9lZAVzgAdfKanlrec5Do97lqnJK3QHNL660cWh-OC1si4nCyJnck4avOyldhIj-TREFlPzMiOBuzg0ByGZlpv6SaGnDNoTmgv9xzqLlv80vqxlTUVhRdxHDeBZ3bcz-RMj3HmnkWpjy4shimKDAP8_QN7e0SQ.aJ1Uxw.cU8cQglp-zbPpSzkKonGW_cWMZ4
2025-08-14 11:15:20 - INFO - 
自动化流程成功完成！
2025-08-14 11:15:20 - INFO - HAR 文件已保存: har_files\success_lqiqd1755141243_smartdocker.online_20250814_111520.har
2025-08-14 11:15:20 - INFO - 捕获了 151 个网络请求
2025-08-14 11:15:20 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:15:20 - INFO - WebSocket 摘要已保存: websocket_logs\success_lqiqd1755141243_smartdocker.online_20250814_111520_ws.json
2025-08-14 11:15:20 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 11:15:20 - INFO - 添加第1个
2025-08-14 11:15:43 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62683,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:15:43 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:15:43 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:15:43 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:15:43 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:15:43 - INFO - 记录了 34 个页面资源
2025-08-14 11:15:43 - INFO - 页面支持 WebSocket
2025-08-14 11:15:43 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:15:43 - INFO - <EMAIL>
2025-08-14 11:15:43 - INFO - 网络捕获: 主函数开始
2025-08-14 11:15:43 - INFO - 记录了 34 个页面资源
2025-08-14 11:15:43 - INFO - 页面支持 WebSocket
2025-08-14 11:15:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:16:00 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:16:04 - INFO - 找到 Turnstile...
2025-08-14 11:16:06 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:16:09 - INFO - 网络捕获: 登录完成后
2025-08-14 11:16:09 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBLeGVSZHF6QXpWZlFPMFVnaFl5aE5vWDNtZG5RMUpFVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVCVnVueDFFRHlkeEYwTG1MX2ZfMy16bnNlTEI2MFREo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:16:09 - INFO - 记录了 20 个页面资源
2025-08-14 11:16:09 - INFO - 页面支持 WebSocket
2025-08-14 11:16:09 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:16:09 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:16:09 - INFO - CDP:启用节流模式
2025-08-14 11:16:09 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:16:09 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:16:09 - INFO - 记录了 20 个页面资源
2025-08-14 11:16:09 - INFO - 页面支持 WebSocket
2025-08-14 11:16:09 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:16:24 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:16:24 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:16:24 - INFO - ReCAPTCHA Token: 
2025-08-14 11:16:24 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:16:55 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 11:16:55 - INFO - 鼠标左键放开操作完成。
2025-08-14 11:16:55 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:16:55 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:16:55 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:16:55 - INFO - 记录了 106 个页面资源
2025-08-14 11:16:55 - INFO - 页面支持 WebSocket
2025-08-14 11:16:55 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:16:55 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:16:55 - INFO - 成功！当前在订阅页面。
2025-08-14 11:16:55 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:16:55 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:16:58 - INFO - 代码信息: {
  "codeVerifier": "pySFMLzCRHe6NIbnYBdGtNiogNHifm5Uof_h3cDK0EU",
  "code_challenge": "rY6Yi5-KwnV5NMxRdlHZeYfLrFDj113QymeVm1M1_GM",
  "state": "ae7439b6-c22a-484d-b575-3bd8192a0136"
}
2025-08-14 11:16:58 - INFO - ==================================================
2025-08-14 11:16:58 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_99909bf5015ffc5189f63dde21140a68&state=ae7439b6-c22a-484d-b575-3bd8192a0136&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-14 11:17:00 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:17:00 - INFO - 添加session: <EMAIL>   9bf7dd4539c83a847675ad98d68d26e81feb69b92c33efe03d2a20fb586964b2   https://d5.api.augmentcode.com/  2025-08-21T03:16:28Z
2025-08-14 11:17:00 - INFO - email:<EMAIL> === cookie:.eJxNjc1OwzAQhN9lz0nln7XX7ok3idx4gwzxJnVTpAp4d0wvcJsZzXzzCdPOrSZhOeB8tDsPsKRa1sckqTKcAQZ4LR8s_3zJ-3S_cZtK7gHXVNYvH2J2uHi0OofFWMqIOEfuddlk7ksK6EMgRy5i8MoqFQd4Yp6ETtrfrg_R5JxGbW18udXUjrzN79xOm6xFfml_x8ooIk15dEHFEbscLzHR6Ge7sDFsczDw_QOR0ERB.aJ1VLA.j_YEGCbJqqNUC5WgTrzt12ckE8g
2025-08-14 11:17:00 - INFO - 
自动化流程成功完成！
2025-08-14 11:17:00 - INFO - HAR 文件已保存: har_files\success_pjqyn1755141339_smartdocker.online_20250814_111700.har
2025-08-14 11:17:00 - INFO - 捕获了 151 个网络请求
2025-08-14 11:17:00 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:17:00 - INFO - WebSocket 摘要已保存: websocket_logs\success_pjqyn1755141339_smartdocker.online_20250814_111700_ws.json
2025-08-14 11:17:00 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 11:17:00 - INFO - 添加第2个
2025-08-14 11:17:23 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62852,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:17:23 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:17:23 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:17:23 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:17:23 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:17:23 - INFO - 记录了 34 个页面资源
2025-08-14 11:17:23 - INFO - 页面支持 WebSocket
2025-08-14 11:17:23 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:17:23 - INFO - <EMAIL>
2025-08-14 11:17:23 - INFO - 网络捕获: 主函数开始
2025-08-14 11:17:23 - INFO - 记录了 34 个页面资源
2025-08-14 11:17:23 - INFO - 页面支持 WebSocket
2025-08-14 11:17:23 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:17:33 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:17:37 - INFO - 找到 Turnstile...
2025-08-14 11:17:39 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:17:42 - INFO - 网络捕获: 登录完成后
2025-08-14 11:17:42 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SA3aWhpVzF5S1BMWElFdXA4Nm1rX1NXR19WT1FRYmlZN6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGNPa0hsVkk3SGI0aUFkbDZqcUdqUzZwLVlvWk9vSy1Jo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:17:42 - INFO - 记录了 20 个页面资源
2025-08-14 11:17:42 - INFO - 页面支持 WebSocket
2025-08-14 11:17:42 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:17:42 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:17:42 - INFO - CDP:启用节流模式
2025-08-14 11:17:56 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:17:56 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:17:56 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=OSA7LJLefED8lWXpl_5F4IJuoIiXtztqNU7tIZk703I&code_challenge=koISbuOvjdCDrYCjbqr5Xo-JoH_fBRpLckJ5bwQJEcc&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:17:56 - INFO - 记录了 19 个页面资源
2025-08-14 11:17:56 - INFO - 页面支持 WebSocket
2025-08-14 11:17:56 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:17:56 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:17:56 - INFO - ReCAPTCHA Token: 
2025-08-14 11:17:56 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:18:00 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:18:00 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:18:00 - INFO - 记录了 3 个页面资源
2025-08-14 11:18:00 - INFO - 页面支持 WebSocket
2025-08-14 11:18:00 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:18:00 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:18:03 - INFO - 网络捕获: 注册尝试后
2025-08-14 11:18:03 - INFO - 记录了 3 个页面资源
2025-08-14 11:18:03 - INFO - 页面支持 WebSocket
2025-08-14 11:18:03 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 11:18:03 - INFO - 网络捕获: 定期捕获
2025-08-14 11:18:03 - INFO - 记录了 3 个页面资源
2025-08-14 11:18:03 - INFO - 页面支持 WebSocket
2025-08-14 11:18:33 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 11:18:33 - INFO - HAR 文件已保存: har_files\error_jmxmn1755141439_smartdocker.online_20250814_111833.har
2025-08-14 11:18:33 - INFO - 捕获了 69 个网络请求
2025-08-14 11:18:33 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:18:33 - INFO - WebSocket 摘要已保存: websocket_logs\error_jmxmn1755141439_smartdocker.online_20250814_111833_ws.json
2025-08-14 11:18:36 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62986,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:18:36 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:18:36 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:18:36 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:18:36 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:18:36 - INFO - 记录了 34 个页面资源
2025-08-14 11:18:36 - INFO - 页面支持 WebSocket
2025-08-14 11:18:36 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:18:36 - INFO - <EMAIL>
2025-08-14 11:18:36 - INFO - 网络捕获: 主函数开始
2025-08-14 11:18:36 - INFO - 记录了 34 个页面资源
2025-08-14 11:18:36 - INFO - 页面支持 WebSocket
2025-08-14 11:18:36 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:18:51 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:18:54 - INFO - 找到 Turnstile...
2025-08-14 11:18:54 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:18:57 - INFO - 网络捕获: 登录完成后
2025-08-14 11:18:57 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBsVGZnbmRmc3F0c1RwVDg4SVJfaGh4eDl5aWs1NXZVSaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIERZZzR4NjlGS0JYc1FPcWxOWDc1cWF4ZlcxWHB3ZThDo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:18:57 - INFO - 记录了 21 个页面资源
2025-08-14 11:18:57 - INFO - 页面支持 WebSocket
2025-08-14 11:18:57 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:18:58 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:18:58 - INFO - CDP:启用节流模式
2025-08-14 11:18:58 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:18:58 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:19:12 - INFO - 记录了 21 个页面资源
2025-08-14 11:19:12 - INFO - 页面支持 WebSocket
2025-08-14 11:19:12 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:19:12 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:19:12 - INFO - ReCAPTCHA Token: 
2025-08-14 11:19:12 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:19:18 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:19:18 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:19:18 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=LnkQwLtuJJEIoIg60THig6nWIMzGhSbixeVd438QVMA&code_challenge=RcIVHzqZ2MP-r4b3593EWpmcYlbrIx82iGzWYPfCxq8&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 11:19:18 - INFO - 记录了 3 个页面资源
2025-08-14 11:19:18 - INFO - 页面支持 WebSocket
2025-08-14 11:19:18 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:19:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:19:21 - INFO - 网络捕获: 注册尝试后
2025-08-14 11:19:21 - INFO - 记录了 3 个页面资源
2025-08-14 11:19:21 - INFO - 页面支持 WebSocket
2025-08-14 11:19:21 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 11:19:21 - INFO - 网络捕获: 定期捕获
2025-08-14 11:19:21 - INFO - 记录了 3 个页面资源
2025-08-14 11:19:21 - INFO - 页面支持 WebSocket
2025-08-14 11:19:51 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 11:19:54 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63139,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:19:54 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:19:54 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:19:54 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:19:55 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:19:55 - INFO - 记录了 34 个页面资源
2025-08-14 11:19:55 - INFO - 页面支持 WebSocket
2025-08-14 11:19:55 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:19:55 - INFO - <EMAIL>
2025-08-14 11:19:55 - INFO - 网络捕获: 主函数开始
2025-08-14 11:19:55 - INFO - 记录了 34 个页面资源
2025-08-14 11:19:55 - INFO - 页面支持 WebSocket
2025-08-14 11:19:55 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:20:04 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:20:08 - INFO - 找到 Turnstile...
2025-08-14 11:20:10 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:20:13 - INFO - 网络捕获: 登录完成后
2025-08-14 11:20:13 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBoMGVZUE93SjRQUHVnc2lpUG9LYnozc0FBS3I0Sk9yQqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHZSbWt5cEZPZ25mdDhnZldpei1wcGpVYmJSejZhVTRoo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:20:13 - INFO - 记录了 21 个页面资源
2025-08-14 11:20:13 - INFO - 页面支持 WebSocket
2025-08-14 11:20:13 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:20:13 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:20:13 - INFO - CDP:启用节流模式
2025-08-14 11:20:27 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:20:27 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:20:27 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=tcy-zVoi2KIEyEyV7AVHh41wEeTl93tQOoyqwp0so0E&code_challenge=hAx7vsUWjgDMV1Fau5Bpn1fOrYUo-si1woPgfUQOdaU&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:20:27 - INFO - 记录了 19 个页面资源
2025-08-14 11:20:27 - INFO - 页面支持 WebSocket
2025-08-14 11:20:27 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:20:27 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:20:27 - INFO - ReCAPTCHA Token: 
2025-08-14 11:20:27 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:20:58 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 11:20:58 - INFO - 鼠标左键放开操作完成。
2025-08-14 11:20:58 - INFO - 鼠标左键点击页面操作完成。
2025-08-14 11:20:59 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-14 11:21:02 - INFO - 验证成功，已进入目标页面。
2025-08-14 11:21:02 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:21:02 - INFO - 记录了 3 个页面资源
2025-08-14 11:21:02 - INFO - 页面支持 WebSocket
2025-08-14 11:21:02 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:21:02 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:21:05 - INFO - 网络捕获: 注册尝试后
2025-08-14 11:21:05 - INFO - 记录了 3 个页面资源
2025-08-14 11:21:05 - INFO - 页面支持 WebSocket
2025-08-14 11:21:05 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 11:21:05 - INFO - 网络捕获: 定期捕获
2025-08-14 11:21:05 - INFO - 记录了 3 个页面资源
2025-08-14 11:21:05 - INFO - 页面支持 WebSocket
2025-08-14 11:21:35 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 11:21:38 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63289,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:21:38 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:21:38 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:21:38 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:21:38 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:21:38 - INFO - 记录了 34 个页面资源
2025-08-14 11:21:38 - INFO - 页面支持 WebSocket
2025-08-14 11:21:38 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:21:38 - INFO - <EMAIL>
2025-08-14 11:21:38 - INFO - 网络捕获: 主函数开始
2025-08-14 11:21:38 - INFO - 记录了 34 个页面资源
2025-08-14 11:21:38 - INFO - 页面支持 WebSocket
2025-08-14 11:21:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:21:49 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:21:53 - INFO - 找到 Turnstile...
2025-08-14 11:21:54 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:21:57 - INFO - 网络捕获: 登录完成后
2025-08-14 11:21:57 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBmRGFfNm8xRm9yUVJxejlZbWR4cWdsdXRfSzU0a0lHMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDNLLWc1WDBOZ0RkQWdWTEc5VzlfbGNibkNJN1ctT2Ewo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:21:57 - INFO - 记录了 20 个页面资源
2025-08-14 11:21:58 - INFO - 页面支持 WebSocket
2025-08-14 11:21:58 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:21:58 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:21:58 - INFO - CDP:启用节流模式
2025-08-14 11:22:12 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:22:12 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:22:12 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=rYIv637ogKF2k392mZ96Fci7FdZROgb-E9R5oFp5qRk&code_challenge=CRNxCgyhS1rTbqBtanleDkJprqzvLr8vpMyyQpCX-oI&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:22:12 - INFO - 记录了 19 个页面资源
2025-08-14 11:22:12 - INFO - 页面支持 WebSocket
2025-08-14 11:22:12 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:22:12 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:22:12 - INFO - ReCAPTCHA Token: 
2025-08-14 11:22:12 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:22:25 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:22:25 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:22:25 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:22:31 - INFO - 记录了 94 个页面资源
2025-08-14 11:22:31 - INFO - 页面支持 WebSocket
2025-08-14 11:22:31 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:22:31 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:22:31 - INFO - 成功！当前在订阅页面。
2025-08-14 11:22:31 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:22:31 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:22:34 - INFO - 代码信息: {
  "codeVerifier": "n6lRIZa485kj5ATElFlrtIePrcGZpKfR4gI95njr6-Y",
  "code_challenge": "oTfNail6ktSIY28o4KxFkEWoRBV-77UC3CShGXD-MHI",
  "state": "5b41b8eb-f488-4233-bc49-ed923c14692e"
}
2025-08-14 11:22:34 - INFO - ==================================================
2025-08-14 11:22:35 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_953593d402ce5618861b9c46ff6f297e&state=5b41b8eb-f488-4233-bc49-ed923c14692e&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-14 11:22:37 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:22:37 - INFO - 添加session: <EMAIL>   65968f8585e76360bc3ad9db6d4837f8fc07acda01e5673aa4a88fe382b49942   https://d9.api.augmentcode.com/  2025-08-21T03:22:15Z
2025-08-14 11:22:37 - INFO - email:<EMAIL> === cookie:.eJxNjctOwzAQRf9l1kmVsWf86Io_iRx7ggyxU7kpAhX-HStiwfK-zn3CfJNWQpV6wPVoDxlgDSVvX3MNReAKMMBr_pD6T-d0mx93aXNO3ZAS8vZtnE_MrEljcqvSNlGcMCy9Xvca-1KRQ-eNVk5ZQvTK8QAn5iR00lv5LBUtMxKS9i_3EtqR9vgu7bLXLVeBv8V5bJZpjYp4ZE80klvs6KZkRmZreOkhY4SfX4aYREY.aJ1WfA.f7XTsV0fA9vxdfWDrotg0Z8GtIo
2025-08-14 11:22:37 - INFO - 
自动化流程成功完成！
2025-08-14 11:22:37 - INFO - 添加第3个
2025-08-14 11:22:52 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63457,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:22:52 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:22:52 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:22:52 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:22:52 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:22:52 - INFO - 记录了 34 个页面资源
2025-08-14 11:22:52 - INFO - 页面支持 WebSocket
2025-08-14 11:22:52 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:22:52 - INFO - <EMAIL>
2025-08-14 11:22:52 - INFO - 网络捕获: 主函数开始
2025-08-14 11:22:52 - INFO - 记录了 34 个页面资源
2025-08-14 11:22:52 - INFO - 页面支持 WebSocket
2025-08-14 11:22:52 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:23:09 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:23:13 - INFO - 找到 Turnstile...
2025-08-14 11:23:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:23:18 - INFO - 网络捕获: 登录完成后
2025-08-14 11:23:18 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SB2dklPcVhfMGFER2dHLU1QdVJRdjJNeTdBcTlwVnBtYqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDhyMGZxRS13RllxR0d3QWNzLTZzYUhhbDVNVklJS1JBo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:23:18 - INFO - 记录了 21 个页面资源
2025-08-14 11:23:18 - INFO - 页面支持 WebSocket
2025-08-14 11:23:18 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:23:18 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:23:18 - INFO - CDP:启用节流模式
2025-08-14 11:23:33 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:23:33 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:23:33 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=I5M0vOG7xff9YRzP8F3-yVE42QCpr5Lzl5uQx6bNKsc&code_challenge=ITNAQK9RD2KNVF5dgoWlKRzCen-6RBtEUCU_IMmKY3w&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:23:33 - INFO - 记录了 19 个页面资源
2025-08-14 11:23:33 - INFO - 页面支持 WebSocket
2025-08-14 11:23:33 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:23:33 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:23:33 - INFO - ReCAPTCHA Token: 
2025-08-14 11:23:33 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:23:54 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:23:54 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:23:54 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:23:54 - INFO - 记录了 103 个页面资源
2025-08-14 11:23:54 - INFO - 页面支持 WebSocket
2025-08-14 11:23:54 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:23:54 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:23:54 - INFO - 成功！当前在订阅页面。
2025-08-14 11:23:54 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:23:54 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:23:57 - INFO - 代码信息: {
  "codeVerifier": "_4TcOeagYkacosDWis6B9xMDL5AjIL0CLOo76M2Uefg",
  "code_challenge": "4Z9npifVQ89ZlxMIuqdte-Ae21RGji_E5fNyCf2I868",
  "state": "2cdd6aeb-ce7d-4d27-973e-c3773054bbf0"
}
2025-08-14 11:23:57 - INFO - ==================================================
2025-08-14 11:23:58 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_65fa4ef483544a8387c633ecced197ab&state=2cdd6aeb-ce7d-4d27-973e-c3773054bbf0&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-14 11:24:00 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:24:00 - INFO - 添加session: <EMAIL>   30d1df625d55aeed83f3de4d21811cb07193b86999cfc9ad679cb3bf5b1d1b12   https://d8.api.augmentcode.com/  2025-08-21T03:23:37Z
2025-08-14 11:24:00 - INFO - email:<EMAIL> === cookie:.eJxNjc0KwjAQhN9lz61ks_mrJ9-kBHcrwSaV2AqivruhePA4w3zfvGC8Sc2xSFnhuNZNOphiTvNzLDELHAE6uKSHlL-c-DZud6lj4lZIjml-uzCwdZEMIYdJk2cXAk2xzctSzo1EIlTolcKglLKkte1g1-yGZkrbujB6a9Ggd-F0z7GuvJyvUg9LmVMR-BH7sTbktBuGHp2xvbGIfWgfPZPxyI7UNAh8vmLkQzc.aJ1Wzw.f2avybeOWu3jKfNKxeEtDiROu8s
2025-08-14 11:24:00 - INFO - 
自动化流程成功完成！
2025-08-14 11:24:00 - INFO - HAR 文件已保存: har_files\success_iutod1755141768_smartdocker.online_20250814_112400.har
2025-08-14 11:24:00 - INFO - 捕获了 161 个网络请求
2025-08-14 11:24:00 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:24:00 - INFO - WebSocket 摘要已保存: websocket_logs\success_iutod1755141768_smartdocker.online_20250814_112400_ws.json
2025-08-14 11:24:00 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 11:24:00 - INFO - 添加第4个
2025-08-14 11:24:21 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63700,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:24:22 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:24:22 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:24:22 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:24:22 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:24:22 - INFO - 记录了 34 个页面资源
2025-08-14 11:24:22 - INFO - 页面支持 WebSocket
2025-08-14 11:24:22 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:24:22 - INFO - <EMAIL>
2025-08-14 11:24:22 - INFO - 网络捕获: 主函数开始
2025-08-14 11:24:22 - INFO - 记录了 34 个页面资源
2025-08-14 11:24:22 - INFO - 页面支持 WebSocket
2025-08-14 11:24:22 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:24:32 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:24:36 - INFO - 找到 Turnstile...
2025-08-14 11:24:38 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:24:41 - INFO - 网络捕获: 登录完成后
2025-08-14 11:24:41 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBXb2JvbGVROF9ZcDNpeVYzdmNWNF9HQ0pfOWVFXy1jWaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDBqMW1JSEdHM3JNN3V0OFlWTjJFeFk2TEdWOU5FUFVho2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:24:41 - INFO - 记录了 21 个页面资源
2025-08-14 11:24:41 - INFO - 页面支持 WebSocket
2025-08-14 11:24:41 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:24:41 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:24:41 - INFO - CDP:启用节流模式
2025-08-14 11:24:41 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:24:41 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:24:41 - INFO - 记录了 21 个页面资源
2025-08-14 11:24:41 - INFO - 页面支持 WebSocket
2025-08-14 11:24:55 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:24:55 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:24:55 - INFO - ReCAPTCHA Token: 
2025-08-14 11:24:55 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:25:15 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:25:15 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:25:15 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=Sx6q3Nnn1XHwSQSCkoA7uZ1V4f0I-IV7lzQATts0FT8&code_challenge=1JTELnK0KMZbbKsqs52HfVxvMoGK5dNMyl7yjCYFIQA&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 11:25:15 - INFO - 记录了 3 个页面资源
2025-08-14 11:25:15 - INFO - 页面支持 WebSocket
2025-08-14 11:25:15 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:25:15 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:25:18 - INFO - 网络捕获: 注册尝试后
2025-08-14 11:25:18 - INFO - 记录了 3 个页面资源
2025-08-14 11:25:18 - INFO - 页面支持 WebSocket
2025-08-14 11:25:18 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 11:25:18 - INFO - 网络捕获: 定期捕获
2025-08-14 11:25:18 - INFO - 记录了 3 个页面资源
2025-08-14 11:25:18 - INFO - 页面支持 WebSocket
2025-08-14 11:25:48 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 11:25:48 - INFO - HAR 文件已保存: har_files\error_ogfin1755141858_smartdocker.online_20250814_112548.har
2025-08-14 11:25:48 - INFO - 捕获了 61 个网络请求
2025-08-14 11:25:48 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:25:48 - INFO - WebSocket 摘要已保存: websocket_logs\error_ogfin1755141858_smartdocker.online_20250814_112548_ws.json
2025-08-14 11:25:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63859,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:25:51 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:25:51 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:25:51 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:25:51 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:25:51 - INFO - 记录了 34 个页面资源
2025-08-14 11:25:51 - INFO - 页面支持 WebSocket
2025-08-14 11:25:51 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:25:51 - INFO - <EMAIL>
2025-08-14 11:25:51 - INFO - 网络捕获: 主函数开始
2025-08-14 11:25:51 - INFO - 记录了 34 个页面资源
2025-08-14 11:25:51 - INFO - 页面支持 WebSocket
2025-08-14 11:25:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:26:03 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:26:07 - INFO - 找到 Turnstile...
2025-08-14 11:26:07 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:26:10 - INFO - 网络捕获: 登录完成后
2025-08-14 11:26:10 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SByU0o3VjB2V0hhSU5JTmRSX2lpTHc1T1FqU2F5dHBJeKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDlaZDdvQjkzN0k3b2dMZ2tRMHdkcFVUenlMUDhlaUtro2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:26:10 - INFO - 记录了 21 个页面资源
2025-08-14 11:26:10 - INFO - 页面支持 WebSocket
2025-08-14 11:26:10 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:26:10 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:26:10 - INFO - CDP:启用节流模式
2025-08-14 11:26:10 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:26:10 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:26:10 - INFO - 记录了 21 个页面资源
2025-08-14 11:26:10 - INFO - 页面支持 WebSocket
2025-08-14 11:26:10 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:26:26 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:26:26 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:26:26 - INFO - ReCAPTCHA Token: 
2025-08-14 11:26:26 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:26:44 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:26:44 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:26:44 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:26:44 - INFO - 记录了 104 个页面资源
2025-08-14 11:26:44 - INFO - 页面支持 WebSocket
2025-08-14 11:26:44 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:26:44 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:26:44 - INFO - 成功！当前在订阅页面。
2025-08-14 11:26:44 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:26:44 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:26:47 - INFO - 代码信息: {
  "codeVerifier": "GsrN1YYpNT8JDYmZqqIJYkUt6jI5xpj7ObY7ByWXktU",
  "code_challenge": "yDbCQrfaCZya3rgDHJjBtide6bhNDaFVbHdcQgeAytY",
  "state": "5a9deff4-72c2-4314-95ad-2ca4cec87e2d"
}
2025-08-14 11:26:47 - INFO - ==================================================
2025-08-14 11:26:48 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_f468de6467f87ce56602393b1cb342df&state=5a9deff4-72c2-4314-95ad-2ca4cec87e2d&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-14 11:26:54 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:26:54 - INFO - 添加session: <EMAIL>   7557ed86b31d76938ee4fabf4fcd7c8374b0bc7a62017d8fa501b600fdffdfdf   https://d12.api.augmentcode.com/  2025-08-21T03:26:29Z
2025-08-14 11:26:54 - INFO - email:<EMAIL> === cookie:.eJxNjUFuwzAMBP_Cs11YlkjTOeUnhiJSgVCLDhSnQNH27zWMHnrcxc7sFywPbTWa2g6Xvb20gxxrWT8Xi1XhAtDBvXyo_ctFHsvrqW0pchRaY1m_iWdByhS8E86jn4RyyOKOuW2WDtKNhGFipNnjMDmieezg1JyGw7TdczE3IbrgGPn6rLHtsqV3bW-brcUU_ojzOCuyBJd6PwyxDyS3nily72m-peQDk0f4-QW3pUTZ.aJ1XeQ.lv-_reSzOVgy-vBxyC8ERp267b8
2025-08-14 11:26:54 - INFO - 
自动化流程成功完成！
2025-08-14 11:26:54 - INFO - 添加第5个
2025-08-14 11:27:17 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64096,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:27:17 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:27:17 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:27:17 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:27:17 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:27:17 - INFO - 记录了 34 个页面资源
2025-08-14 11:27:17 - INFO - 页面支持 WebSocket
2025-08-14 11:27:17 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:27:17 - INFO - <EMAIL>
2025-08-14 11:27:17 - INFO - 网络捕获: 主函数开始
2025-08-14 11:27:17 - INFO - 记录了 34 个页面资源
2025-08-14 11:27:17 - INFO - 页面支持 WebSocket
2025-08-14 11:27:17 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:27:28 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:27:32 - INFO - 找到 Turnstile...
2025-08-14 11:27:34 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:27:37 - INFO - 网络捕获: 登录完成后
2025-08-14 11:27:37 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBKQTl3MHh0MkRmVTdoLTV6Ym5PZ0U0X09iTnZFNTlhMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFhBdDJVdzJ0QlloUXZLUjk1YVg2QnpCWnRMdE40cXY3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:27:37 - INFO - 记录了 21 个页面资源
2025-08-14 11:27:37 - INFO - 页面支持 WebSocket
2025-08-14 11:27:37 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:27:37 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:27:37 - INFO - CDP:启用节流模式
2025-08-14 11:27:52 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:27:52 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:27:52 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=Atx2lPUsuALzQOuw8kluMh5P11RFtBZvgeqT8kVRaCU&code_challenge=fEt9slcNludT3SXI9hPzXEnGoDAJJdbZClExJBca9KA&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:27:52 - INFO - 记录了 19 个页面资源
2025-08-14 11:27:52 - INFO - 页面支持 WebSocket
2025-08-14 11:27:52 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:27:52 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:27:52 - INFO - ReCAPTCHA Token: 
2025-08-14 11:27:52 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:28:11 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:28:11 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:28:11 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:28:11 - INFO - 记录了 103 个页面资源
2025-08-14 11:28:11 - INFO - 页面支持 WebSocket
2025-08-14 11:28:11 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:28:11 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:28:11 - INFO - 成功！当前在订阅页面。
2025-08-14 11:28:11 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:28:11 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:28:14 - INFO - 代码信息: {
  "codeVerifier": "WJ1RnX07Mg-gMEnCIuc7P1kb9ylLXiJIZwAeQ769Osk",
  "code_challenge": "yDJicCYOS1zuTBD5pZoFV3hWqNAjcqN2EKBZy7J_sWc",
  "state": "64df445c-45cc-4194-bb9d-e59499d94fa6"
}
2025-08-14 11:28:14 - INFO - ==================================================
2025-08-14 11:28:15 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_38088c03ff85f807cf67e0f29fdf4528&state=64df445c-45cc-4194-bb9d-e59499d94fa6&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-14 11:28:16 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:28:16 - INFO - 添加session: <EMAIL>   514afb1ad6034470c002a5b127e3064aaa8a786a412dbfd292e1ef0e36a4091d   https://d12.api.augmentcode.com/  2025-08-21T03:27:55Z
2025-08-14 11:28:16 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2DoH205-SZkYRfTQAspYGLUd5egB48zme-bJ7QL54iJ0wbNlncuYMAYpkebMDI0AAXcwp3TXw60tPvKuQ10FBwxTK_aeTIWa60EuUEqS5a8d_KYpzn1BymryipnnRbeS1dbUYkCTs1pOExDwGUU1hihZaXUdY2YN5r7kfNlTlNIDD_ie-yMp05gKWuvS92ZvuzIcokCB6W6XjFpeH8Ah11FSA.aJ1X0A.seI_IZmDvrno4hF2LDoMfgFhNbk
2025-08-14 11:28:16 - INFO - 
自动化流程成功完成！
2025-08-14 11:28:16 - INFO - HAR 文件已保存: har_files\success_fiapk1755142033_smartdocker.online_20250814_112816.har
2025-08-14 11:28:16 - INFO - 捕获了 161 个网络请求
2025-08-14 11:28:16 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:28:16 - INFO - WebSocket 摘要已保存: websocket_logs\success_fiapk1755142033_smartdocker.online_20250814_112816_ws.json
2025-08-14 11:28:16 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 11:28:16 - INFO - 添加第6个
2025-08-14 11:28:37 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64266,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:28:38 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:28:38 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:28:38 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:28:38 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:28:38 - INFO - 记录了 34 个页面资源
2025-08-14 11:28:38 - INFO - 页面支持 WebSocket
2025-08-14 11:28:38 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:28:38 - INFO - <EMAIL>
2025-08-14 11:28:38 - INFO - 网络捕获: 主函数开始
2025-08-14 11:28:38 - INFO - 记录了 34 个页面资源
2025-08-14 11:28:38 - INFO - 页面支持 WebSocket
2025-08-14 11:28:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:28:48 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:28:52 - INFO - 找到 Turnstile...
2025-08-14 11:28:52 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:28:55 - INFO - 网络捕获: 登录完成后
2025-08-14 11:28:55 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:28:55 - INFO - 记录了 21 个页面资源
2025-08-14 11:28:55 - INFO - 页面支持 WebSocket
2025-08-14 11:28:55 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:28:56 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:28:56 - INFO - CDP:启用节流模式
2025-08-14 11:28:56 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:28:56 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:28:56 - INFO - 记录了 21 个页面资源
2025-08-14 11:28:56 - INFO - 页面支持 WebSocket
2025-08-14 11:28:56 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:29:09 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:29:09 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:29:09 - INFO - ReCAPTCHA Token: 
2025-08-14 11:29:09 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:29:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:29:22 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:29:22 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:29:24 - INFO - 记录了 98 个页面资源
2025-08-14 11:29:24 - INFO - 页面支持 WebSocket
2025-08-14 11:29:24 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:29:24 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:29:24 - INFO - 成功！当前在订阅页面。
2025-08-14 11:29:24 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:29:24 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:29:27 - INFO - 代码信息: {
  "codeVerifier": "pYeNUeluKqTxqSHlman8xSR62bwYDnwyG7IVrVsR5BQ",
  "code_challenge": "yjAoZ0uYfHsawb9LcGewSwkR7B3i0GW3SFAWzyTl7eg",
  "state": "7e72634b-3f82-4a85-9ee7-8230394c300f"
}
2025-08-14 11:29:27 - INFO - ==================================================
2025-08-14 11:29:28 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_76deaab1f10c91b8d963acf945c68f24&state=7e72634b-3f82-4a85-9ee7-8230394c300f&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-14 11:29:31 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:29:31 - INFO - 添加session: <EMAIL>   1e75a6da5dd43ae51466bef67571e7618a1176686344b38d2a61e8b6aa97660e   https://d6.api.augmentcode.com/  2025-08-21T03:29:13Z
2025-08-14 11:29:31 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtV2E-tHKqjcxWJNqhVqyoThF0s_dYxhZdDmDeW9-YFy1Fa5aNzhv7aodJC55vo-Vi8IZoIP3_KX1X86yjteLtjHLXmjhPP-GIYqnFJxFGZKxJIMzJp72eV3qtJOB0ATryFFAY7wPsYPDcgh2kXyst28k79EZRPd6Kdw2WaZPbS9LnXNVeBLHL0e2yZ6o54modyzYv4n3_YSJgkaf0An8PQA7JkSp.aJ1YGQ.HTyOJjWAWGbLosNwqp_BQuxJSaU
2025-08-14 11:29:31 - INFO - 
自动化流程成功完成！
2025-08-14 11:29:31 - INFO - HAR 文件已保存: har_files\success_dhpxz1755142114_smartdocker.online_20250814_112931.har
2025-08-14 11:29:31 - INFO - 捕获了 145 个网络请求
2025-08-14 11:29:31 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:29:31 - INFO - WebSocket 摘要已保存: websocket_logs\success_dhpxz1755142114_smartdocker.online_20250814_112931_ws.json
2025-08-14 11:29:31 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 11:29:31 - INFO - 添加第7个
2025-08-14 11:29:52 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64428,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:29:52 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:29:52 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:29:52 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:29:52 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:29:52 - INFO - 记录了 34 个页面资源
2025-08-14 11:29:52 - INFO - 页面支持 WebSocket
2025-08-14 11:29:52 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:29:52 - INFO - <EMAIL>
2025-08-14 11:29:52 - INFO - 网络捕获: 主函数开始
2025-08-14 11:29:52 - INFO - 记录了 34 个页面资源
2025-08-14 11:29:52 - INFO - 页面支持 WebSocket
2025-08-14 11:29:52 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:30:02 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:30:06 - INFO - 找到 Turnstile...
2025-08-14 11:30:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:30:11 - INFO - 网络捕获: 登录完成后
2025-08-14 11:30:11 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBVRENDYzRYc0c3WlE4SkV2aG1HajF1UnotcFpLRm1YTaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFJMejVVaS1JaVVsekk1bHd4TmdfQnVQc08tRjlQVnk2o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:30:11 - INFO - 记录了 21 个页面资源
2025-08-14 11:30:11 - INFO - 页面支持 WebSocket
2025-08-14 11:30:11 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:30:11 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:30:11 - INFO - CDP:启用节流模式
2025-08-14 11:30:11 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:30:11 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:30:25 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=cDqF4NYUNAZoleLNCn8Pkp4CcyHxUEJdQpUx6DxOd2Y&code_challenge=KUZcbvdb44g7DU34Pw2Qs2eHMym4m7SNuL3O_eAKYjA&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:30:25 - INFO - 记录了 18 个页面资源
2025-08-14 11:30:25 - INFO - 页面支持 WebSocket
2025-08-14 11:30:25 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:30:25 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:30:25 - INFO - ReCAPTCHA Token: 
2025-08-14 11:30:25 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:30:27 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:30:27 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:30:27 - INFO - 记录了 3 个页面资源
2025-08-14 11:30:27 - INFO - 页面支持 WebSocket
2025-08-14 11:30:27 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:30:27 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:30:30 - INFO - 网络捕获: 注册尝试后
2025-08-14 11:30:30 - INFO - 记录了 3 个页面资源
2025-08-14 11:30:30 - INFO - 页面支持 WebSocket
2025-08-14 11:30:30 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 11:30:30 - INFO - 网络捕获: 定期捕获
2025-08-14 11:30:30 - INFO - 记录了 3 个页面资源
2025-08-14 11:30:30 - INFO - 页面支持 WebSocket
2025-08-14 11:31:00 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 11:31:00 - INFO - HAR 文件已保存: har_files\error_nxjac1755142188_smartdocker.online_20250814_113100.har
2025-08-14 11:31:00 - INFO - 捕获了 70 个网络请求
2025-08-14 11:31:00 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:31:00 - INFO - WebSocket 摘要已保存: websocket_logs\error_nxjac1755142188_smartdocker.online_20250814_113100_ws.json
2025-08-14 11:31:03 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64589,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:31:03 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:31:03 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:31:04 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:31:04 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:31:04 - INFO - 记录了 34 个页面资源
2025-08-14 11:31:04 - INFO - 页面支持 WebSocket
2025-08-14 11:31:04 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:31:04 - INFO - <EMAIL>
2025-08-14 11:31:04 - INFO - 网络捕获: 主函数开始
2025-08-14 11:31:04 - INFO - 记录了 34 个页面资源
2025-08-14 11:31:04 - INFO - 页面支持 WebSocket
2025-08-14 11:31:04 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:31:18 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:31:22 - INFO - 找到 Turnstile...
2025-08-14 11:31:24 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:31:27 - INFO - 网络捕获: 登录完成后
2025-08-14 11:31:27 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SAydUVVQnRVUW1GN3dUS3AtNnVqbEJydW14NFluZVlSX6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDdBZ25mTlBqdzRnYnVBbmktZktMeFZGWlhibG5Sd295o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:31:27 - INFO - 记录了 20 个页面资源
2025-08-14 11:31:27 - INFO - 页面支持 WebSocket
2025-08-14 11:31:27 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:31:27 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:31:27 - INFO - CDP:启用节流模式
2025-08-14 11:31:40 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:31:40 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:31:40 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=OAK3N31eLHIF588__GKCUfnAck75YvqwrJUYx0oEfyU&code_challenge=GZOXk4s50DJBnqYZZX3_lqSMJRP1u3v3KWo7p2G_7cs&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:31:40 - INFO - 记录了 19 个页面资源
2025-08-14 11:31:40 - INFO - 页面支持 WebSocket
2025-08-14 11:31:40 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:31:40 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:31:40 - INFO - ReCAPTCHA Token: 
2025-08-14 11:31:40 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:31:50 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:31:50 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:31:50 - INFO - 记录了 3 个页面资源
2025-08-14 11:31:50 - INFO - 页面支持 WebSocket
2025-08-14 11:31:50 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:31:50 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:31:53 - INFO - 网络捕获: 注册尝试后
2025-08-14 11:31:53 - INFO - 记录了 3 个页面资源
2025-08-14 11:31:53 - INFO - 页面支持 WebSocket
2025-08-14 11:31:53 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 11:31:53 - INFO - 网络捕获: 定期捕获
2025-08-14 11:31:53 - INFO - 记录了 3 个页面资源
2025-08-14 11:31:53 - INFO - 页面支持 WebSocket
2025-08-14 11:32:23 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 11:32:26 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64707,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:32:26 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:32:26 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:32:26 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:32:26 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:32:26 - INFO - 记录了 34 个页面资源
2025-08-14 11:32:26 - INFO - 页面支持 WebSocket
2025-08-14 11:32:26 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:32:26 - INFO - <EMAIL>
2025-08-14 11:32:26 - INFO - 网络捕获: 主函数开始
2025-08-14 11:32:26 - INFO - 记录了 34 个页面资源
2025-08-14 11:32:26 - INFO - 页面支持 WebSocket
2025-08-14 11:32:26 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:32:36 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:32:40 - INFO - 找到 Turnstile...
2025-08-14 11:32:42 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:32:45 - INFO - 网络捕获: 登录完成后
2025-08-14 11:32:45 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBDWWpuUmxqdU5obWVyd2RjRFMxMDRNa3dRR3M1T1luR6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEpUSVdyQ1p4ZnRVMkJkSnpwYVZLWnFvRzBaOHBkR3R0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:32:45 - INFO - 记录了 21 个页面资源
2025-08-14 11:32:45 - INFO - 页面支持 WebSocket
2025-08-14 11:32:45 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:32:45 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:32:45 - INFO - CDP:启用节流模式
2025-08-14 11:32:58 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:32:58 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:32:58 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=9cnrfpdeMEySqEZjKY9s8FFFBQQHn36Ypp7fTJbFEsI&code_challenge=GUFHUrrJkmIKqzYKsXUzH53-r3DjWFxdjjFFQNVDEqc&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:32:58 - INFO - 记录了 16 个页面资源
2025-08-14 11:32:58 - INFO - 页面支持 WebSocket
2025-08-14 11:32:58 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:32:58 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:32:58 - INFO - ReCAPTCHA Token: 
2025-08-14 11:32:58 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:33:31 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:33:31 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:33:31 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:33:35 - INFO - 记录了 109 个页面资源
2025-08-14 11:33:35 - INFO - 页面支持 WebSocket
2025-08-14 11:33:35 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:33:35 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:33:35 - INFO - 成功！当前在订阅页面。
2025-08-14 11:33:35 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:33:35 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:33:38 - INFO - 代码信息: {
  "codeVerifier": "Oye0jCHCeiB85vnYzJXekoz_9St5VkHIUuMSHznqOjQ",
  "code_challenge": "utGPtxLX0jWiJtxyu6IemX2RHb9xxcGeOqyoFeRbZcs",
  "state": "830b5ffd-3165-4397-8faf-f0450c0e11d8"
}
2025-08-14 11:33:38 - INFO - ==================================================
2025-08-14 11:33:39 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_bad8e69fa021d9321a7f22aef8494ff7&state=830b5ffd-3165-4397-8faf-f0450c0e11d8&tenant_url=https%3A%2F%2Fd7.api.augmentcode.com%2F
2025-08-14 11:33:41 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:33:41 - INFO - 添加session: <EMAIL>   9008dc37b95350cc17c26f18743129fd53bf26a94d0f2fe8559814b81c76ff42   https://d7.api.augmentcode.com/  2025-08-21T03:33:02Z
2025-08-14 11:33:41 - INFO - email:<EMAIL> === cookie:.eJxNjc1ugzAQhN9lz1DZ6x9sTnkTtLKXyi1ekEOiRG3fPQjl0OOM5vvmB6aNWyVh2WHc2407mKmW5TkJVYYRoIPPcmf5l0veptuV21TyUXClsvz6ELMLVlmjc5jRDDmQj6iOuaySDtIb470zNsaI2ns16A5Oyyk4RPL4oqQH57RFHcLlWqnteU3f3D5WWYowvInzV6tZJWNyz2SotwljHxKG3s_sMjpGxgR_Ly2oRIs.aJ1ZFA.F7FQ_Vh3tZojNRVCL4_amP7zYss
2025-08-14 11:33:41 - INFO - 
自动化流程成功完成！
2025-08-14 11:33:41 - INFO - 添加第8个
2025-08-14 11:33:55 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64874,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:33:56 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:33:56 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:33:56 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:33:56 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:33:56 - INFO - 记录了 34 个页面资源
2025-08-14 11:33:56 - INFO - 页面支持 WebSocket
2025-08-14 11:33:56 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:33:56 - INFO - <EMAIL>
2025-08-14 11:33:56 - INFO - 网络捕获: 主函数开始
2025-08-14 11:33:56 - INFO - 记录了 34 个页面资源
2025-08-14 11:33:56 - INFO - 页面支持 WebSocket
2025-08-14 11:33:56 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:34:05 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:34:09 - INFO - 找到 Turnstile...
2025-08-14 11:34:10 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:34:13 - INFO - 网络捕获: 登录完成后
2025-08-14 11:34:13 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBidENGTzhZQjJDTmtlZDU4UUFOTF9pQWRTbzVhNjNWZqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDFDUFVmcWVOaVZsblYtcjl0TE93YUFDRW9Pby1OWkE0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:34:13 - INFO - 记录了 21 个页面资源
2025-08-14 11:34:13 - INFO - 页面支持 WebSocket
2025-08-14 11:34:13 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:34:13 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:34:13 - INFO - CDP:启用节流模式
2025-08-14 11:34:13 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:34:13 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:34:13 - INFO - 记录了 21 个页面资源
2025-08-14 11:34:13 - INFO - 页面支持 WebSocket
2025-08-14 11:34:13 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:34:13 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:34:27 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:34:27 - INFO - ReCAPTCHA Token: 
2025-08-14 11:34:27 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:34:33 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:34:33 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:34:33 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=cqTTnJYeWnY3ONXUAL8uij4txtcnfwMNW3Hc51X8US4&code_challenge=O1PzV7ebrWFaZp5oVth03ShfQPFsYbMT1_B-CI3id-Y&code_challenge_method=S256 (标题: Augment Login)
2025-08-14 11:34:33 - INFO - 记录了 3 个页面资源
2025-08-14 11:34:33 - INFO - 页面支持 WebSocket
2025-08-14 11:34:33 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:34:33 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:34:36 - INFO - 网络捕获: 注册尝试后
2025-08-14 11:34:36 - INFO - 记录了 3 个页面资源
2025-08-14 11:34:36 - INFO - 页面支持 WebSocket
2025-08-14 11:34:36 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 11:34:36 - INFO - 网络捕获: 定期捕获
2025-08-14 11:34:36 - INFO - 记录了 3 个页面资源
2025-08-14 11:34:36 - INFO - 页面支持 WebSocket
2025-08-14 11:35:06 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 11:35:06 - INFO - HAR 文件已保存: har_files\error_pqkre1755142432_smartdocker.online_20250814_113506.har
2025-08-14 11:35:06 - INFO - 捕获了 61 个网络请求
2025-08-14 11:35:06 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:35:06 - INFO - WebSocket 摘要已保存: websocket_logs\error_pqkre1755142432_smartdocker.online_20250814_113506_ws.json
2025-08-14 11:35:09 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65018,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:35:09 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:35:09 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:35:09 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:35:09 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:35:09 - INFO - 记录了 34 个页面资源
2025-08-14 11:35:09 - INFO - 页面支持 WebSocket
2025-08-14 11:35:09 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:35:09 - INFO - <EMAIL>
2025-08-14 11:35:09 - INFO - 网络捕获: 主函数开始
2025-08-14 11:35:09 - INFO - 记录了 34 个页面资源
2025-08-14 11:35:09 - INFO - 页面支持 WebSocket
2025-08-14 11:35:09 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:35:19 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:35:23 - INFO - 找到 Turnstile...
2025-08-14 11:35:25 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:35:28 - INFO - 网络捕获: 登录完成后
2025-08-14 11:35:28 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBzMl9wSzI2NVB5T2g1am9jenN4WUNINnZaOEwzcTdkVaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHdid1M5RjRZM1ptQktkbE9GU2NKNWQ2cFk4eHZZTDhGo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:35:28 - INFO - 记录了 26 个页面资源
2025-08-14 11:35:28 - INFO - 页面支持 WebSocket
2025-08-14 11:35:28 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:35:28 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:35:28 - INFO - CDP:启用节流模式
2025-08-14 11:35:28 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:35:28 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:35:42 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=Q0wtj9IoCGg4jcKflR37aJwJe-9u8fsQx8AeNNpFpyg&code_challenge=xMGRnsuHiaQdTipH4L8UkiUQBs8j6GltlrlTVJxFJt8&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:35:42 - INFO - 记录了 18 个页面资源
2025-08-14 11:35:42 - INFO - 页面支持 WebSocket
2025-08-14 11:35:42 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:35:42 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:35:42 - INFO - ReCAPTCHA Token: 
2025-08-14 11:35:42 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:36:04 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:36:04 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:36:04 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:36:04 - INFO - 记录了 105 个页面资源
2025-08-14 11:36:07 - INFO - 页面支持 WebSocket
2025-08-14 11:36:07 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:36:07 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:36:07 - INFO - 成功！当前在订阅页面。
2025-08-14 11:36:07 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:36:07 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:36:10 - INFO - 代码信息: {
  "codeVerifier": "B5hLV6TxNTvi-Qhdj-s6FotgWU_iA7FMWF-nGXvEUQs",
  "code_challenge": "FBV54m0giYeS0FGYsATEpJ8mOPlY_BOsauruDc68Jos",
  "state": "16d71a56-cf30-43de-b171-fe8e97eaf5c4"
}
2025-08-14 11:36:10 - INFO - ==================================================
2025-08-14 11:36:10 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_3f8cbd3df327c5e6acb9e7b70bd6594e&state=16d71a56-cf30-43de-b171-fe8e97eaf5c4&tenant_url=https%3A%2F%2Fd3.api.augmentcode.com%2F
2025-08-14 11:36:12 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:36:12 - INFO - 添加session: <EMAIL>   dfcb5527e2eac7f446aa1773ed6d5ec5130e79df3bbd6e541a38a342f6a0b936   https://d3.api.augmentcode.com/  2025-08-21T03:35:45Z
2025-08-14 11:36:12 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAURP_lrsHQx20pK_-ElPZiGmjBAiZG_Xcb4sLlTOaceUG_Uo42Udqh2_NBFYw2hvnZJxsJOoAKbuFB6S8Hv_bHRrkPvhQUbZjfqjUejRBSMN-OXGhvyDjuyzwtyRVSaK2UahpkKBClaRlWcGpOQzGt9ykT04hMcin4dYs2735xE-XLkuaQCH7EeWzKSmnBaulGV8u2odo2aqidUcMoOWprLHy-jrpEqQ.aJ1Zqw.SBvax306t7Hr0Sfo6rWKH0jy-GQ
2025-08-14 11:36:12 - INFO - 
自动化流程成功完成！
2025-08-14 11:36:12 - INFO - 添加第9个
2025-08-14 11:36:35 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65175,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:36:35 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:36:35 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:36:35 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:36:35 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:36:35 - INFO - 记录了 34 个页面资源
2025-08-14 11:36:35 - INFO - 页面支持 WebSocket
2025-08-14 11:36:35 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:36:35 - INFO - <EMAIL>
2025-08-14 11:36:35 - INFO - 网络捕获: 主函数开始
2025-08-14 11:36:35 - INFO - 记录了 34 个页面资源
2025-08-14 11:36:35 - INFO - 页面支持 WebSocket
2025-08-14 11:36:35 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:36:45 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:36:49 - INFO - 找到 Turnstile...
2025-08-14 11:36:49 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:36:52 - INFO - 网络捕获: 登录完成后
2025-08-14 11:36:52 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBLaGdMNlE2MVNoVHlsT1BSWF9wTU0xMHpueTd5TlZrRaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIERmVTBtRmV1bUhHZ1Z3dVlkdkVZWXZuWnFoWEtkbmQxo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:36:52 - INFO - 记录了 20 个页面资源
2025-08-14 11:36:52 - INFO - 页面支持 WebSocket
2025-08-14 11:36:52 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:36:52 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:36:52 - INFO - CDP:启用节流模式
2025-08-14 11:37:07 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:37:07 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:37:07 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=prwUPYOwib0LjS198Kv-Tw4K_5C1vnIVSIIC5F6wXZM&code_challenge=2wKlwcXL-cn36cFfhyC3QH8cIvjqcXu8Xt5Vm1o5EiA&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:37:07 - INFO - 记录了 19 个页面资源
2025-08-14 11:37:07 - INFO - 页面支持 WebSocket
2025-08-14 11:37:07 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:37:07 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:37:07 - INFO - ReCAPTCHA Token: 
2025-08-14 11:37:07 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:37:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:37:22 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:37:22 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:37:22 - INFO - 记录了 101 个页面资源
2025-08-14 11:37:22 - INFO - 页面支持 WebSocket
2025-08-14 11:37:22 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:37:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:37:22 - INFO - 成功！当前在订阅页面。
2025-08-14 11:37:22 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:37:22 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:37:25 - INFO - 代码信息: {
  "codeVerifier": "8UWtJHN2ayM-et8SKJotKtHqtdUDh1muG8gI95bBFNw",
  "code_challenge": "xPoRxb1FfUmMm0nns_iUzRFBsXTSFXFsqo1HVqE1M48",
  "state": "ebbee114-1bdb-4e58-bd07-c8d7938ee4a6"
}
2025-08-14 11:37:25 - INFO - ==================================================
2025-08-14 11:37:26 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_da750e26735da4f9281cfcb74f97ff1e&state=ebbee114-1bdb-4e58-bd07-c8d7938ee4a6&tenant_url=https%3A%2F%2Fd3.api.augmentcode.com%2F
2025-08-14 11:37:27 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:37:27 - INFO - 添加session: <EMAIL>   c1eb092d3a01182b8a49682e2bc939df9e3c05216814f67ff78e749024b0ba92   https://d3.api.augmentcode.com/  2025-08-21T03:37:10Z
2025-08-14 11:37:27 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3YUYNdATv0Taw1LhGKwRexIVZt_r2X1kOOM5r35gXGVVrhK3eC2tV06SFzy_D1WLgI3gA7u-SX1I-e4jvtT2pjjUUjhPP8Ozkfy0aBR0SVtbOQp0OSPeV1qOEhHerDkrEJljNPoyXRwak7DYXqtad2VJVKoyauvZ-G2xSU8pF2WOucq8E-cx06Qh3iVnjipHjliP1mb-hACoUNFFDS8_wDgRkWj.aJ1Z9w.rr2TmQiAuLJUHAH4HtqB4UlVlYc
2025-08-14 11:37:27 - INFO - 
自动化流程成功完成！
2025-08-14 11:37:27 - INFO - HAR 文件已保存: har_files\success_vpfpu1755142591_smartdocker.online_20250814_113727.har
2025-08-14 11:37:27 - INFO - 捕获了 158 个网络请求
2025-08-14 11:37:27 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:37:27 - INFO - WebSocket 摘要已保存: websocket_logs\success_vpfpu1755142591_smartdocker.online_20250814_113727_ws.json
2025-08-14 11:37:27 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 11:37:27 - INFO - 添加第10个
2025-08-14 11:37:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65332,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:37:51 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:37:51 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:37:51 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:37:51 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:37:51 - INFO - 记录了 34 个页面资源
2025-08-14 11:37:51 - INFO - 页面支持 WebSocket
2025-08-14 11:37:51 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:37:51 - INFO - <EMAIL>
2025-08-14 11:37:51 - INFO - 网络捕获: 主函数开始
2025-08-14 11:37:51 - INFO - 记录了 34 个页面资源
2025-08-14 11:37:51 - INFO - 页面支持 WebSocket
2025-08-14 11:37:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:38:00 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:38:04 - INFO - 找到 Turnstile...
2025-08-14 11:38:06 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:38:09 - INFO - 网络捕获: 登录完成后
2025-08-14 11:38:09 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBoSHRFTl9hdHJhakJ4TjZLOXIwNFhGWVVaT1IycElMUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDN0bFN5WW1VTEhwaGI1VGMzN0diVS04YjdJZDFVS05Vo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:38:09 - INFO - 记录了 20 个页面资源
2025-08-14 11:38:09 - INFO - 页面支持 WebSocket
2025-08-14 11:38:09 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:38:09 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:38:09 - INFO - CDP:启用节流模式
2025-08-14 11:38:09 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:38:09 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:38:09 - INFO - 记录了 20 个页面资源
2025-08-14 11:38:09 - INFO - 页面支持 WebSocket
2025-08-14 11:38:09 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:38:23 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:38:23 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:38:23 - INFO - ReCAPTCHA Token: 
2025-08-14 11:38:23 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:38:53 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-14 11:38:53 - INFO - 鼠标左键放开操作完成。
2025-08-14 11:38:54 - INFO - 鼠标左键点击页面操作完成。
2025-08-14 11:38:54 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-14 11:38:57 - INFO - 验证成功，已进入目标页面。
2025-08-14 11:38:57 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:38:57 - INFO - 记录页面访问: https://app.augmentcode.com/account/subscription (标题: )
2025-08-14 11:38:59 - INFO - 记录了 110 个页面资源
2025-08-14 11:38:59 - INFO - 页面支持 WebSocket
2025-08-14 11:38:59 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:38:59 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:38:59 - INFO - 成功！当前在订阅页面。
2025-08-14 11:38:59 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 11:38:59 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 11:39:02 - INFO - 代码信息: {
  "codeVerifier": "LQw65KyPSWCkeQAvm_vmk46zqDweKuQ4AWLyvd6Dxzw",
  "code_challenge": "ye6mGH0qvFu5feVO7VHYGwoypxRH_yg9ImwUC5IFIhg",
  "state": "43b4c49d-d113-46f6-9b46-9d905a38e049"
}
2025-08-14 11:39:02 - INFO - ==================================================
2025-08-14 11:39:03 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_9ae0e18c3f687b69f79bdb3a820b592c&state=43b4c49d-d113-46f6-9b46-9d905a38e049&tenant_url=https%3A%2F%2Fd3.api.augmentcode.com%2F
2025-08-14 11:39:04 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 11:39:04 - INFO - 添加session: <EMAIL>   aba35015ce9e918255aadfd644a589158013e5019d66962e55c8b69648e7507a   https://d3.api.augmentcode.com/  2025-08-21T03:38:26Z
2025-08-14 11:39:04 - INFO - email:<EMAIL> === cookie:.eJxNjc1uwzAMg99F52SoFduye-qbBHKtDEZjpXDTAvt79xnBDruRBPnxC-a7tMoqusN5b08ZYOFa1o9ZuQqcAQZ4Ly_Rf77k-_x8SJtL7oFULuu3DzE7NmInk8OCE-VkckTX67rptS8xeGsdGXOiSOQIIw5wYA5CJyl_vm6GnDMWvafLo3Lb83a9SXvbdC0q8Lc4jidO5LM5jRgTjdZTGOPSFaLHlIQm9gF-fgGv7kS2.aJ1aWA.hfVSwD9adT5lnL9rVucglzQg5lg
2025-08-14 11:39:04 - INFO - 
自动化流程成功完成！
2025-08-14 11:39:04 - INFO - HAR 文件已保存: har_files\success_nazvk1755142667_smartdocker.online_20250814_113904.har
2025-08-14 11:39:04 - INFO - 捕获了 155 个网络请求
2025-08-14 11:39:04 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:39:04 - INFO - WebSocket 摘要已保存: websocket_logs\success_nazvk1755142667_smartdocker.online_20250814_113904_ws.json
2025-08-14 11:39:04 - INFO - WebSocket 统计: {'total_connections': 0, 'active_connections': 0, 'closed_connections': 0, 'total_frames': 0, 'sent_frames': 0, 'received_frames': 0}
2025-08-14 11:39:04 - INFO - 添加第11个
2025-08-14 11:39:27 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65467,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 11:39:27 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 11:39:27 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 11:39:27 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 11:39:27 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 11:39:27 - INFO - 记录了 34 个页面资源
2025-08-14 11:39:27 - INFO - 页面支持 WebSocket
2025-08-14 11:39:27 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 11:39:27 - INFO - <EMAIL>
2025-08-14 11:39:27 - INFO - 网络捕获: 主函数开始
2025-08-14 11:39:27 - INFO - 记录了 34 个页面资源
2025-08-14 11:39:27 - INFO - 页面支持 WebSocket
2025-08-14 11:39:27 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 11:39:43 - INFO - 获取 cap_value 验证码成功...
2025-08-14 11:39:47 - INFO - 找到 Turnstile...
2025-08-14 11:39:48 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 11:39:51 - INFO - 网络捕获: 登录完成后
2025-08-14 11:39:51 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBQaEU3amZDZ09iaURDdXRnZ3JGNFU3ZjNiQV9MTGN4aqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEV4ank5R3U1ODVhT180bXlkd09PaW9Qc2QtTnloTFZCo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 11:39:51 - INFO - 记录了 20 个页面资源
2025-08-14 11:39:51 - INFO - 页面支持 WebSocket
2025-08-14 11:39:51 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 11:39:52 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 11:39:52 - INFO - CDP:启用节流模式
2025-08-14 11:40:06 - INFO - 验证码已提交，等待跳转...
2025-08-14 11:40:06 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 11:40:06 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=_5eJQOVJyfrXm1Q77OItA7ibI1oO5tSZn5L21IPKamE&code_challenge=tr3bkjnhehigGzfWFJrPM-i49tiTktbTFhP0Sdg8Dzo&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 11:40:06 - INFO - 记录了 19 个页面资源
2025-08-14 11:40:06 - INFO - 页面支持 WebSocket
2025-08-14 11:40:06 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 11:40:06 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 11:40:06 - INFO - ReCAPTCHA Token: 
2025-08-14 11:40:06 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 11:40:06 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 11:40:06 - INFO - 网络捕获: 人类验证过程中
2025-08-14 11:40:06 - INFO - 记录了 19 个页面资源
2025-08-14 11:40:06 - INFO - 页面支持 WebSocket
2025-08-14 11:40:06 - INFO - 
第 1/5 次尝试注册...
2025-08-14 11:40:06 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 11:40:09 - INFO - 网络捕获: 注册尝试后
2025-08-14 11:40:09 - INFO - 记录了 4 个页面资源
2025-08-14 11:40:09 - INFO - 页面支持 WebSocket
2025-08-14 11:40:09 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 11:40:09 - INFO - 网络捕获: 定期捕获
2025-08-14 11:40:10 - INFO - 记录了 4 个页面资源
2025-08-14 11:40:10 - INFO - 页面支持 WebSocket
2025-08-14 11:40:16 - INFO - HAR 文件已保存: har_files\final_ppoao1755142763_smartdocker.online_20250814_114016.har
2025-08-14 11:40:16 - INFO - 捕获了 70 个网络请求
2025-08-14 11:40:16 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 11:40:16 - INFO - WebSocket 摘要已保存: websocket_logs\final_ppoao1755142763_smartdocker.online_20250814_114016_ws.json
2025-08-14 11:40:16 - INFO - Traceback (most recent call last):

2025-08-14 11:40:16 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m2271[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-14 11:40:16 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m2051[0m, in [35mmain[0m
    result = attempt_signup_with_retry(driver)

2025-08-14 11:40:16 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1442[0m, in [35mattempt_signup_with_retry[0m
    [31mtime.sleep[0m[1;31m(30)[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^[0m

2025-08-14 11:40:16 - INFO - [1;35mKeyboardInterrupt[0m

