{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-14T14:38:11.570381Z", "time": 0, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "132236"}], "cookies": [], "content": {"size": 132236, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 132236}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "工作台"}, {"startedDateTime": "2025-08-14T14:38:11.574793Z", "time": 5.9000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DWNmc_MY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21479, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21479}, "cache": {}, "timings": {"send": 0, "wait": 5.9000000059604645, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:11.574801Z", "time": 6, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-BKOrM9G3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 146848, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 146848}, "cache": {}, "timings": {"send": 0, "wait": 6, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574804Z", "time": 12.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DjWl3JGw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 786361, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 786361}, "cache": {}, "timings": {"send": 0, "wait": 12.599999994039536, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574807Z", "time": 7.9000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-H8ttwM1S.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 474, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 474}, "cache": {}, "timings": {"send": 0, "wait": 7.9000000059604645, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574810Z", "time": 8.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CIEDqDGY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 468, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 468}, "cache": {}, "timings": {"send": 0, "wait": 8.599999994039536, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574812Z", "time": 8.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-B9PrHQl7.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4414, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4414}, "cache": {}, "timings": {"send": 0, "wait": 8.799999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574815Z", "time": 11.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BN5KR6Qs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5129, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5129}, "cache": {}, "timings": {"send": 0, "wait": 11.600000008940697, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574818Z", "time": 12.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtKobjF2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14336, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14336}, "cache": {}, "timings": {"send": 0, "wait": 12.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574820Z", "time": 14, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-R6XwfvJN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3351, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3351}, "cache": {}, "timings": {"send": 0, "wait": 14, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574823Z", "time": 13.400000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BRMOCcEH.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 130305, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 130305}, "cache": {}, "timings": {"send": 0, "wait": 13.400000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574826Z", "time": 14.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-kZT51Bed.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2762, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2762}, "cache": {}, "timings": {"send": 0, "wait": 14.299999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574830Z", "time": 14.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DeQidb8Q.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6357, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6357}, "cache": {}, "timings": {"send": 0, "wait": 14.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574833Z", "time": 14.700000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_user_key-BQXKMoNP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2168, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2168}, "cache": {}, "timings": {"send": 0, "wait": 14.700000002980232, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574836Z", "time": 14.700000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-Bd2HX4Pl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5330, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5330}, "cache": {}, "timings": {"send": 0, "wait": 14.700000002980232, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574839Z", "time": 8.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-C_thiKp4.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3318, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 3318}, "cache": {}, "timings": {"send": 0, "wait": 8.599999994039536, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:38:11.574842Z", "time": 8.899999991059303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DwV2T02E.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 50384, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 50384}, "cache": {}, "timings": {"send": 0, "wait": 8.899999991059303, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:38:11.574845Z", "time": 9.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtkFttLu.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 42774, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 42774}, "cache": {}, "timings": {"send": 0, "wait": 9.599999994039536, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:38:11.574848Z", "time": 10.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-ntgrtH_u.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1564, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 1564}, "cache": {}, "timings": {"send": 0, "wait": 10.799999997019768, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:38:11.574852Z", "time": 11.699999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DDWJuWk8.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 9043, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 9043}, "cache": {}, "timings": {"send": 0, "wait": 11.699999988079071, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:38:11.574855Z", "time": 1.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/app/config", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 408, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 408}, "cache": {}, "timings": {"send": 0, "wait": 1.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:11.574859Z", "time": 1.699999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/zh-CN-DTI-qNBp.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94467, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 94467}, "cache": {}, "timings": {"send": 0, "wait": 1.699999988079071, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:11.574863Z", "time": 2.7000000029802322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/list?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6466, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 6466}, "cache": {}, "timings": {"send": 0, "wait": 2.7000000029802322, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:11.574866Z", "time": 2.8999999910593033, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/proxy/detection?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 655, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 655}, "cache": {}, "timings": {"send": 0, "wait": 2.8999999910593033, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:11.574870Z", "time": 3, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/core-info?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 406, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 406}, "cache": {}, "timings": {"send": 0, "wait": 3, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:11.574874Z", "time": 3.2000000029802322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_spin-Dat2nEi2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 822, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 822}, "cache": {}, "timings": {"send": 0, "wait": 3.2000000029802322, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:11.574878Z", "time": 4, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob2-CEoTd5f3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1157, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1157}, "cache": {}, "timings": {"send": 0, "wait": 4, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:11.574882Z", "time": 4.200000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob1-CCAm7wau.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1143, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1143}, "cache": {}, "timings": {"send": 0, "wait": 4.200000002980232, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:11.574885Z", "time": 26.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/JetBrains%20Mono/JetBrainsMono-Bold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 278128, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 278128}, "cache": {}, "timings": {"send": 0, "wait": 26.5, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:38:11.574889Z", "time": 27.200000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Regular.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 407356, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 407356}, "cache": {}, "timings": {"send": 0, "wait": 27.200000002980232, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:38:11.574893Z", "time": 27.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Medium.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 411800, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 411800}, "cache": {}, "timings": {"send": 0, "wait": 27.600000008940697, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:38:11.574898Z", "time": 27.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-SemiBold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 414276, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 414276}, "cache": {}, "timings": {"send": 0, "wait": 27.799999997019768, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:38:11.574902Z", "time": 5.5999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 285778, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 285778}, "cache": {}, "timings": {"send": 0, "wait": 5.5999999940395355, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:38:11.574907Z", "time": 2.699999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/US-BXoNwYaC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5807, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5807}, "cache": {}, "timings": {"send": 0, "wait": 2.699999988079071, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:11.574911Z", "time": 2.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_speed_test-2BHwaLbY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2258, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2258}, "cache": {}, "timings": {"send": 0, "wait": 2.5, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.886063Z", "time": 0, "request": {"method": "GET", "url": "https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBSUThzaTB1Tzk5TDBtZUZ5RG1iem9uQXZIMEpvRFhpdKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHdBRVpIdks3UmI4TThlcEo0YlYxMGhpR0R5Sy1aT0RYo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "48699"}], "cookies": [], "content": {"size": 48699, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 48699}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Code"}, {"startedDateTime": "2025-08-14T14:38:28.890699Z", "time": 0, "request": {"method": "GET", "url": "https://cdn.auth0.com/ulp/react-components/1.146.2/css/main.cdn.min.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:38:28.890711Z", "time": 0, "request": {"method": "GET", "url": "https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890717Z", "time": 200.5, "request": {"method": "GET", "url": "https://www.augmentcode.com/android-chrome-512x512.png", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "image/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 200.5, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:38:28.890722Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890726Z", "time": 1, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/v1/projects/ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg/settings", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:28.890732Z", "time": 224.5, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-16732971011&en=page_view&dr=login.augmentcode.com&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge&scrsrc=www.googletagmanager.com&frm=0&rnd=1380619331.1755153507&dt=Augment%20Code&auid=1125204801.1755153495&navt=n&npa=0&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105033766~105033768~105103161~105103163~105231383~105231385&tft=1755153506759&tfd=859&apve=1&apvf=f", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 224.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:28.890737Z", "time": 227.40000000596046, "request": {"method": "GET", "url": "https://googleads.g.doubleclick.net/pagead/viewthroughconversion/16732971011/?random=1755153506757&cv=11&fst=1755153506757&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105033766~105033768~105103161~105103163~105231383~105231385&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBSUThzaTB1Tzk5TDBtZUZ5RG1iem9uQXZIMEpvRFhpdKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHdBRVpIdks3UmI4TThlcEo0YlYxMGhpR0R5Sy1aT0RYo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBSUThzaTB1Tzk5TDBtZUZ5RG1iem9uQXZIMEpvRFhpdKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHdBRVpIdks3UmI4TThlcEo0YlYxMGhpR0R5Sy1aT0RYo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=1125204801.1755153495&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=4", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2511, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2511}, "cache": {}, "timings": {"send": 0, "wait": 227.40000000596046, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890742Z", "time": 219.40000000596046, "request": {"method": "GET", "url": "https://analytics.google.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=1755153506729&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105033766~105033768~105103161~105103163~105231383~105231385&cid=1155547475.1755153495&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&_s=1&sid=1755153495&sct=1&seg=1&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBSUThzaTB1Tzk5TDBtZUZ5RG1iem9uQXZIMEpvRFhpdKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHdBRVpIdks3UmI4TThlcEo0YlYxMGhpR0R5Sy1aT0RYo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dr=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBSUThzaTB1Tzk5TDBtZUZ5RG1iem9uQXZIMEpvRFhpdKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHdBRVpIdks3UmI4TThlcEo0YlYxMGhpR0R5Sy1aT0RYo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dt=Augment%20Code&en=page_view&_ee=1&tfd=870", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 219.40000000596046, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:28.890747Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/ajs-destination.bundle.8e6b895db75187c55313.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890752Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/schemaFilter.bundle.1b218d13fed021531d4e.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890757Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890762Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890767Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/845/431110629a9fe8297174.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890772Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890777Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890782Z", "time": 0, "request": {"method": "GET", "url": "https://us.i.posthog.com/static/array.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890788Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_GIfRcxAmWBSIxniAKUhlM9pQ343wGGnAgC3iBMO1FSN/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:28.890794Z", "time": 245.09999999403954, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755153506807&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 245.09999999403954, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:28.890799Z", "time": 177.70000000298023, "request": {"method": "GET", "url": "https://www.google.com/pagead/1p-user-list/16732971011/?random=1755153506757&cv=11&fst=1755151200000&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105033766~105033768~105103161~105103163~105231383~105231385&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBSUThzaTB1Tzk5TDBtZUZ5RG1iem9uQXZIMEpvRFhpdKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHdBRVpIdks3UmI4TThlcEo0YlYxMGhpR0R5Sy1aT0RYo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBSUThzaTB1Tzk5TDBtZUZ5RG1iem9uQXZIMEpvRFhpdKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHdBRVpIdks3UmI4TThlcEo0YlYxMGhpR0R5Sy1aT0RYo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=1125204801.1755153495&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=3&is_vtc=1&cid=CAQSigEA2abss6dISzNA6ATBbKzv0260408Dod7WYXX2xg1oUuZXn95PbvAufa8yqErrFg078aXnI7GqnRThN6Z3b42aqRsh-YL6p9565tDloGPzxg4crJqmO8dWxRoUR_0kJQn-PfM8DjjryOLDRnQy2AbNEg1Ck8O7sHqTJB8Egt2tPflB3b-brNPTKn8&random=1450866187&rmt_tld=0&ipr=y", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 342, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 342}, "cache": {}, "timings": {"send": 0, "wait": 177.70000000298023, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:38:28.890805Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:43.293768Z", "time": 0, "request": {"method": "GET", "url": "https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=zQ-VeMhMXxsP3pkepBDcMU-Aoiu1ej9fyFfsVK5rAg4&code_challenge=nLaNFLw4ml29owFfGBCJOzWa8Fi1pK3mLUtMG-HAfSc&code_challenge_method=S256", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "20407"}], "cookies": [], "content": {"size": 20407, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 20407}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Verification"}, {"startedDateTime": "2025-08-14T14:38:43.296979Z", "time": 468.09999999403954, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css?family=Offside", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 671, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 671}, "cache": {}, "timings": {"send": 0, "wait": 468.09999999403954, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:38:43.296992Z", "time": 515.0999999940395, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css2?family=Inter:wght@500&family=Roboto&display=swap", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1978, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 1978}, "cache": {}, "timings": {"send": 0, "wait": 515.0999999940395, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:38:43.296998Z", "time": 6470.5, "request": {"method": "GET", "url": "https://js-232.augmentcode.com/prod/bundle.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 6470.5, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:43.297004Z", "time": 214.79999999701977, "request": {"method": "GET", "url": "https://www.google.com/recaptcha/enterprise.js?render=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 214.79999999701977, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:43.297010Z", "time": 9053.20000000298, "request": {"method": "GET", "url": "https://www.gstatic.com/recaptcha/releases/07cvpCr3Xe3g2ttJNUkC6W0J/recaptcha__en.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 342118, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 342118}, "cache": {}, "timings": {"send": 0, "wait": 9053.20000000298, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:38:43.297016Z", "time": 2169, "request": {"method": "GET", "url": "https://net.prod.verisoul.ai/http?project_id=9d08ce27-3d9b-4737-b477-e57c7446255b&session_id=fe1a62de-80f9-4b6e-9ac2-0ffab4e2abb7", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 2169, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:43.297022Z", "time": 564.2000000029802, "request": {"method": "GET", "url": "https://ingest.prod.verisoul.ai/worker/ice-servers?project_id=9d08ce27-3d9b-4737-b477-e57c7446255b&session_id=fe1a62de-80f9-4b6e-9ac2-0ffab4e2abb7", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 564.2000000029802, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:43.297028Z", "time": 3698, "request": {"method": "GET", "url": "https://ingest.prod.verisoul.ai/worker", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 3698, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:43.297035Z", "time": 2953.7999999970198, "request": {"method": "GET", "url": "https://net1.prod.verisoul.ai/tcp", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 2953.7999999970198, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:43.297075Z", "time": 2035, "request": {"method": "GET", "url": "https://ingest.prod.verisoul.ai/webrtc-sdp", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 2035, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:38:43.297081Z", "time": 1711.5999999940395, "request": {"method": "GET", "url": "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1&co=aHR0cHM6Ly9hdXRoLmF1Z21lbnRjb2RlLmNvbTo0NDM.&hl=en&v=07cvpCr3Xe3g2ttJNUkC6W0J&size=invisible&anchor-ms=20000&execute-ms=15000&cb=c3c4ckaflv88", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1711.5999999940395, "receive": 0}, "resourceType": "iframe"}, {"startedDateTime": "2025-08-14T14:38:54.190235Z", "time": 0, "request": {"method": "GET", "url": "https://fonts.gstatic.com/s/inter/v19/UcCO3FwrK3iLTeHuS_nVMrMxCp50SjIw2boKoduKmMEVuI6fAZ9hiA.woff2", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "css"}], "websockets": [], "pages": [{"startedDateTime": "2025-08-14T14:39:27.241051Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}