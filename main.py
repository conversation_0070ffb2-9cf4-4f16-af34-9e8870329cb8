from spider import get_codeinfo
from DrissionPage import ChromiumOptions, ChromiumPage
import sys
import time
import json
import os
import re
import inspect
import random
import string
import requests
import email
import html
import uuid
from email.parser import BytesParser
from email.utils import formatdate
from fake_useragent import UserAgent
s = requests.Session()

def get_current_method_name():
    return inspect.currentframe().f_back.f_code.co_name


def extract_email_elements(email_message_str):
    code = None
    # 将字符串转换为字节类型（如果原始字符串不是字节类型的话）
    email_message_bytes = email_message_str.encode('utf-8')
    
    # 使用BytesParser解析字节字符串为Message对象
    msg = email.message_from_bytes(email_message_bytes)
    
    # 访问邮件的各个部分
    if "<EMAIL>" in msg['From']:

        
        # 检查邮件日期是否在当前时间附近1分钟内
        target_time = formatdate(localtime=False, usegmt=True)  # 获取当前时间，格式如 "Thu, 3 Jul 2025 04:10:52 GMT"
        if is_time_within_range(msg['Date'], target_time, minutes=2):
            # 访问邮件正文
            if msg.is_multipart():
                for part in msg.walk():
                    # 每个part都是一个Message对象，我们可以检查其内容类型
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    # 打印文本内容
                    if content_type == "text/plain" and "attachment" not in content_disposition:
                        body = part.get_payload(decode=True).decode()  # 解码并获取文本内容
                        match = re.search(r'Your verification code is: (\d{6})', body)
                        if match:
                            code = match.group(1)
        else:
            pass
            print(f'{msg['Date']} 时间不在最近 {target_time }')
    
    

    
    return code
    
def is_time_within_range(email_date_str, target_date_str, minutes=1):
    """
    检查邮件日期是否在目标时间的指定分钟范围内
    
    参数:
        email_date_str: 邮件中的日期字符串，格式如 "Thu, 3 Jul 2025 04:10:52 +0000"
        target_date_str: 目标日期字符串，相同格式
        minutes: 允许的时间差（分钟）
        
    返回:
        布尔值，表示是否在时间范围内
    """
    from email.utils import parsedate_to_datetime
    from datetime import datetime, timedelta
    
    try:
        # 解析邮件日期字符串为datetime对象
        email_date = parsedate_to_datetime(email_date_str)
        
        # 解析目标日期字符串为datetime对象
        target_date = parsedate_to_datetime(target_date_str)
        
        # 计算时间差（绝对值）
        time_diff = abs((email_date - target_date).total_seconds())
        
        # 检查时间差是否在允许范围内（转换为秒）
        return time_diff <= (minutes * 60)
    except Exception as e:
        print(f"日期比较出错: {e}")
        return False


def setup_browser_options():
    """配置浏览器选项 - 增强设备指纹伪造"""
    os.environ["PYTHONVERBOSE"] = "0"
    os.environ["PYINSTALLER_VERBOSE"] = "0"
    os.environ["PYDEVD_WARN_SLOW_RESOLVE_TIMEOUT"] = "0"
    ua = UserAgent()

    co = ChromiumOptions()
    #co.set_browser_path("D:\\软件\\谷歌\\Chrome\\App\\chrome.exe")
    co.set_proxy('http://************:7897')
    # 基础反检测配置
    co.set_argument("--fingerprint=1000")
    co.set_pref("credentials_enable_service", False)
    co.set_argument("--hide-crash-restore-bubble")
    #co.set_argument("--disable-blink-features=AutomationControlled")
    co.set_argument("--exclude-switches=enable-automation")
    co.set_argument("--disable-extensions-except")
    co.set_argument("--disable-plugins-discovery")
    #co.set_argument("--disable-web-security")
    co.set_argument("--disable-features=VizDisplayCompositor")
    co.set_argument("--no-first-run")
    co.set_argument("--disable-default-apps")

    #./chrome  --user-data-dir=/tmp/chromium/1000 --timezone="America/Los_Angeles" --proxy-server="你的代理服务器地址"
    co.set_user_agent(ua.random)
    
    # 随机化窗口大小
    window_sizes = [(1920, 1080), (1366, 768), (1440, 900), (1536, 864)]
    width, height = random.choice(window_sizes)
    co.set_argument(f"--window-size={width},{height}")
    
    # 设备指纹伪造
    co.set_pref("webgl.disabled", False)
    co.set_pref("media.navigator.enabled", True)
    co.set_pref("media.peerconnection.enabled", True)
    
    # 语言和时区随机化
    languages = ["en-US,en;q=0.9", "zh-CN,zh;q=0.9,en;q=0.8", "en-GB,en;q=0.9"]
    co.set_argument(f"--lang={random.choice(languages).split(',')[0]}")
    
    co.incognito()
    
    extension_path = _get_extension_path("turnstilePatch")
    co.add_extension(extension_path)

    # Mac 系统特殊处理
    if sys.platform == "darwin":
        co.set_argument("--no-sandbox")
        co.set_argument("--disable-gpu")

    return co

def _get_extension_path(exname='turnstilePatch'):
    """获取插件路径"""
    root_dir = os.getcwd()
    extension_path = os.path.join(root_dir, exname)

    if hasattr(sys, "_MEIPASS"):
        extension_path = os.path.join(sys._MEIPASS, exname)

    if not os.path.exists(extension_path):
        raise FileNotFoundError(f"插件不存在: {extension_path}")

    return extension_path


def get_captcha(url,retry=0):
    baseurl = "http://************:5000" #"http://************:5000"
    def _getcap_value(id):
        cap_value = s.get(baseurl + "/result",params={"id":id})
        try:
            if cap_value.text != "CAPTCHA_NOT_READY":
                return cap_value.json()["value"]
            else:
                time.sleep(5)
                #print(f"{get_current_method_name()} --获取 cap_value 失败，重试...")
                return _getcap_value(id)
        except Exception as e:
            #print(f"{get_current_method_name()}发生错误: ====== {cap_value.text}")
            return _getcap_value(id)
            

    task_id = s.get(baseurl + "/turnstile",params={"url":url,"sitekey":"0x4AAAAAAAQFNSW6xordsuIq"}).json()["task_id"]
    
    if task_id:
        cap_value = _getcap_value(task_id)
        # print(cap_value)
        if cap_value != "CAPTCHA_FAIL":
            print(f"获取 cap_value 验证码成功...")
            return cap_value
        else:
            print(f"获取验证码失败，重试次数: {retry+1}")
            return get_captcha(url,retry+1)


def get_email():
    #SWITCH_EMAIL = rando你是什么模型你是m.choice(list(ALLDOMAIN.keys()))
    baseurl = "https://es.slogo.eu.org"
    #s.proxies = {"http": "http://127.0.0.1:10808", "https": "http://127.0.0.1:10808"}
    def _generate_random_name():
        # 生成5位英文字符
        letters1 = ''.join(random.choices(string.ascii_lowercase, k=5))
        # 生成1-3个数字
        numbers = ''.join(random.choices(string.digits, k=random.randint(1, 3)))
        # 生成1-3个英文字符
        letters2 = ''.join(random.choices(string.ascii_lowercase, k=random.randint(1, 3)))
        # 组合成最终名称
        return letters1 + str(int(time.time()))
    
    def _fetch_email_data(name):
        try:
            res = requests.post(
                baseurl+"/admin/new_address",
                json={
                    "enablePrefix": True,
                    "name": name,
                    "domain": "565847.cfd"
                    #"domain": "smartdocker.online",
                },
                headers={
                    'x-admin-auth': "chen1234.",
                    "Content-Type": "application/json"
                },
                proxies={"http": "http://***********:10808", "https": "http://***********:10808"}
            )
            if res.status_code == 200:
                response_data = res.json()
                email = response_data.get("address", 0)
                jwt = response_data.get("jwt", 0)
                return email,jwt
            else:
                print(f"请求失败，状态码: {res.status_code}")
                return None
        except requests.RequestException as e:
            print(f"请求出现错误: {e}")
            return None
    return _fetch_email_data(_generate_random_name())

def get_email_data(jwt):
    baseurl = "https://es.slogo.eu.org"
    code = None
    if jwt:
        
        res = s.get(
            baseurl+"/api/mails",
            params={"limit":10,"offset":0},
            proxies={"http": "http://***********:10808", "https": "http://***********:10808"},
            headers={
                "Authorization": f"Bearer {jwt}",
                # "x-custom-auth": "<你的网站密码>", # 如果启用了自定义密码
                "Content-Type": "application/json"
        })

        results = res.json().get("results", 0)
        if results:
            raw = results[0].get("raw", 0)
            #print(f"{get_current_method_name()} -- results -- success")
            code = extract_email_elements(raw)
        else:
            #print(f"{get_current_method_name()} -- no results")
            return get_email_data(jwt)

    return code

    
def get_augtoken(url,code_verifier):

    from urllib.parse import urlparse, parse_qs
    _query_str = urlparse(url).query  # 提取 ? 后面的部分
    query_str = _query_str.replace("&amp;", "&")
    # 解析参数并转为字典（重复 key 会变成数组）
    params = parse_qs(query_str)
    tenant_url = params.get("tenant_url",0)[0]
    code = params.get("code",0)[0]
    data = {}
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Augment.vscode-augment/0.482.1 (win32; x64; 10.0.19045) vscode/1.95.3",
        "x-request-id": str(uuid.uuid4()),
        "x-request-session-id": str(uuid.uuid4()),
        "x-api-version": "2"
    }
    body = {
        "grant_type": "authorization_code",
        "client_id": "augment-vscode-extension",
        "code_verifier": code_verifier,
        "redirect_uri": "vscode://augment.vscode-augment/auth/result",
        "code": code
    }
    res = s.post(tenant_url+"token",headers=headers,json=body).json()
    access_token = res.get("access_token", 0)
    if not access_token:
        return None
    data["accessToken"] = access_token
    headers["Authorization"] = f"Bearer {access_token}"
    res_info = s.post(tenant_url+"subscription-info",headers=headers,json={}).json()
    end_date = res_info.get("subscription",0).get("ActiveSubscription",0).get("end_date", 0)
    if not end_date:
        return None
    data["end_date"] = end_date
    data["tenantURL"] = tenant_url

    return data


def add_session(email,augmentSession:dict,expire_time="",other=""):
    data = {
            "email":email,
            "augmentSession":augmentSession,
            "expire_time":expire_time,
            "other":other
        }
    res =s.post("https://aug.202578.xyz/add_session",
                 headers={"X-User-ID": "admin"},
                 json=data
                 )
    if res.status_code == 200:
        print(f"添加session成功: {res.json()}")
    else:
        print(f"添加session失败: {res.text}")
    


def demo_basic_navigation():
    """演示基本的页面导航和元素操作"""
    print("=== DrissionPage 基本导航演示 ===")

    # 创建浏览器实例
    co = setup_browser_options()
    browser = ChromiumPage(co)
    url = "https://app.augmentcode.com/account"

    try:
        # 访问百度首页
        print("正在访问首页...")
        #browser.wait.load_start()
        tab = browser.latest_tab
        #tab.set.window.hide()
        tab.get(url,timeout=10)
        
        # Wait for page to load
        # print(tab.get("https://app.augmentcode.com/account",timeout=10))
        login_input = tab.ele('xpath://*[@id="username"]', timeout=10)
        login_button = tab.ele('@name=action', timeout=10)

        capvalue = get_captcha(tab.url)
        print("获取capvalue...")
        #ej = get_email()

        print("获取email...")
        #if ej and login_input:
        if login_input:
            email,jwt = "<EMAIL>","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.VVDwwRlkS8WlS5j6KNeLMyXdqszqDHstMzoEBoayRa4"
            login_input.click()
            login_input.input(email)
            print("Successfully found and clicked on login input")
            captchainbpx = tab.ele('@name=captcha', timeout=10)
            if captchainbpx and login_button:
                print(f"找到 Turnstile...")
                captchainbpx.set.value(capvalue)
                time.sleep(1)
                login_button.click()
                email_code = tab.ele('#code', timeout=10)
                code = get_email_data(jwt)

                if email_code and code:
                    email_code.input(code)
                    tab.ele('@name=action', timeout=10).click()
                    capvalue2 = get_captcha(tab.url)
                    captchainbpx2 = tab.get_frame(1,timeout=10).ele('#recaptcha-token',timeout=10)
                    if not captchainbpx2 or not capvalue2:
                        return None
                    captchainbpx2.set.value(capvalue2)
                    tab.ele(".c-checkbox--mark",timeout=10).click()

                    tab.ele("#signup-button",timeout=10).click()

                    retry = 3
                    while retry > 0:
                        retry -= 1
                        time.sleep(3)
                        if tab.url == "https://app.augmentcode.com/account/subscription":
                            data = get_codeinfo()
                            print(f"代码信息: {json.dumps(data, indent=2)}")
                            print("=" * 50)
                            if data:
                                params = {
                                    "response_type": "code",
                                    "code_challenge": data["code_challenge"],
                                    "code_challenge_method": "S256",
                                    "client_id": "augment-vscode-extension",
                                    "redirect_uri": "vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult",
                                    "state": data["state"],
                                    "scope": "email",
                                    "prompt": "login"
                                }
                                query_str = "&".join([f"{k}={v}" for k, v in params.items()])
                                tab.get("https://auth.augmentcode.com/terms-accept" + f"?{query_str}", timeout=10)
                                cookie = tab.cookies().as_dict().get("session","")
                                res = tab.html
                                #res = s.get(url="https://auth.augmentcode.com/terms-accept",params=params,cookies=cookie).text
                                if "vscode://" in res:
                                    match = re.search(r'href="(vscode://[^"]+)"', res)
                                    if match:
                                        decoded_url = html.unescape(match.group(1))
                                        print("Extracted URL:", decoded_url)
                                        tokeninfo = get_augtoken(decoded_url,data["codeVerifier"])
                                        if tokeninfo:
                                            accessToken = tokeninfo.get("accessToken",0)
                                            tenantURL = tokeninfo.get("tenantURL",0)
                                            end_date = tokeninfo.get("end_date",0)
                                            # add_session(email.replace(SWITCH_EMAIL,ALLDOMAIN[SWITCH_EMAIL]),{
                                            #     "accessToken":accessToken,
                                            #     "tenantURL":tenantURL,
                                            #     "scopes":["email"]
                                            #     },expire_time=end_date,other=cookie)
                                            print(f"添加session: {email}   {accessToken}   {tenantURL}  {end_date}")

                                        print(f"email:{email} === cookie:{cookie}")
                            break


                else:
                    print("email_code or code error")
                    return
            else:
                print("Waiting second...")
        else:
            print("Could not find login input field")


    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭浏览器
        browser.quit()
        print("浏览器已关闭")



def main():
    """主函数，运行所有演示"""
    print("开始")
    print("=" * 50)
    print(f"SWITCH_EMAIL: {SWITCH_EMAIL}")
    try:
        starttime = time.time()
        demo_basic_navigation()
        endtime = time.time()
        print(endtime - starttime)
    except KeyboardInterrupt:
        print("\n用户中断了程序执行")
    except Exception as e:
        print(f"程序执行出错: {e}")

    print("\n所有演示完成！")


if __name__ == "__main__":
    ALLDOMAIN = {
        "smartdocker.online":"163.com"
    }
    #while True:
    SWITCH_EMAIL = random.choice(list(ALLDOMAIN.keys()))
    main()
    time.sleep(30)
    # add_session("<EMAIL>".replace("smartdocker.online","163.com"),{
    # "accessToken":"1cfbd5474115a2092781f25c503ad90b9f07af593f4aab46468131df6b91c9b5",
    # "tenantURL":"https://d3.api.augmentcode.com/",
    # "scopes":["email"]
    # },expire_time="2025-08-01T18:12:03.055072153Z")
