import os
import uuid
import base64
import hashlib




# --- 辅助函数：Base64URL 编码与解码 ---

def encode_base64url(data_bytes):
    """
    将字节数据编码为 URL 安全的 Base64 字符串 (Base64URL)。
    移除标准 Base64 中的 '+', '/', 并去除 '=' 填充符。
    """
    # Base64 编码，返回 bytes 类型，然后解码为 UTF-8 字符串
    base64_string = base64.b64encode(data_bytes).decode('utf-8')
    # 进行 URL 安全替换并移除填充符
    return base64_string.replace('+', '-').replace('/', '_').replace('=', '')

def decode_base64url(encoded_string):
    """
    将 Base64URL 字符串解码回原始字节数据。
    """
    # 1. 还原替换字符
    standard_base64 = encoded_string.replace('-', '+').replace('_', '/')
    # 2. 补齐 '=' 填充字符
    while len(standard_base64) % 4 != 0:
        standard_base64 += '='
    # 3. Base64 解码回字节
    return base64.b64decode(standard_base64)


def get_codeinfo():
    data = {}
    random_bytes = os.urandom(32)
    codeVerifier = encode_base64url(random_bytes)
    data["codeVerifier"] = codeVerifier

    sha256_hasher = hashlib.sha256()
    sha256_hasher.update(codeVerifier.encode())
    hash_digest_bytes = sha256_hasher.digest()
    final_result_ee_hash = encode_base64url(hash_digest_bytes)
    data["code_challenge"] = final_result_ee_hash
    data["state"] = str(uuid.uuid4())

    return data


def get_augtoken(url,code_verifier):
    import json
    from urllib.parse import urlparse, parse_qs
    import requests
    s = requests.Session()
    _query_str = urlparse(url).query  # 提取 ? 后面的部分
    query_str = _query_str.replace("&amp;", "&")
    # 解析参数并转为字典（重复 key 会变成数组）
    params = parse_qs(query_str)
    tenant_url = params.get("tenant_url",0)[0]
    code = params.get("code",0)[0]

    data = {}
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Augment.vscode-augment/0.509.1 (win32; x64; 10.0.19045) vscode/1.95.3",
        "x-request-id": str(uuid.uuid4()),
        "x-request-session-id": str(uuid.uuid4()),
        "x-api-version": "2"
    }
    body = {
        "grant_type": "authorization_code",
        "client_id": "augment-vscode-extension",
        "code_verifier": code_verifier,
        "redirect_uri": "vscode://augment.vscode-augment/auth/result",
        "code": code
    }
    if "i1" in url:
        s.proxies = {
            "http": "socks5://127.0.0.1:10808",
            "https": "socks5://127.0.0.1:10808",
        }
    res = s.post(tenant_url+"token",headers=headers,json=body).json()
    access_token = res.get("access_token", 0)
    if not access_token:
        return None
    data["accessToken"] = access_token
    headers["Authorization"] = f"Bearer {access_token}"
    res_info = s.post(tenant_url+"subscription-info",headers=headers,json={}).json()
    end_date = res_info.get("subscription",None).get("ActiveSubscription",None).get("end_date", "2025-08-30T00:00:00Z")
    data["end_date"] = end_date
    data["tenantURL"] = tenant_url

    datab = {
        "email":email,
        "augmentSession": {"accessToken": access_token,
                           "tenantURL": tenant_url, "scopes": ["email"]},
        "expire_time": end_date,
        "other": other
    }
    if datab:
        s.headers.update({"X-User-ID":"admin"})
        addres = s.post("https://aug.202578.xyz/add_session",json=datab)
        print(addres.json())
    return json.dumps(datab)



#__all__ = ["get_codeinfo"]
if __name__ == "__main__":
    data = get_codeinfo()
    print(data)
    codeVerifier = data.get("codeVerifier")
    url  = f"https://auth.augmentcode.com/terms-accept?response_type=code&code_challenge={data.get('code_challenge')}&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state={data.get('state')}&scope=email&prompt=login"
    print(url)
    email = input("邮箱：")
    vsurl = input("输入vscode链接：")
    other = "" #input("other：")
    print(get_augtoken(vsurl,codeVerifier))
    # from urllib.parse import urlparse, parse_qs
    # # 解析 URL
    # parsed_url = urlparse(vsurl)
    # query_params = parse_qs(parsed_url.query)
    # code = query_params.get('code', [''])[0]
    # tenant_url = query_params.get('tenant_url', [''])[0]
    # print(tenant_url,code)
    #vscode://augment.vscode-augment/auth/result?code=_017cfe2f86d25436079964a632cd9dcb&state=57147e93-1d0f-475d-a067-d856b5bcf9b6&tenant_url=https%3A%2F%2Fd14.api.augmentcode.com%2F
