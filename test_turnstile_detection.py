#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试Turnstile反检测的脚本
"""

import os
import time
import random
from dotenv import load_dotenv

def test_turnstile_detection():
    """测试Turnstile检测和处理"""
    print("🚀 开始测试Turnstile反检测功能")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 检查必要的环境变量
    required_vars = ["ROXYBRWOSER_ID", "ROXYWORK_ID", "ROXYTOKEN"]
    for var in required_vars:
        if not os.getenv(var):
            print(f"❌ 缺少环境变量: {var}")
            return False
    
    try:
        from RoxyD import (
            setup_driver, 
            apply_anti_detection, 
            handle_turnstile_challenge,
            wait_for_page_stability,
            check_detection_status
        )
        
        print("1️⃣ 初始化浏览器...")
        driver = setup_driver()
        
        print("2️⃣ 应用高级反检测...")
        if apply_anti_detection(driver):
            print("   ✅ 反检测机制应用成功")
        else:
            print("   ❌ 反检测机制应用失败")
            return False
        
        print("3️⃣ 访问目标页面...")
        driver.get("https://app.augmentcode.com/account")
        
        print("4️⃣ 等待页面稳定...")
        wait_for_page_stability(driver)
        
        print("5️⃣ 检查检测状态...")
        detection_result = check_detection_status(driver)
        if detection_result == True:
            print("   ❌ 检测到严重阻止")
            return False
        elif detection_result == "verification":
            print("   ⚠️ 检测到验证码，这是正常的")
        else:
            print("   ✅ 未检测到阻止")
        
        print("6️⃣ 处理Turnstile挑战...")
        if handle_turnstile_challenge(driver):
            print("   ✅ Turnstile处理成功")
        else:
            print("   ⚠️ Turnstile处理失败或无需处理")
        
        print("7️⃣ 检查页面内容...")
        page_title = driver.title
        print(f"   页面标题: {page_title}")
        
        # 检查是否有错误信息
        error_keywords = ['blocked', 'forbidden', 'denied', 'error']
        page_source_lower = driver.page_source.lower()
        
        found_errors = []
        for keyword in error_keywords:
            if keyword in page_source_lower:
                found_errors.append(keyword)
        
        if found_errors:
            print(f"   ⚠️ 检测到错误关键词: {found_errors}")
        else:
            print("   ✅ 页面内容正常")
        
        print("8️⃣ 检查关键元素...")
        try:
            # 检查用户名输入框
            username_input = driver.find_element_by_id("username")
            if username_input:
                print("   ✅ 找到用户名输入框")
            
            # 检查验证码框
            captcha_input = driver.find_element_by_name("captcha")
            if captcha_input:
                print("   ✅ 找到验证码输入框")
                
        except Exception as e:
            print(f"   ⚠️ 查找元素时出错: {e}")
        
        print("9️⃣ 最终状态检查...")
        final_url = driver.current_url
        print(f"   当前URL: {final_url}")
        
        if "blocked" in final_url or "denied" in final_url:
            print("   ❌ URL显示被阻止")
            result = False
        elif "account" in final_url:
            print("   ✅ 成功访问账户页面")
            result = True
        else:
            print("   ⚠️ 未知状态")
            result = None
        
        print("\n🔍 详细信息收集...")
        
        # 收集浏览器信息
        try:
            user_agent = driver.execute_script("return navigator.userAgent")
            print(f"User-Agent: {user_agent}")
            
            webdriver_prop = driver.execute_script("return navigator.webdriver")
            print(f"navigator.webdriver: {webdriver_prop}")
            
            languages = driver.execute_script("return navigator.languages")
            print(f"navigator.languages: {languages}")
            
            platform = driver.execute_script("return navigator.platform")
            print(f"navigator.platform: {platform}")
            
        except Exception as e:
            print(f"收集浏览器信息时出错: {e}")
        
        print("\n⏸️ 测试暂停，请手动检查浏览器...")
        input("按回车键继续...")
        
        driver.quit()
        
        print("\n" + "=" * 60)
        if result == True:
            print("🎉 测试成功！反检测机制工作正常")
        elif result == False:
            print("❌ 测试失败！仍然被检测到")
        else:
            print("⚠️ 测试结果不确定，需要进一步分析")
        print("=" * 60)
        
        return result
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_basic_anti_detection():
    """测试基本反检测功能"""
    print("🔧 测试基本反检测功能...")
    
    try:
        from RoxyD import setup_driver, apply_anti_detection
        
        driver = setup_driver()
        apply_anti_detection(driver)
        
        # 访问反检测测试页面
        driver.get("https://bot.sannysoft.com/")
        time.sleep(5)
        
        print("请查看浏览器页面，检查反检测效果...")
        print("绿色表示通过，红色表示被检测")
        input("按回车键继续...")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"基本反检测测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Turnstile反检测测试套件")
    print("=" * 60)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 完整Turnstile反检测测试")
        print("2. 基本反检测功能测试")
        print("3. 运行实际注册流程")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == "0":
            print("退出测试")
            break
        elif choice == "1":
            test_turnstile_detection()
        elif choice == "2":
            test_basic_anti_detection()
        elif choice == "3":
            try:
                from RoxyD import main as roxyd_main
                print("🚀 运行实际注册流程...")
                roxyd_main()
            except Exception as e:
                print(f"运行注册流程时出错: {e}")
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
