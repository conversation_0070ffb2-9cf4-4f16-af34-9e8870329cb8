2025-08-20 23:34:08 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58509,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:34:08 - INFO - 127.0.0.1:58509
2025-08-20 23:34:09 - INFO - <EMAIL>
2025-08-20 23:34:09 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:34:26 - INFO - 获取 cap_value 验证码成功...
2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1293, in _session
    self._start_bidi()
    ~~~~~~~~~~~~~~~~^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1251, in _start_bidi
    raise WebDriverException("Unable to find url to connect to from capabilities")

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: Unable to find url to connect to from capabilities


2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1281, in browser
    self._start_bidi()
    ~~~~~~~~~~~~~~~~^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1251, in _start_bidi
    raise WebDriverException("Unable to find url to connect to from capabilities")

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: Unable to find url to connect to from capabilities


2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1317, in browsing_context
    self._start_bidi()
    ~~~~~~~~~~~~~~~~^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1251, in _start_bidi
    raise WebDriverException("Unable to find url to connect to from capabilities")

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: Unable to find url to connect to from capabilities


2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1258, in network
    self._start_bidi()
    ~~~~~~~~~~~~~~~~^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1251, in _start_bidi
    raise WebDriverException("Unable to find url to connect to from capabilities")

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: Unable to find url to connect to from capabilities


2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1172, in orientation
    return self.execute(Command.GET_SCREEN_ORIENTATION)["value"]
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 454, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: unknown command: unknown command: session/eeac06a9796dbebbd51caede15ac7ab1/orientation
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff6084f2441]
	(No symbol) [0x0x7ff6084f1f19]
	(No symbol) [0x0x7ff608444b05]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff608443b00]
	GetHandleVerifier [0x0x7ff608ab84c8+4357608]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]


2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1364, in permissions
    self._start_bidi()
    ~~~~~~~~~~~~~~~~^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1251, in _start_bidi
    raise WebDriverException("Unable to find url to connect to from capabilities")

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: Unable to find url to connect to from capabilities


2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1240, in script
    self._start_bidi()
    ~~~~~~~~~~~~~~~~^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1251, in _start_bidi
    raise WebDriverException("Unable to find url to connect to from capabilities")

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: Unable to find url to connect to from capabilities


2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1342, in storage
    self._start_bidi()
    ~~~~~~~~~~~~~~~~^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1251, in _start_bidi
    raise WebDriverException("Unable to find url to connect to from capabilities")

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: Unable to find url to connect to from capabilities


2025-08-20 23:35:04 - INFO - Traceback (most recent call last):

2025-08-20 23:35:04 - INFO -   File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_resolver.py", line 193, in _get_py_dictionary
    attr = getattr(var, name)

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1386, in webextension
    self._start_bidi()
    ~~~~~~~~~~~~~~~~^^

2025-08-20 23:35:04 - INFO -   File "e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 1251, in _start_bidi
    raise WebDriverException("Unable to find url to connect to from capabilities")

2025-08-20 23:35:04 - INFO - selenium.common.exceptions.WebDriverException: Message: Unable to find url to connect to from capabilities


