{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-14T14:52:08.498795Z", "time": 0, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "132236"}], "cookies": [], "content": {"size": 132236, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 132236}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "工作台"}, {"startedDateTime": "2025-08-14T14:52:08.503500Z", "time": 4.*************03, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DWNmc_MY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21479, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21479}, "cache": {}, "timings": {"send": 0, "wait": 4.*************03, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:08.503513Z", "time": 5.*************32, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-BKOrM9G3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 146848, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 146848}, "cache": {}, "timings": {"send": 0, "wait": 5.*************32, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503518Z", "time": 7.*************32, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-B9PrHQl7.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4414, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4414}, "cache": {}, "timings": {"send": 0, "wait": 7.*************32, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503522Z", "time": 10.400000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DjWl3JGw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 786361, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 786361}, "cache": {}, "timings": {"send": 0, "wait": 10.400000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503526Z", "time": 6.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-H8ttwM1S.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 474, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 474}, "cache": {}, "timings": {"send": 0, "wait": 6.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503530Z", "time": 7.*************355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CIEDqDGY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 468, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 468}, "cache": {}, "timings": {"send": 0, "wait": 7.*************355, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503534Z", "time": 10, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BN5KR6Qs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5129, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5129}, "cache": {}, "timings": {"send": 0, "wait": 10, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503539Z", "time": 10.099999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtKobjF2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14336, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14336}, "cache": {}, "timings": {"send": 0, "wait": 10.099999994039536, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503543Z", "time": 11.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-R6XwfvJN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3351, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3351}, "cache": {}, "timings": {"send": 0, "wait": 11.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503548Z", "time": 12, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DeQidb8Q.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6357, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6357}, "cache": {}, "timings": {"send": 0, "wait": 12, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503552Z", "time": 12, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-Bd2HX4Pl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5330, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5330}, "cache": {}, "timings": {"send": 0, "wait": 12, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503557Z", "time": 12.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-kZT51Bed.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2762, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2762}, "cache": {}, "timings": {"send": 0, "wait": 12.299999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503562Z", "time": 11, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BRMOCcEH.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 130305, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 130305}, "cache": {}, "timings": {"send": 0, "wait": 11, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503567Z", "time": 6.9000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-C_thiKp4.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3318, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 3318}, "cache": {}, "timings": {"send": 0, "wait": 6.9000000059604645, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:52:08.503572Z", "time": 7.4000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DwV2T02E.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 50384, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 50384}, "cache": {}, "timings": {"send": 0, "wait": 7.4000000059604645, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:52:08.503577Z", "time": 12.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_user_key-BQXKMoNP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2168, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2168}, "cache": {}, "timings": {"send": 0, "wait": 12.299999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503582Z", "time": 8.**************1, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtkFttLu.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 42774, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 42774}, "cache": {}, "timings": {"send": 0, "wait": 8.**************1, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:52:08.503588Z", "time": 9.199999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-ntgrtH_u.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1564, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 1564}, "cache": {}, "timings": {"send": 0, "wait": 9.199999988079071, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:52:08.503593Z", "time": 9.*************36, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DDWJuWk8.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 9043, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 9043}, "cache": {}, "timings": {"send": 0, "wait": 9.*************36, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:52:08.503599Z", "time": 1.2999999970197678, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/app/config", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 408, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 408}, "cache": {}, "timings": {"send": 0, "wait": 1.2999999970197678, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:52:08.503605Z", "time": 1.9000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/zh-CN-DTI-qNBp.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94467, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 94467}, "cache": {}, "timings": {"send": 0, "wait": 1.9000000059604645, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:08.503610Z", "time": 3.300000011920929, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/list?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6466, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 6466}, "cache": {}, "timings": {"send": 0, "wait": 3.300000011920929, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:52:08.503616Z", "time": 3.2999999970197678, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/core-info?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 406, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 406}, "cache": {}, "timings": {"send": 0, "wait": 3.2999999970197678, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:52:08.503622Z", "time": 3.3999999910593033, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/proxy/detection?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 655, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 655}, "cache": {}, "timings": {"send": 0, "wait": 3.3999999910593033, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:52:08.503628Z", "time": 2.4000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_spin-Dat2nEi2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 822, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 822}, "cache": {}, "timings": {"send": 0, "wait": 2.4000000059604645, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:08.503634Z", "time": 2.7999999970197678, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob2-CEoTd5f3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1157, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1157}, "cache": {}, "timings": {"send": 0, "wait": 2.7999999970197678, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:08.503640Z", "time": 2.9000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob1-CCAm7wau.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1143, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1143}, "cache": {}, "timings": {"send": 0, "wait": 2.9000000059604645, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:08.503647Z", "time": 24.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/JetBrains%20Mono/JetBrainsMono-Bold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 278128, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 278128}, "cache": {}, "timings": {"send": 0, "wait": 24.299999997019768, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:52:08.503654Z", "time": 26.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Regular.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 407356, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 407356}, "cache": {}, "timings": {"send": 0, "wait": 26.799999997019768, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:52:08.503661Z", "time": 27.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Medium.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 411800, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 411800}, "cache": {}, "timings": {"send": 0, "wait": 27.299999997019768, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:52:08.503668Z", "time": 27.*************36, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-SemiBold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 414276, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 414276}, "cache": {}, "timings": {"send": 0, "wait": 27.*************36, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:52:08.503675Z", "time": 7.*************355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 285778, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 285778}, "cache": {}, "timings": {"send": 0, "wait": 7.*************355, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:52:08.503683Z", "time": 3.4000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/US-BXoNwYaC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5807, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5807}, "cache": {}, "timings": {"send": 0, "wait": 3.4000000059604645, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:08.503690Z", "time": 3, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_speed_test-2BHwaLbY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2258, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2258}, "cache": {}, "timings": {"send": 0, "wait": 3, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.655683Z", "time": 0, "request": {"method": "GET", "url": "https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBxdGR0ZFp4NjZER2Zmbk9ZbVhyQWFMMF9jeFZMcllTMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDdhaWNMTGZQbjRMcFlxU29YNGpJZHl5ek9YNDV1Z2s3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "48698"}], "cookies": [], "content": {"size": 48698, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 48698}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Code"}, {"startedDateTime": "2025-08-14T14:52:27.661113Z", "time": 0, "request": {"method": "GET", "url": "https://cdn.auth0.com/ulp/react-components/1.146.2/css/main.cdn.min.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:52:27.661126Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661131Z", "time": 0, "request": {"method": "GET", "url": "https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661136Z", "time": 235.29999999701977, "request": {"method": "GET", "url": "https://www.augmentcode.com/android-chrome-512x512.png", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "image/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 235.29999999701977, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:52:27.661141Z", "time": 0.800000011920929, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/v1/projects/ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg/settings", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0.800000011920929, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:52:27.661146Z", "time": 201.5, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=login.augmentcode.com&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge&scrsrc=www.googletagmanager.com&frm=0&rnd=687403910.1755154344&dt=Augment%20Code&auid=*********.**********&navt=n&npa=0&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=1755154344244&tfd=951&apve=1&apvf=f", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 201.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:52:27.661151Z", "time": 204.79999999701977, "request": {"method": "GET", "url": "https://googleads.g.doubleclick.net/pagead/viewthroughconversion/***********/?random=1755154344242&cv=11&fst=1755154344242&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBxdGR0ZFp4NjZER2Zmbk9ZbVhyQWFMMF9jeFZMcllTMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDdhaWNMTGZQbjRMcFlxU29YNGpJZHl5ek9YNDV1Z2s3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBxdGR0ZFp4NjZER2Zmbk9ZbVhyQWFMMF9jeFZMcllTMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDdhaWNMTGZQbjRMcFlxU29YNGpJZHl5ek9YNDV1Z2s3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=*********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=4", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2510, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2510}, "cache": {}, "timings": {"send": 0, "wait": 204.79999999701977, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661156Z", "time": 202.*************3, "request": {"method": "GET", "url": "https://analytics.google.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=1755154344210&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=**********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBxdGR0ZFp4NjZER2Zmbk9ZbVhyQWFMMF9jeFZMcllTMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDdhaWNMTGZQbjRMcFlxU29YNGpJZHl5ek9YNDV1Z2s3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dr=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBxdGR0ZFp4NjZER2Zmbk9ZbVhyQWFMMF9jeFZMcllTMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDdhaWNMTGZQbjRMcFlxU29YNGpJZHl5ek9YNDV1Z2s3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dt=Augment%20Code&en=page_view&_ee=1&tfd=962", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 202.*************3, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:52:27.661161Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/ajs-destination.bundle.8e6b895db75187c55313.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661166Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/schemaFilter.bundle.1b218d13fed021531d4e.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661170Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661175Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661180Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/845/431110629a9fe8297174.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661186Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661191Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661196Z", "time": 0, "request": {"method": "GET", "url": "https://us.i.posthog.com/static/array.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661202Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_GIfRcxAmWBSIxniAKUhlM9pQ343wGGnAgC3iBMO1FSN/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:52:27.661207Z", "time": 227, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755154344294&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 227, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:52:27.661213Z", "time": 184.29999999701977, "request": {"method": "GET", "url": "https://www.google.com/pagead/1p-user-list/***********/?random=1755154344242&cv=11&fst=1755151200000&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBxdGR0ZFp4NjZER2Zmbk9ZbVhyQWFMMF9jeFZMcllTMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDdhaWNMTGZQbjRMcFlxU29YNGpJZHl5ek9YNDV1Z2s3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBxdGR0ZFp4NjZER2Zmbk9ZbVhyQWFMMF9jeFZMcllTMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDdhaWNMTGZQbjRMcFlxU29YNGpJZHl5ek9YNDV1Z2s3o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=*********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=3&is_vtc=1&cid=CAQSigEA2abss3-O6RKcAja4xlYMilX54Xv-3nrGFMwHmCPCYS8DJXkqDXdqOWGIDS2kc98UejCNazXT50XdtFJJ074Tdcpqh_dARlcmFIfWiyVFyVzFNNFTcUNM3yk7SbbrZhbPODwpQMpLONAg-IjQs3FtoBuAjiaMNYZJN_6lLnCXo-L32Btv4voRGHk&random=2310658507&rmt_tld=0&ipr=y", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 342, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 342}, "cache": {}, "timings": {"send": 0, "wait": 184.29999999701977, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:52:27.661219Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:53:17.273339Z", "time": 0, "request": {"method": "GET", "url": "https://app.augmentcode.com/account/subscription", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "71754"}], "cookies": [], "content": {"size": 71754, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 71754}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": ""}, {"startedDateTime": "2025-08-14T14:53:17.279521Z", "time": 1737.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/tailwind-DxnphuB3.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5101, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 5101}, "cache": {}, "timings": {"send": 0, "wait": 1737.*************, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:53:17.279542Z", "time": 6867.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-C-JJqdMW.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94361, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 94361}, "cache": {}, "timings": {"send": 0, "wait": 6867.29999999702, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:53:17.279552Z", "time": 392.79999999701977, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-D6aQ-Xs1.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 416, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 416}, "cache": {}, "timings": {"send": 0, "wait": 392.79999999701977, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:53:17.279560Z", "time": 801.8000000119209, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/manifest-46feb8ab.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4124, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4124}, "cache": {}, "timings": {"send": 0, "wait": 801.8000000119209, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279568Z", "time": 543.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/entry.client-C92V1BwZ.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1792, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1792}, "cache": {}, "timings": {"send": 0, "wait": 543.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279576Z", "time": 5476, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BS38kjqr.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21235, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21235}, "cache": {}, "timings": {"send": 0, "wait": 5476, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279585Z", "time": 6130.70000000298, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-Bi4s4-Io.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 46139, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 46139}, "cache": {}, "timings": {"send": 0, "wait": 6130.70000000298, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279594Z", "time": 693.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/QueryClientProvider-CeGnmbe-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 696, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 696}, "cache": {}, "timings": {"send": 0, "wait": 693.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279604Z", "time": 1837.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/client-only-C74SDDMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2093, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2093}, "cache": {}, "timings": {"send": 0, "wait": 1837.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279612Z", "time": 2753.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryClient.client-Dk3lS3wN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3850, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3850}, "cache": {}, "timings": {"send": 0, "wait": 2753.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279621Z", "time": 3252.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B7Ui2t93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5343, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5343}, "cache": {}, "timings": {"send": 0, "wait": 3252.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279630Z", "time": 4780.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/components--HLsvfrm.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13680, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13680}, "cache": {}, "timings": {"send": 0, "wait": 4780.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279639Z", "time": 4966.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index.modern-950P1XoK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14011, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14011}, "cache": {}, "timings": {"send": 0, "wait": 4966.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279649Z", "time": 4997.399999991059, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/theme-C1ulz75E.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13876, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13876}, "cache": {}, "timings": {"send": 0, "wait": 4997.399999991059, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279660Z", "time": 847.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/container-BlJCmUTg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 884, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 884}, "cache": {}, "timings": {"send": 0, "wait": 847.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279668Z", "time": 878.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/link-CMt6MnuB.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 998, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 998}, "cache": {}, "timings": {"send": 0, "wait": 878.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279675Z", "time": 894.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/card-BBgKeY7L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 865, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 865}, "cache": {}, "timings": {"send": 0, "wait": 894.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279682Z", "time": 3003.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/flex-C9XhsxSj.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3855, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3855}, "cache": {}, "timings": {"send": 0, "wait": 3003.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279694Z", "time": 941.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/button-Dvrjyl3p.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 608, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 608}, "cache": {}, "timings": {"send": 0, "wait": 941.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279709Z", "time": 988, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DrFu-skq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1110, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1110}, "cache": {}, "timings": {"send": 0, "wait": 988, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279723Z", "time": 1003, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/jotaiStore.client-sdvKmlSn.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 430, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 430}, "cache": {}, "timings": {"send": 0, "wait": 1003, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279734Z", "time": 3485.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Toast-CG_NC-6_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5695, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5695}, "cache": {}, "timings": {"send": 0, "wait": 3485.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279792Z", "time": 1033.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-C8U1uDHl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1132, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1132}, "cache": {}, "timings": {"send": 0, "wait": 1033.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279807Z", "time": 1048.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-BkoMXhYo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 462, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 462}, "cache": {}, "timings": {"send": 0, "wait": 1048.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279817Z", "time": 2410.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CI4icoal.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1970, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1970}, "cache": {}, "timings": {"send": 0, "wait": 2410.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279825Z", "time": 2423.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAuWbg93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1857, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1857}, "cache": {}, "timings": {"send": 0, "wait": 2423.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279833Z", "time": 1125.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/spinner-Cq6egsy4.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1179, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1179}, "cache": {}, "timings": {"send": 0, "wait": 1125.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279841Z", "time": 1141.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAyM6kBC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 766, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 766}, "cache": {}, "timings": {"send": 0, "wait": 1141.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279849Z", "time": 1171.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/get-subtree-8AxxbxX_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 598, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 598}, "cache": {}, "timings": {"send": 0, "wait": 1171.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279858Z", "time": 1187.199999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B5mzPb5P.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 935, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 935}, "cache": {}, "timings": {"send": 0, "wait": 1187.199999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279866Z", "time": 1218, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/base-button-Dk95TXPu.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1152, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1152}, "cache": {}, "timings": {"send": 0, "wait": 1218, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279874Z", "time": 1279.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/useQuery-BvSAfNQo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1398, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1398}, "cache": {}, "timings": {"send": 0, "wait": 1279.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279883Z", "time": 3532, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-Cup3efAs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6206, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6206}, "cache": {}, "timings": {"send": 0, "wait": 3532, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279891Z", "time": 4212.699999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-icons.esm-g3l3pVh3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8914, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8914}, "cache": {}, "timings": {"send": 0, "wait": 4212.699999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279900Z", "time": 3096.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/animations-CMbVnQEg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3734, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3734}, "cache": {}, "timings": {"send": 0, "wait": 3096.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279908Z", "time": 4228.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/style-Bv9a6v44.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8859, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8859}, "cache": {}, "timings": {"send": 0, "wait": 4228.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279917Z", "time": 1357.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/BaseHeader-y1wz0aO3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1603, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1603}, "cache": {}, "timings": {"send": 0, "wait": 1357.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279926Z", "time": 2566.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/string-CwzBSc0v.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3135, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3135}, "cache": {}, "timings": {"send": 0, "wait": 2566.1000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279934Z", "time": 5151.20000000298, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/user-d_3utlAo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 16299, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 16299}, "cache": {}, "timings": {"send": 0, "wait": 5151.20000000298, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279943Z", "time": 1418.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/guards-C20ItfmI.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 963, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 963}, "cache": {}, "timings": {"send": 0, "wait": 1418.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279952Z", "time": 1450.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/subscription-creation-pending-Mylp5-_d.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1450.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279961Z", "time": 1466, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plans-D8s3V0en.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 510, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 510}, "cache": {}, "timings": {"send": 0, "wait": 1466, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279970Z", "time": 1496.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/skeleton-qwMe81ym.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 880, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 880}, "cache": {}, "timings": {"send": 0, "wait": 1496.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279978Z", "time": 1512.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/feature-flags.client-BVZhVN7G.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 431, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 431}, "cache": {}, "timings": {"send": 0, "wait": 1512.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279988Z", "time": 1527.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-D-DXDI2l.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1527.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.279997Z", "time": 1558, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/box-DvlTT8Qh.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 824, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 824}, "cache": {}, "timings": {"send": 0, "wait": 1558, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280006Z", "time": 1573.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryOptions-Yjo86aMs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 721, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 721}, "cache": {}, "timings": {"send": 0, "wait": 1573.1000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280014Z", "time": 1603, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/toDate-qOSwr3PX.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 615, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 615}, "cache": {}, "timings": {"send": 0, "wait": 1603, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280023Z", "time": 1618.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/addLeadingZeros-6--iqVZy.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 456, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 456}, "cache": {}, "timings": {"send": 0, "wait": 1618.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280032Z", "time": 2845.3999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/ProgressPage-CfNQ5HtT.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1971, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1971}, "cache": {}, "timings": {"send": 0, "wait": 2845.3999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280040Z", "time": 4103.699999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout-s5dvxSMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 7715, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 7715}, "cache": {}, "timings": {"send": 0, "wait": 4103.699999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280049Z", "time": 4686.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/proto3-Bmo7MjaP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 10903, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 10903}, "cache": {}, "timings": {"send": 0, "wait": 4686.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280060Z", "time": 1945.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/heading-Duq80h8F.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 991, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 991}, "cache": {}, "timings": {"send": 0, "wait": 1945.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280069Z", "time": 1976.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account--DlvNh9L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1172, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1172}, "cache": {}, "timings": {"send": 0, "wait": 1976.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280078Z", "time": 1991.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-BneX_s9R.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 745, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 745}, "cache": {}, "timings": {"send": 0, "wait": 1991.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280089Z", "time": 3360.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/PlanPicker-CZi5-vIO.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4367, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4367}, "cache": {}, "timings": {"send": 0, "wait": 3360.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280103Z", "time": 2053.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/icons--M48DCb3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1161, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1161}, "cache": {}, "timings": {"send": 0, "wait": 2053.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280115Z", "time": 2084.300000011921, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/number-BS8GKe3y.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1719, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1719}, "cache": {}, "timings": {"send": 0, "wait": 2084.300000011921, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280124Z", "time": 2099.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plural-D9YAiM4O.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1040, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1040}, "cache": {}, "timings": {"send": 0, "wait": 2099.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280133Z", "time": 2131.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Enabled-BoZ8Au2f.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 860, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 860}, "cache": {}, "timings": {"send": 0, "wait": 2131.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280143Z", "time": 4113, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Card-BR5rB2rc.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6435, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6435}, "cache": {}, "timings": {"send": 0, "wait": 4113, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280153Z", "time": 2177.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-C5gnWpVx.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 387, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 387}, "cache": {}, "timings": {"send": 0, "wait": 2177.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280163Z", "time": 2193.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/url-_DgIuZOw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 635, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 635}, "cache": {}, "timings": {"send": 0, "wait": 2193.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280173Z", "time": 2239.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/isBefore-DuJnhAXP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 446, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 446}, "cache": {}, "timings": {"send": 0, "wait": 2239.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280184Z", "time": 2270, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/badge-CrInsKkE.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 986, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 986}, "cache": {}, "timings": {"send": 0, "wait": 2270, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280195Z", "time": 2908.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BEyGE8AK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1912, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1912}, "cache": {}, "timings": {"send": 0, "wait": 2908.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280205Z", "time": 4149.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Badge-CCBfROU-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6905, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6905}, "cache": {}, "timings": {"send": 0, "wait": 4149.29999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280216Z", "time": 4165, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DzvzAwJl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6526, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6526}, "cache": {}, "timings": {"send": 0, "wait": 4165, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280226Z", "time": 2285.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constructFrom-DWjd9ymD.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 441, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 441}, "cache": {}, "timings": {"send": 0, "wait": 2285.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280236Z", "time": 5335, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account.subscription-BBRL8heg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 17282, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 17282}, "cache": {}, "timings": {"send": 0, "wait": 5335, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280247Z", "time": 2090.*************, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400..700,0..1,0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 705, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 705}, "cache": {}, "timings": {"send": 0, "wait": 2090.*************, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:53:17.280267Z", "time": 216, "request": {"method": "GET", "url": "https://app.augmentcode.com/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2118, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2118}, "cache": {}, "timings": {"send": 0, "wait": 216, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:53:17.280279Z", "time": 944.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/user", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 484, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 484}, "cache": {}, "timings": {"send": 0, "wait": 944.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280290Z", "time": 463.29999999701977, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/team/plan-change-pending", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 339, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 339}, "cache": {}, "timings": {"send": 0, "wait": 463.29999999701977, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280301Z", "time": 1085.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/subscription", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 801, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 801}, "cache": {}, "timings": {"send": 0, "wait": 1085.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280311Z", "time": 958.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/plans", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 943, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 943}, "cache": {}, "timings": {"send": 0, "wait": 958.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280322Z", "time": 430.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/augment-logo.svg", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3975, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 3975}, "cache": {}, "timings": {"send": 0, "wait": 430.**************, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:53:17.280358Z", "time": 397.*************, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=auth.augmentcode.com&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&scrsrc=www.googletagmanager.com&frm=0&rnd=*********.**********&auid=*********.**********&navt=n&npa=1&_tu=AAg&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l3l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=**********217&tfd=10776&apve=1&apvf=sb", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 397.*************, "receive": 0}, "resourceType": "beacon"}, {"startedDateTime": "2025-08-14T14:53:17.280370Z", "time": 563.*************, "request": {"method": "GET", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=*************&gcd=13l3l3l3l3l1&npa=1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=**********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&ngs=1&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&dr=https%3A%2F%2Fauth.augmentcode.com%2F&dt=&_tu=AAg&en=page_view&_ee=1&tfd=10789", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 563.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280381Z", "time": 732.*************, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/i", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 732.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280392Z", "time": 685.2999999970198, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/p", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 685.2999999970198, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280403Z", "time": 1539.*************, "request": {"method": "GET", "url": "https://analytics.twitter.com/i/adsct?txn_id=pva71&p_id=Twitter&tw_sale_amount=0&tw_order_quantity=0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1539.*************, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:53:17.280415Z", "time": 1242.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/credits", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 381, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 381}, "cache": {}, "timings": {"send": 0, "wait": 1242.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280426Z", "time": 621.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/payment", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 346, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 346}, "cache": {}, "timings": {"send": 0, "wait": 621.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280437Z", "time": 281.*************4, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/deletions", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 336, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 336}, "cache": {}, "timings": {"send": 0, "wait": 281.*************4, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280448Z", "time": 762.*************, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 762.*************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:53:17.280460Z", "time": 279.5, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755154376024&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 279.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280471Z", "time": 1238.*************, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/recorder.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1238.*************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:53:17.280482Z", "time": 524.*************, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 524.*************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:53:17.280497Z", "time": 663, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/web-vitals.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 663, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:53:17.280508Z", "time": 318.79999999701977, "request": {"method": "GET", "url": "https://us.i.posthog.com/e/?ip=0&_=1755154379025&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 318.79999999701977, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280520Z", "time": 205, "request": {"method": "GET", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=*************&gcd=13l3l3l3l3l1&npa=1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=**********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AEAAAAQ&ngs=1&_s=2&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&dr=https%3A%2F%2Fauth.augmentcode.com%2F&dt=&_tu=AAg&en=scroll&epn.percent_scrolled=90&_et=13&tfd=15793", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 205, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280533Z", "time": 371.*************, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=*************&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 371.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280544Z", "time": 557.*************, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=1755154389325&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 557.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:53:17.280556Z", "time": 371.79999999701977, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=1755154394313&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 371.79999999701977, "receive": 0}, "resourceType": "fetch"}], "websockets": [], "pages": [{"startedDateTime": "2025-08-14T14:53:23.062313Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}