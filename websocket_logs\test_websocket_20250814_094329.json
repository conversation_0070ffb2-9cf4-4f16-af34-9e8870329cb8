{"captureTime": "2025-08-14T09:43:29.702107Z", "totalConnections": 1, "connections": [{"url": "wss://app.augmentcode.com/ws", "status": "closed", "createdDateTime": "2025-08-14T09:43:29.700252Z", "closedDateTime": "2025-08-14T09:43:29.700259Z", "totalFrames": 4, "sentFrames": 2, "receivedFrames": 2, "handshakeResponse": {"status": 101, "statusText": "Switching Protocols", "headers": {"Upgrade": "websocket", "Connection": "Upgrade", "Sec-WebSocket-Accept": "test-accept-key"}, "timestamp": 1755135809.7002554}, "frames": [{"type": "sent", "timestamp": 1755135809.7002563, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"auth\",\"token\":\"test-token\"}", "payloadLength": 35}, {"type": "received", "timestamp": 1755135809.800257, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"auth_success\",\"user_id\":\"12345\"}", "payloadLength": 40}, {"type": "sent", "timestamp": 1755135810.700258, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"ping\"}", "payloadLength": 15}, {"type": "received", "timestamp": 1755135810.8002586, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"pong\"}", "payloadLength": 15}]}]}