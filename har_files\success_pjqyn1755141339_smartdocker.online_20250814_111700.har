{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-14T11:15:43.822349Z", "time": 0, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "132235"}], "cookies": [], "content": {"size": 132235, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 132235}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "工作台"}, {"startedDateTime": "2025-08-14T11:15:43.825568Z", "time": 4.700000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DWNmc_MY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21479, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21479}, "cache": {}, "timings": {"send": 0, "wait": 4.700000002980232, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:15:43.825575Z", "time": 6.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-B9PrHQl7.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4414, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4414}, "cache": {}, "timings": {"send": 0, "wait": 6.600000008940697, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825578Z", "time": 4.800000011920929, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-BKOrM9G3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 146848, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 146848}, "cache": {}, "timings": {"send": 0, "wait": 4.800000011920929, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825581Z", "time": 12.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DjWl3JGw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 786361, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 786361}, "cache": {}, "timings": {"send": 0, "wait": 12.600000008940697, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825584Z", "time": 6.************303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CIEDqDGY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 468, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 468}, "cache": {}, "timings": {"send": 0, "wait": 6.************303, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825586Z", "time": 7.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-H8ttwM1S.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 474, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 474}, "cache": {}, "timings": {"send": 0, "wait": 7.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825589Z", "time": 11.*************32, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BN5KR6Qs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5129, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5129}, "cache": {}, "timings": {"send": 0, "wait": 11.*************32, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825591Z", "time": 11.099999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtKobjF2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14336, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14336}, "cache": {}, "timings": {"send": 0, "wait": 11.099999994039536, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825594Z", "time": 12.900000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-R6XwfvJN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3351, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3351}, "cache": {}, "timings": {"send": 0, "wait": 12.900000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825596Z", "time": 12.*************32, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BRMOCcEH.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 130305, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 130305}, "cache": {}, "timings": {"send": 0, "wait": 12.*************32, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825599Z", "time": 13.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DeQidb8Q.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6357, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6357}, "cache": {}, "timings": {"send": 0, "wait": 13.299999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825602Z", "time": 13.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-kZT51Bed.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2762, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2762}, "cache": {}, "timings": {"send": 0, "wait": 13.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825605Z", "time": 12.************303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-Bd2HX4Pl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5330, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5330}, "cache": {}, "timings": {"send": 0, "wait": 12.************303, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825608Z", "time": 7.0999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-C_thiKp4.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3318, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 3318}, "cache": {}, "timings": {"send": 0, "wait": 7.0999999940395355, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:15:43.825611Z", "time": 13.700000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_user_key-BQXKMoNP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2168, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2168}, "cache": {}, "timings": {"send": 0, "wait": 13.700000002980232, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825614Z", "time": 7.800000011920929, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DwV2T02E.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 50384, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 50384}, "cache": {}, "timings": {"send": 0, "wait": 7.800000011920929, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:15:43.825618Z", "time": 9.100000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtkFttLu.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 42774, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 42774}, "cache": {}, "timings": {"send": 0, "wait": 9.100000008940697, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:15:43.825621Z", "time": 9.**************4, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-ntgrtH_u.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1564, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 1564}, "cache": {}, "timings": {"send": 0, "wait": 9.**************4, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:15:43.825625Z", "time": 10.100000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DDWJuWk8.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 9043, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 9043}, "cache": {}, "timings": {"send": 0, "wait": 10.100000008940697, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:15:43.825628Z", "time": 1.7000000029802322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/app/config", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 408, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 408}, "cache": {}, "timings": {"send": 0, "wait": 1.7000000029802322, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:15:43.825632Z", "time": 2.*************322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/zh-CN-DTI-qNBp.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94467, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 94467}, "cache": {}, "timings": {"send": 0, "wait": 2.*************322, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:15:43.825635Z", "time": 3, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/list?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6465, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 6465}, "cache": {}, "timings": {"send": 0, "wait": 3, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:15:43.825639Z", "time": 3.1000000089406967, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/core-info?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 406, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 406}, "cache": {}, "timings": {"send": 0, "wait": 3.1000000089406967, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:15:43.825644Z", "time": 3.7000000029802322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/proxy/detection?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 654, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 654}, "cache": {}, "timings": {"send": 0, "wait": 3.7000000029802322, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:15:43.825650Z", "time": 3.1000000089406967, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_spin-Dat2nEi2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 822, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 822}, "cache": {}, "timings": {"send": 0, "wait": 3.1000000089406967, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:15:43.825656Z", "time": 2.699999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob1-CCAm7wau.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1143, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1143}, "cache": {}, "timings": {"send": 0, "wait": 2.699999988079071, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:15:43.825661Z", "time": 3.*************355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob2-CEoTd5f3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1157, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1157}, "cache": {}, "timings": {"send": 0, "wait": 3.*************355, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:15:43.825667Z", "time": 25, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/JetBrains%20Mono/JetBrainsMono-Bold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 278128, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 278128}, "cache": {}, "timings": {"send": 0, "wait": 25, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:15:43.825673Z", "time": 26.100000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Regular.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 407356, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 407356}, "cache": {}, "timings": {"send": 0, "wait": 26.100000008940697, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:15:43.825680Z", "time": 25.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Medium.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 411800, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 411800}, "cache": {}, "timings": {"send": 0, "wait": 25.799999997019768, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:15:43.825686Z", "time": 26.099999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-SemiBold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 414276, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 414276}, "cache": {}, "timings": {"send": 0, "wait": 26.099999994039536, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:15:43.825692Z", "time": 5.*************32, "request": {"method": "GET", "url": "http://127.0.0.1:45535/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 285778, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 285778}, "cache": {}, "timings": {"send": 0, "wait": 5.*************32, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:15:43.825698Z", "time": 2.7000000029802322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_speed_test-2BHwaLbY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2258, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2258}, "cache": {}, "timings": {"send": 0, "wait": 2.7000000029802322, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:15:43.825704Z", "time": 2.7000000029802322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/US-BXoNwYaC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5807, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5807}, "cache": {}, "timings": {"send": 0, "wait": 2.7000000029802322, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.419780Z", "time": 0, "request": {"method": "GET", "url": "https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBLeGVSZHF6QXpWZlFPMFVnaFl5aE5vWDNtZG5RMUpFVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVCVnVueDFFRHlkeEYwTG1MX2ZfMy16bnNlTEI2MFREo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "48709"}], "cookies": [], "content": {"size": 48709, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 48709}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Code"}, {"startedDateTime": "2025-08-14T11:16:09.424077Z", "time": 0, "request": {"method": "GET", "url": "https://cdn.auth0.com/ulp/react-components/1.146.2/css/main.cdn.min.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:16:09.424089Z", "time": 0, "request": {"method": "GET", "url": "https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424095Z", "time": 300.**************, "request": {"method": "GET", "url": "https://www.augmentcode.com/android-chrome-512x512.png", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "image/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 300.**************, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:16:09.424100Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424104Z", "time": 0.699999988079071, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/v1/projects/ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg/settings", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0.699999988079071, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:09.424109Z", "time": 265.**************, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=login.augmentcode.com&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge&scrsrc=www.googletagmanager.com&frm=0&rnd=872238704.1755141366&dt=Augment%20Code&auid=**********.**********&navt=n&npa=0&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=1755141365790&tfd=1088&apve=1&apvf=f", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 265.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:09.424113Z", "time": 268.30000001192093, "request": {"method": "GET", "url": "https://googleads.g.doubleclick.net/pagead/viewthroughconversion/***********/?random=1755141365789&cv=11&fst=1755141365789&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBLeGVSZHF6QXpWZlFPMFVnaFl5aE5vWDNtZG5RMUpFVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVCVnVueDFFRHlkeEYwTG1MX2ZfMy16bnNlTEI2MFREo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBLeGVSZHF6QXpWZlFPMFVnaFl5aE5vWDNtZG5RMUpFVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVCVnVueDFFRHlkeEYwTG1MX2ZfMy16bnNlTEI2MFREo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=**********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=4", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2520, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2520}, "cache": {}, "timings": {"send": 0, "wait": 268.30000001192093, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424118Z", "time": 291.70000000298023, "request": {"method": "GET", "url": "https://analytics.google.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=1755141365759&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=*********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBLeGVSZHF6QXpWZlFPMFVnaFl5aE5vWDNtZG5RMUpFVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVCVnVueDFFRHlkeEYwTG1MX2ZfMy16bnNlTEI2MFREo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dr=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBLeGVSZHF6QXpWZlFPMFVnaFl5aE5vWDNtZG5RMUpFVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVCVnVueDFFRHlkeEYwTG1MX2ZfMy16bnNlTEI2MFREo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dt=Augment%20Code&en=page_view&_ee=1&tfd=1100", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 291.70000000298023, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:09.424123Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/ajs-destination.bundle.8e6b895db75187c55313.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424127Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/schemaFilter.bundle.1b218d13fed021531d4e.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424132Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424139Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424146Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/845/431110629a9fe8297174.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424152Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424161Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424168Z", "time": 0, "request": {"method": "GET", "url": "https://us.i.posthog.com/static/array.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424175Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_GIfRcxAmWBSIxniAKUhlM9pQ343wGGnAgC3iBMO1FSN/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:09.424183Z", "time": 331.*************, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755141365842&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 331.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:09.424191Z", "time": 338.69999998807907, "request": {"method": "GET", "url": "https://www.google.com/pagead/1p-user-list/***********/?random=1755141365789&cv=11&fst=1755140400000&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBLeGVSZHF6QXpWZlFPMFVnaFl5aE5vWDNtZG5RMUpFVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVCVnVueDFFRHlkeEYwTG1MX2ZfMy16bnNlTEI2MFREo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBLeGVSZHF6QXpWZlFPMFVnaFl5aE5vWDNtZG5RMUpFVKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEVCVnVueDFFRHlkeEYwTG1MX2ZfMy16bnNlTEI2MFREo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=**********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=3&is_vtc=1&cid=CAQSigEA2abss-QOrlJzLO7GeJO6PjJUrg4dTOqIn8OOhptjrssm3fcqMCKTZymQ2tHiofajA7OGZjUZzsmXvgOHQA4nWaQJTsOOq0ff1fRA5tJF09n2Ac0NRqBxPm5WKsfCX47YNeKqm2FTBHEixzhbFr_gDb31GrM5Jgs8Q7dQNhFrQRguyAxbBaNP_JE&random=1206599116&rmt_tld=0&ipr=y", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 342, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 342}, "cache": {}, "timings": {"send": 0, "wait": 338.69999998807907, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:16:09.424204Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:55.047056Z", "time": 0, "request": {"method": "GET", "url": "https://app.augmentcode.com/account/subscription", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "38986"}], "cookies": [], "content": {"size": 38986, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 38986}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": ""}, {"startedDateTime": "2025-08-14T11:16:55.051648Z", "time": 6730.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-C-JJqdMW.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94361, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 94361}, "cache": {}, "timings": {"send": 0, "wait": 6730.************, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:16:55.051666Z", "time": 240.09999999403954, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-D6aQ-Xs1.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 416, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 416}, "cache": {}, "timings": {"send": 0, "wait": 240.09999999403954, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:16:55.051674Z", "time": 2633.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/tailwind-DxnphuB3.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5101, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 5101}, "cache": {}, "timings": {"send": 0, "wait": 2633.*************, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:16:55.051680Z", "time": 1494.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/entry.client-C92V1BwZ.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1792, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1792}, "cache": {}, "timings": {"send": 0, "wait": 1494.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051691Z", "time": 2435.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/manifest-46feb8ab.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4124, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4124}, "cache": {}, "timings": {"send": 0, "wait": 2435.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051697Z", "time": 5982.70000000298, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-Bi4s4-Io.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 46139, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 46139}, "cache": {}, "timings": {"send": 0, "wait": 5982.70000000298, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051702Z", "time": 3182.4000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B7Ui2t93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5343, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5343}, "cache": {}, "timings": {"send": 0, "wait": 3182.4000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051708Z", "time": 5224.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BS38kjqr.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21235, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21235}, "cache": {}, "timings": {"send": 0, "wait": 5224.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051715Z", "time": 416.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/QueryClientProvider-CeGnmbe-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 696, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 696}, "cache": {}, "timings": {"send": 0, "wait": 416.**************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051721Z", "time": 2034.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/client-only-C74SDDMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2093, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2093}, "cache": {}, "timings": {"send": 0, "wait": 2034.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051729Z", "time": 2697.7000000029802, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryClient.client-Dk3lS3wN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3850, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3850}, "cache": {}, "timings": {"send": 0, "wait": 2697.7000000029802, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051734Z", "time": 4574.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/components--HLsvfrm.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13680, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13680}, "cache": {}, "timings": {"send": 0, "wait": 4574.29999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051740Z", "time": 524.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/card-BBgKeY7L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 865, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 865}, "cache": {}, "timings": {"send": 0, "wait": 524.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051746Z", "time": 554.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/container-BlJCmUTg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 884, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 884}, "cache": {}, "timings": {"send": 0, "wait": 554.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051753Z", "time": 569.7000000029802, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/link-CMt6MnuB.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 998, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 998}, "cache": {}, "timings": {"send": 0, "wait": 569.7000000029802, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051759Z", "time": 4759.4000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/theme-C1ulz75E.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13876, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13876}, "cache": {}, "timings": {"send": 0, "wait": 4759.4000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051765Z", "time": 4789.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index.modern-950P1XoK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14011, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14011}, "cache": {}, "timings": {"send": 0, "wait": 4789.29999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051772Z", "time": 618.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/button-Dvrjyl3p.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 608, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 608}, "cache": {}, "timings": {"send": 0, "wait": 618.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051778Z", "time": 648.1999999880791, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DrFu-skq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1110, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1110}, "cache": {}, "timings": {"send": 0, "wait": 648.1999999880791, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051784Z", "time": 694.1999999880791, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/jotaiStore.client-sdvKmlSn.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 430, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 430}, "cache": {}, "timings": {"send": 0, "wait": 694.1999999880791, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051791Z", "time": 709.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-C8U1uDHl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1132, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1132}, "cache": {}, "timings": {"send": 0, "wait": 709.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051798Z", "time": 2774.199999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/flex-C9XhsxSj.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3855, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3855}, "cache": {}, "timings": {"send": 0, "wait": 2774.199999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051804Z", "time": 3258.699999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Toast-CG_NC-6_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5695, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5695}, "cache": {}, "timings": {"send": 0, "wait": 3258.699999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051811Z", "time": 739.4000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-BkoMXhYo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 462, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 462}, "cache": {}, "timings": {"send": 0, "wait": 739.4000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051818Z", "time": 2159.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CI4icoal.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1970, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1970}, "cache": {}, "timings": {"send": 0, "wait": 2159.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051824Z", "time": 2174.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAuWbg93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1857, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1857}, "cache": {}, "timings": {"send": 0, "wait": 2174.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051831Z", "time": 800.4000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/spinner-Cq6egsy4.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1179, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1179}, "cache": {}, "timings": {"send": 0, "wait": 800.4000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051839Z", "time": 832.4000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAyM6kBC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 766, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 766}, "cache": {}, "timings": {"send": 0, "wait": 832.4000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051845Z", "time": 848, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/get-subtree-8AxxbxX_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 598, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 598}, "cache": {}, "timings": {"send": 0, "wait": 848, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051852Z", "time": 878.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/base-button-Dk95TXPu.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1152, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1152}, "cache": {}, "timings": {"send": 0, "wait": 878.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051860Z", "time": 894.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B5mzPb5P.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 935, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 935}, "cache": {}, "timings": {"send": 0, "wait": 894.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051866Z", "time": 3984.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-icons.esm-g3l3pVh3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8914, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8914}, "cache": {}, "timings": {"send": 0, "wait": 3984.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051874Z", "time": 970.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/useQuery-BvSAfNQo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1398, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1398}, "cache": {}, "timings": {"send": 0, "wait": 970.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051882Z", "time": 2866.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/animations-CMbVnQEg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3734, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3734}, "cache": {}, "timings": {"send": 0, "wait": 2866.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051889Z", "time": 3304.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-Cup3efAs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6206, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6206}, "cache": {}, "timings": {"send": 0, "wait": 3304.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051896Z", "time": 1031.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/BaseHeader-y1wz0aO3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1603, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1603}, "cache": {}, "timings": {"send": 0, "wait": 1031.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051904Z", "time": 2295.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/string-CwzBSc0v.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3135, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3135}, "cache": {}, "timings": {"send": 0, "wait": 2295.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051911Z", "time": 4015.199999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/style-Bv9a6v44.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8859, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8859}, "cache": {}, "timings": {"send": 0, "wait": 4015.199999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051919Z", "time": 1124, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plans-D8s3V0en.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 510, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 510}, "cache": {}, "timings": {"send": 0, "wait": 1124, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051926Z", "time": 1154.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/guards-C20ItfmI.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 963, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 963}, "cache": {}, "timings": {"send": 0, "wait": 1154.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051933Z", "time": 1169.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/subscription-creation-pending-Mylp5-_d.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1169.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051940Z", "time": 1199.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/skeleton-qwMe81ym.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 880, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 880}, "cache": {}, "timings": {"send": 0, "wait": 1199.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051948Z", "time": 1214.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/feature-flags.client-BVZhVN7G.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 431, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 431}, "cache": {}, "timings": {"send": 0, "wait": 1214.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051956Z", "time": 1245.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/box-DvlTT8Qh.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 824, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 824}, "cache": {}, "timings": {"send": 0, "wait": 1245.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051965Z", "time": 1260.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-D-DXDI2l.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1260.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051973Z", "time": 4945.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/user-d_3utlAo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 16299, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 16299}, "cache": {}, "timings": {"send": 0, "wait": 4945.29999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051981Z", "time": 1293.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryOptions-Yjo86aMs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 721, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 721}, "cache": {}, "timings": {"send": 0, "wait": 1293.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051988Z", "time": 1308.4000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/toDate-qOSwr3PX.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 615, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 615}, "cache": {}, "timings": {"send": 0, "wait": 1308.4000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.051997Z", "time": 1400.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/addLeadingZeros-6--iqVZy.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 456, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 456}, "cache": {}, "timings": {"send": 0, "wait": 1400.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052006Z", "time": 2403.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/ProgressPage-CfNQ5HtT.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1971, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1971}, "cache": {}, "timings": {"send": 0, "wait": 2403.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052014Z", "time": 3737.************3, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout-s5dvxSMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 7715, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 7715}, "cache": {}, "timings": {"send": 0, "wait": 3737.************3, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052022Z", "time": 4495.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/proto3-Bmo7MjaP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 10903, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 10903}, "cache": {}, "timings": {"send": 0, "wait": 4495.29999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052031Z", "time": 1416.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/heading-Duq80h8F.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 991, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 991}, "cache": {}, "timings": {"send": 0, "wait": 1416.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052038Z", "time": 1447, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account--DlvNh9L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1172, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1172}, "cache": {}, "timings": {"send": 0, "wait": 1447, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052046Z", "time": 1462.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-BneX_s9R.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 745, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 745}, "cache": {}, "timings": {"send": 0, "wait": 1462.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052054Z", "time": 3007.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/PlanPicker-CZi5-vIO.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4367, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4367}, "cache": {}, "timings": {"send": 0, "wait": 3007.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052064Z", "time": 3783.7000000029802, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Card-BR5rB2rc.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6435, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6435}, "cache": {}, "timings": {"send": 0, "wait": 3783.7000000029802, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052072Z", "time": 1586.199999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/number-BS8GKe3y.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1719, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1719}, "cache": {}, "timings": {"send": 0, "wait": 1586.199999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052081Z", "time": 1601.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/icons--M48DCb3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1161, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1161}, "cache": {}, "timings": {"send": 0, "wait": 1601.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052089Z", "time": 1632.699999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plural-D9YAiM4O.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1040, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1040}, "cache": {}, "timings": {"send": 0, "wait": 1632.699999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052098Z", "time": 1647.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Enabled-BoZ8Au2f.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 860, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 860}, "cache": {}, "timings": {"send": 0, "wait": 1647.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052106Z", "time": 1693.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-C5gnWpVx.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 387, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 387}, "cache": {}, "timings": {"send": 0, "wait": 1693.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052116Z", "time": 1724.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/url-_DgIuZOw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 635, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 635}, "cache": {}, "timings": {"send": 0, "wait": 1724.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052125Z", "time": 1771.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/isBefore-DuJnhAXP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 446, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 446}, "cache": {}, "timings": {"send": 0, "wait": 1771.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052135Z", "time": 1818.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/badge-CrInsKkE.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 986, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 986}, "cache": {}, "timings": {"send": 0, "wait": 1818.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052144Z", "time": 2495.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BEyGE8AK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1912, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1912}, "cache": {}, "timings": {"send": 0, "wait": 2495.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052152Z", "time": 3799, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Badge-CCBfROU-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6905, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6905}, "cache": {}, "timings": {"send": 0, "wait": 3799, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052162Z", "time": 1834.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constructFrom-DWjd9ymD.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 441, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 441}, "cache": {}, "timings": {"send": 0, "wait": 1834.1000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052171Z", "time": 3830.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DzvzAwJl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6526, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6526}, "cache": {}, "timings": {"send": 0, "wait": 3830.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052181Z", "time": 5067.***********, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account.subscription-BBRL8heg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 17282, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 17282}, "cache": {}, "timings": {"send": 0, "wait": 5067.***********, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052190Z", "time": 1886.*************, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400..700,0..1,0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 705, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 705}, "cache": {}, "timings": {"send": 0, "wait": 1886.*************, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:16:55.052208Z", "time": 216.09999999403954, "request": {"method": "GET", "url": "https://app.augmentcode.com/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2118, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2118}, "cache": {}, "timings": {"send": 0, "wait": 216.09999999403954, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:16:55.052220Z", "time": 353.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/user", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 484, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 484}, "cache": {}, "timings": {"send": 0, "wait": 353.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052231Z", "time": 305.************3, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/team/plan-change-pending", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 339, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 339}, "cache": {}, "timings": {"send": 0, "wait": 305.************3, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052240Z", "time": 879.6999999880791, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/subscription", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 800, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 800}, "cache": {}, "timings": {"send": 0, "wait": 879.6999999880791, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052251Z", "time": 739.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/plans", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 943, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 943}, "cache": {}, "timings": {"send": 0, "wait": 739.0999999940395, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052260Z", "time": 257.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/augment-logo.svg", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3975, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 3975}, "cache": {}, "timings": {"send": 0, "wait": 257.**************, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:16:55.052293Z", "time": 798.*************, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=auth.augmentcode.com&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&scrsrc=www.googletagmanager.com&frm=0&rnd=**********.**********&auid=**********.**********&navt=n&npa=1&_tu=AAg&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l3l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=*************&tfd=10614&apve=1&apvf=sb", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 798.*************, "receive": 0}, "resourceType": "beacon"}, {"startedDateTime": "2025-08-14T11:16:55.052303Z", "time": 635.*************, "request": {"method": "GET", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=*************&gcd=13l3l3l3l3l1&npa=1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=*********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&ngs=1&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&dr=https%3A%2F%2Fauth.augmentcode.com%2F&dt=&_tu=AAg&en=page_view&_ee=1&tfd=10625", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 635.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052314Z", "time": 688.5, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/i", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 688.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052325Z", "time": 812, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/p", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 812, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052335Z", "time": 1450.************3, "request": {"method": "GET", "url": "https://analytics.twitter.com/i/adsct?txn_id=pva71&p_id=Twitter&tw_sale_amount=0&tw_order_quantity=0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1450.************3, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:16:55.052346Z", "time": 1453.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/credits", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 381, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 381}, "cache": {}, "timings": {"send": 0, "wait": 1453.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052356Z", "time": 585, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/payment", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 346, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 346}, "cache": {}, "timings": {"send": 0, "wait": 585, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052366Z", "time": 395.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/deletions", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 336, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 336}, "cache": {}, "timings": {"send": 0, "wait": 395.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052377Z", "time": 731.2999999970198, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 731.2999999970198, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:55.052387Z", "time": 435.09999999403954, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755141397670&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 435.09999999403954, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052397Z", "time": 1071.7000000029802, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/recorder.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1071.7000000029802, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:55.052409Z", "time": 540.*************, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 540.*************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:55.052422Z", "time": 382.*************, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/web-vitals.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 382.*************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:16:55.052433Z", "time": 328, "request": {"method": "GET", "url": "https://us.i.posthog.com/e/?ip=0&_=1755141400670&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 328, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052444Z", "time": 204.*************, "request": {"method": "GET", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=*************&gcd=13l3l3l3l3l1&npa=1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=*********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AEAAAAQ&ngs=1&_s=2&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&dr=https%3A%2F%2Fauth.augmentcode.com%2F&dt=&_tu=AAg&en=scroll&epn.percent_scrolled=90&_et=8&tfd=15629", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 204.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052455Z", "time": 350.**************, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=*************&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 350.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:16:55.052465Z", "time": 357.************3, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=1755141411137&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 357.************3, "receive": 0}, "resourceType": "fetch"}], "websockets": [], "pages": [{"startedDateTime": "2025-08-14T11:17:00.659278Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}