2025-08-11 12:32:28 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60241,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 12:32:29 - INFO - <EMAIL>
2025-08-11 12:32:29 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 12:32:40 - INFO - 获取 cap_value 验证码成功...
2025-08-11 12:32:44 - INFO - 找到 Turnstile...
2025-08-11 12:32:44 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 12:32:44 - INFO - 已执行拟人滚动: -130px
2025-08-11 12:32:46 - INFO - 已执行页面焦点操作
2025-08-11 12:32:46 - INFO - 已执行拟人操作完成，总停顿: 0.92秒
2025-08-11 12:32:47 - INFO - 登录后已执行更多拟人操作
2025-08-11 12:32:51 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 12:32:59 - INFO - 验证码已提交，等待跳转...
2025-08-11 12:32:59 - INFO - 已执行拟人滚动: -64px
2025-08-11 12:33:00 - INFO - 已执行拟人操作完成，总停顿: 1.28秒
2025-08-11 12:33:05 - INFO - 
第 1/5 次尝试注册...
2025-08-11 12:33:05 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 12:33:15 - INFO - 已执行拟人滚动: -99px
2025-08-11 12:33:16 - INFO - 已执行页面焦点操作
2025-08-11 12:33:17 - INFO - 已执行拟人操作完成，总停顿: 1.20秒
2025-08-11 12:33:17 - INFO - 成功！当前在订阅页面。
2025-08-11 12:33:17 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 12:33:22 - INFO - 代码信息: {
  "codeVerifier": "RJ8VIw4DJDXWeZHSx0fuar07y1BpEYWQ5k4QUWUur4Q",
  "code_challenge": "9fqMADmRMxpGKaQ2Pr6uJExYPr8O_WYugCrgG2P5lJ8",
  "state": "790b95ba-31ce-4075-ab84-d3860ec7778e"
}
2025-08-11 12:33:22 - INFO - ==================================================
2025-08-11 12:33:22 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_93b8d014ee516c42e0d04bed9f7b8a91&state=790b95ba-31ce-4075-ab84-d3860ec7778e&tenant_url=https%3A%2F%2Fd18.api.augmentcode.com%2F
2025-08-11 12:33:24 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 12:33:24 - INFO - 添加session: <EMAIL>   95a6c2d16df8e8b9da8ea5b89b83c7e6e9efbaa9b7b1dd0aa40c8c3e1fb96377   https://d18.api.augmentcode.com/  2025-08-18T04:33:05Z
2025-08-11 12:33:24 - INFO - email:<EMAIL> === cookie:.eJxNjcFugzAQRP9lzxDVeL3GOeVP0GIvlVW8JAYqRU3-PSjtoccZzXvzA8NVamEV3eC81V0amLjk-T4oF4EzQAOf-Vv0X87pOuyr1CGno5DCeX5QH4LvSNCa1E-d9ckxYorHXBeNB-ksIoXOe-c-gvFonGngrXkbDtNtvasa77DvyaO7rIXrlpb4JfW06JxV4I_4PTaJyHBqo-24RR9iO9IUW2KZ0NAYrRvh-QKwdEWx.aJlykw.LObIU25pbZNjrvy2U40u_7Obric
2025-08-11 12:33:24 - INFO - 
自动化流程成功完成！
2025-08-11 12:33:24 - INFO - 添加第1个
2025-08-11 12:33:45 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60400,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 12:33:45 - INFO - <EMAIL>
2025-08-11 12:33:45 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 12:34:08 - INFO - 获取 cap_value 验证码成功...
2025-08-11 12:34:11 - INFO - 找到 Turnstile...
2025-08-11 12:34:13 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 12:34:13 - INFO - 已执行拟人滚动: -92px
2025-08-11 12:34:14 - INFO - 已执行页面焦点操作
2025-08-11 12:34:15 - INFO - 已执行拟人操作完成，总停顿: 1.36秒
2025-08-11 12:34:15 - INFO - 登录后已执行更多拟人操作
2025-08-11 12:34:19 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 12:34:19 - INFO - 验证码已提交，等待跳转...
2025-08-11 12:34:19 - INFO - 已执行拟人滚动: -129px
2025-08-11 12:34:20 - INFO - 已执行拟人操作完成，总停顿: 0.94秒
2025-08-11 12:34:28 - INFO - 
第 1/5 次尝试注册...
2025-08-11 12:34:28 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 12:34:28 - INFO - 检测到 'Verifying you are human...'，执行鼠标左键点击并随机滑动多次
2025-08-11 12:34:36 - INFO - CDP: 已执行 2 轮鼠标点击/拖动/滚动
2025-08-11 12:34:37 - INFO - 成功！当前在订阅页面。
2025-08-11 12:34:37 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 12:34:42 - INFO - 代码信息: {
  "codeVerifier": "EOYuxyRSUWWsrI0Sug45goORAYtHt1-2qeKR_XgbdQY",
  "code_challenge": "mli61kKFUil4uCDMbzuTeRqDN9AB2S7j_foymqiMMvg",
  "state": "3157a1f2-ec32-48d8-82ad-15bc90b460a9"
}
2025-08-11 12:34:42 - INFO - ==================================================
2025-08-11 12:34:43 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_c52c2ac56d73700d868c5132615cb291&state=3157a1f2-ec32-48d8-82ad-15bc90b460a9&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-11 12:34:44 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 12:34:44 - INFO - 添加session: <EMAIL>   ba267c0c350030f78902bc01d1352c73064000b5de93d236d7bd1215f59b13b2   https://d10.api.augmentcode.com/  2025-08-18T04:34:34Z
2025-08-11 12:34:44 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAURP_lrsFwS5-s_BPStLemkRbSgsGo_y4hLlzOZM6ZF4wLlWQz5RWGtWzUQLApTs8x20QwADRwiw_Kfzn6ZdwqlTH6o6Bk4_SW2hjFnOA9eh1Yr7zsUAd1zPOc3UEKZRiXhgtkUgvGhWzgtJyCQ7TXVHdUgmstNZprTbasfnZ3Kpc5TzET_Ijz13FkQnpsDQuh5dRhazWGNvSeQueddEbA5wtBlUTe.aJly5A.8K28TJnWiaDTG-_3qEbTpLX11Bk
2025-08-11 12:34:44 - INFO - 
自动化流程成功完成！
2025-08-11 12:34:44 - INFO - 添加第2个
2025-08-11 12:35:03 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60594,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 12:35:03 - INFO - <EMAIL>
2025-08-11 12:35:03 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 12:35:20 - INFO - 获取 cap_value 验证码成功...
2025-08-11 12:35:24 - INFO - 找到 Turnstile...
2025-08-11 12:35:26 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 12:35:26 - INFO - 已执行拟人滚动: -44px
2025-08-11 12:35:26 - INFO - 已执行页面焦点操作
2025-08-11 12:35:26 - INFO - 已执行拟人操作完成，总停顿: 0.69秒
2025-08-11 12:35:28 - INFO - 登录后已执行更多拟人操作
2025-08-11 12:35:31 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 12:35:39 - INFO - 验证码已提交，等待跳转...
2025-08-11 12:35:39 - INFO - 已执行拟人滚动: -25px
2025-08-11 12:35:40 - INFO - 已执行拟人操作完成，总停顿: 1.00秒
2025-08-11 12:35:40 - INFO - 
第 1/5 次尝试注册...
2025-08-11 12:35:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 12:35:50 - INFO - 已执行拟人滚动: 139px
2025-08-11 12:35:52 - INFO - 已执行拟人操作完成，总停顿: 1.27秒
2025-08-11 12:35:57 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-11 12:36:13 - INFO - Traceback (most recent call last):

2025-08-11 12:36:13 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1044[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-11 12:36:13 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1011[0m, in [35mmain[0m
    result = attempt_signup_with_retry(driver)

2025-08-11 12:36:13 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m879[0m, in [35mattempt_signup_with_retry[0m
    [31mtime.sleep[0m[1;31m(30)[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^[0m

2025-08-11 12:36:13 - INFO - [1;35mKeyboardInterrupt[0m

