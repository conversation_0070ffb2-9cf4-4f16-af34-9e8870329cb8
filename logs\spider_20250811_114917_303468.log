2025-08-11 11:49:21 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55808,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 11:49:21 - INFO - <EMAIL>
2025-08-11 11:49:21 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 11:49:39 - INFO - 获取 cap_value 验证码成功...
2025-08-11 11:49:43 - INFO - 找到 Turnstile...
2025-08-11 11:49:44 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 11:49:44 - INFO - 已执行拟人滚动: 29px
2025-08-11 11:49:45 - INFO - 已执行拟人操作完成，总停顿: 1.10秒
2025-08-11 11:49:46 - INFO - 登录后已执行更多拟人操作
2025-08-11 11:49:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 11:49:58 - INFO - 验证码已提交，等待跳转...
2025-08-11 11:49:59 - INFO - 
第 1/5 次尝试注册...
2025-08-11 11:49:59 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 11:49:59 - INFO - 检测到 'Verifying you are human...'（首次），使用网络节流模式
2025-08-11 11:49:59 - INFO - 检测到人机验证，执行 CDP 网络节流 10 秒
2025-08-11 11:49:59 - INFO - 执行网络节流前的拟人操作...
2025-08-11 11:49:59 - INFO - 已执行拟人滚动: -49px
2025-08-11 11:50:06 - INFO - 已执行页面焦点操作
2025-08-11 11:50:07 - INFO - 已执行拟人操作完成，总停顿: 1.13秒
2025-08-11 11:50:07 - INFO - CDP: 已将网络设为节流模式（延迟7124ms），保持 9.5 秒……
2025-08-11 11:50:18 - INFO - 执行网络恢复后的拟人操作...
2025-08-11 11:50:18 - INFO - 已执行拟人滚动: -16px
2025-08-11 11:50:19 - INFO - 已执行拟人操作完成，总停顿: 1.17秒
2025-08-11 11:50:19 - INFO - CDP: 网络节流已恢复为正常状态。
2025-08-11 11:50:19 - INFO - 成功！当前在订阅页面。
2025-08-11 11:50:19 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 11:50:24 - INFO - 代码信息: {
  "codeVerifier": "idRuY34O4wEb4gP3Y606DDlZA3WEYc-KSFdE0aRHI9U",
  "code_challenge": "HclWLvp3hnfpQ9-jRhVRvooJ2TU87LA-fMFdX86jYyE",
  "state": "559ed729-117e-4f34-814e-4723c5b6e657"
}
2025-08-11 11:50:24 - INFO - ==================================================
2025-08-11 11:50:24 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_1d89f9e3bd00b5ae27c05ad2db9da2d0&state=559ed729-117e-4f34-814e-4723c5b6e657&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-11 11:50:27 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 11:50:27 - INFO - 添加session: <EMAIL>   63124ab2bee20e68535a662b586f88dbaf942c5e331e460145bec357ab7a9349   https://d8.api.augmentcode.com/  2025-08-18T03:50:05Z
2025-08-11 11:50:27 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2D6sy1dT74JKXQ1DbSQiiZGfXcr8eBtZjLzzRP6lUvymfMGx63cuIGzT3F-9NknhiNAA5d45_znY1j725VLH0MNOPk4v6wjss6QVEZaYoX6zDoYXet5yWNdKrRCEBIq-pZJKdvAjtkJlbQtfrrLzqBzKE13uiZftrCME5fDkueYGX6L_diOZEkH2eIgsEWB2A66KmldN4ydcWgEvD9DXENH.aJlogg.fFPMToerAu3-mkaZwbinkzM_CWg
2025-08-11 11:50:27 - INFO - 已执行拟人滚动: -19px
2025-08-11 11:50:27 - INFO - 已执行页面焦点操作
2025-08-11 11:50:27 - INFO - 已执行拟人操作完成，总停顿: 0.81秒
2025-08-11 11:50:29 - INFO - 邮箱验证后已执行更多拟人操作
2025-08-11 11:50:29 - INFO - 
自动化流程成功完成！
2025-08-11 11:50:29 - INFO - 添加第1个
2025-08-11 11:50:29 - INFO - Traceback (most recent call last):

2025-08-11 11:50:29 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1012[0m, in [35m<module>[0m
    [31mtime.sleep[0m[1;31m(random.uniform(10,20))[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-11 11:50:29 - INFO - [1;35mKeyboardInterrupt[0m

