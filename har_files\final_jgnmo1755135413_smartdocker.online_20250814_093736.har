{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-14T09:37:00.498767Z", "time": 0, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html"}], "cookies": [], "content": {"size": 132235, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 132235}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}}, {"startedDateTime": "2025-08-14T09:37:18.822629Z", "time": 0, "request": {"method": "GET", "url": "https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBsZlpvWE1JZmlNMnNvbmVqWlpSN2NqOFoxNXd5MHlPQ6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHAxR3YwSUZic2VRM2gwQ2R2NENKSWVsS3F4Tmh3NW1mo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}}, {"startedDateTime": "2025-08-14T09:37:33.256185Z", "time": 0, "request": {"method": "GET", "url": "https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=B157L4uNDDcAtNah81wzBN2NbFQ5b7Eicv9VS0GbU3Y&code_challenge=TFwbUZLwBWPlht97BivjtlAlK39CGjLLIbmS9LAf_8w&code_challenge_method=S256", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}}], "websockets": [], "pages": [{"startedDateTime": "2025-08-14T09:37:36.366511Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}