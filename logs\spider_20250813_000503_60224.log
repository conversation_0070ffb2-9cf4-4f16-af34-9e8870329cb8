2025-08-13 00:05:08 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59887,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-13 00:05:08 - INFO - <EMAIL>
2025-08-13 00:05:08 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-13 00:05:24 - INFO - 获取 cap_value 验证码成功...
2025-08-13 00:05:28 - INFO - 找到 Turnstile...
2025-08-13 00:05:31 - INFO - 登录信息已提交，等待验证页面...
2025-08-13 00:05:35 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-13 00:05:35 - INFO - CDP:启用节流模式
2025-08-13 00:05:35 - INFO - 验证码已提交，等待跳转...
2025-08-13 00:05:36 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-13 00:05:45 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-13 00:05:45 - INFO - ReCAPTCHA Token: 
2025-08-13 00:05:45 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-13 00:06:01 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff633066e75+26517]
	(No symbol) [0x0x7ff632fc0780]
	(No symbol) [0x0x7ff632e49c1c]
	(No symbol) [0x0x7ff632efb9be]
	(No symbol) [0x0x7ff632ec846a]
	(No symbol) [0x0x7ff632ef065c]
	(No symbol) [0x0x7ff632ec8243]
	(No symbol) [0x0x7ff632e91431]
	(No symbol) [0x0x7ff632e921c3]
	GetHandleVerifier [0x0x7ff6333f84ad+3767757]
	GetHandleVerifier [0x0x7ff63341bb03+3912739]
	GetHandleVerifier [0x0x7ff63341009d+3865021]
	GetHandleVerifier [0x0x7ff63314827e+949150]
	(No symbol) [0x0x7ff632fcc59f]
	(No symbol) [0x0x7ff632fc7f54]
	(No symbol) [0x0x7ff632fc8109]
	(No symbol) [0x0x7ff632fb6c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-13 00:06:01 - INFO - 
第 1/5 次尝试注册...
2025-08-13 00:06:01 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-13 00:06:01 - INFO - 成功！当前在订阅页面。
2025-08-13 00:06:01 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-13 00:06:04 - INFO - 代码信息: {
  "codeVerifier": "m1I1N9GLrSLfTFz_wr3M20Zda29e8Fc3M3MKhYFysMw",
  "code_challenge": "mvelN0iGz3KL-Gq9Lo4hu1CxcjxGsM62GyCGxBIkF7w",
  "state": "b663bd64-1ee3-4290-9d60-565926aa475a"
}
2025-08-13 00:06:04 - INFO - ==================================================
2025-08-13 00:06:05 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_69241ea4ac326e7c48417be4cb5c94df&state=b663bd64-1ee3-4290-9d60-565926aa475a&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-13 00:06:08 - INFO - 添加session成功: {'status': 'success'}
2025-08-13 00:06:08 - INFO - 添加session: <EMAIL>   23cf5756d5a96b6a8fa58dd67e9e47b17d25f40ca1b947e3f924a67287157ac3   https://d6.api.augmentcode.com/  2025-08-19T16:05:49Z
2025-08-13 00:06:08 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3YFBhvWp_6JtYalQjVriziVojT_HmT10MtIM5p584Tl4FpIWE6Yz3rnDhKVvD0WocIwA3TwlX9Y_vkcj-V-47rk2AIulLffyeM6TRat0dGnwbhokDBQq8suoS2d00qNSjf16MyIpoOLcgEa6IgSSLuxdaxT5vNWqJ5xD99cP3bZsjD8La5fnVbjUdk-qYi9tT72GCz1q8NBewzOJITXG0fARCo.aJtmbA.K9y32lX8dqyus5xqWMnfAziP_2s
2025-08-13 00:06:08 - INFO - 
自动化流程成功完成！
2025-08-13 00:06:08 - INFO - 添加第1个
2025-08-13 00:06:21 - INFO - Traceback (most recent call last):

2025-08-13 00:06:21 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1245[0m, in [35m<module>[0m
    [31mtime.sleep[0m[1;31m(random.uniform(10,20))[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-13 00:06:21 - INFO - [1;35mKeyboardInterrupt[0m

