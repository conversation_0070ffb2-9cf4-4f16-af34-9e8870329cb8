{"captureTime": "2025-08-14T09:36:53.656152Z", "totalConnections": 1, "connections": [{"url": "wss://app.augmentcode.com/ws", "status": "closed", "createdDateTime": "2025-08-14T09:36:53.654346Z", "closedDateTime": "2025-08-14T09:36:53.654356Z", "totalFrames": 4, "sentFrames": 2, "receivedFrames": 2, "handshakeResponse": {"status": 101, "statusText": "Switching Protocols", "headers": {"Upgrade": "websocket", "Connection": "Upgrade", "Sec-WebSocket-Accept": "test-accept-key"}, "timestamp": 1755135413.6543505}, "frames": [{"type": "sent", "timestamp": 1755135413.6543517, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"auth\",\"token\":\"test-token\"}", "payloadLength": 35}, {"type": "received", "timestamp": 1755135413.7543528, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"auth_success\",\"user_id\":\"12345\"}", "payloadLength": 40}, {"type": "sent", "timestamp": 1755135414.654354, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"ping\"}", "payloadLength": 15}, {"type": "received", "timestamp": 1755135414.7543554, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"pong\"}", "payloadLength": 15}]}]}