2025-08-11 13:34:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53660,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 13:34:44 - INFO - <EMAIL>
2025-08-11 13:34:44 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 13:34:59 - INFO - 获取 cap_value 验证码成功...
2025-08-11 13:35:03 - INFO - 找到 Turnstile...
2025-08-11 13:35:06 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 13:35:06 - INFO - 已执行拟人滚动: -67px
2025-08-11 13:35:07 - INFO - 已执行页面焦点操作
2025-08-11 13:35:07 - INFO - 已执行拟人操作完成，总停顿: 1.48秒
2025-08-11 13:35:09 - INFO - 登录后已执行更多拟人操作
2025-08-11 13:35:12 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 13:35:21 - INFO - 验证码已提交，等待跳转...
2025-08-11 13:35:21 - INFO - 已执行拟人滚动: 132px
2025-08-11 13:35:30 - INFO - 已执行页面焦点操作
2025-08-11 13:35:31 - INFO - 已执行拟人操作完成，总停顿: 1.31秒
2025-08-11 13:35:31 - INFO - 
第 1/5 次尝试注册...
2025-08-11 13:35:31 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 13:35:41 - INFO - 已执行拟人滚动: -56px
2025-08-11 13:35:41 - INFO - 已执行拟人操作完成，总停顿: 0.78秒
2025-08-11 13:35:41 - INFO - 成功！当前在订阅页面。
2025-08-11 13:35:41 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 13:35:46 - INFO - 代码信息: {
  "codeVerifier": "IcJ_lTQftXi3mmHFL5TZmFfk-vzqDbmkACGQUMQ18rE",
  "code_challenge": "zT70MRxobnE5yjZgbJ0TzxOlDSM85oIN0ollyu_f3S4",
  "state": "717f987c-7b21-4925-bfce-493653e90e5a"
}
2025-08-11 13:35:46 - INFO - ==================================================
2025-08-11 13:35:47 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_986f63034a4556db37f3f65f2bc46983&state=717f987c-7b21-4925-bfce-493653e90e5a&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-11 13:35:50 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 13:35:50 - INFO - 添加session: <EMAIL>   fee0f42043109ed847af0e6d22584a5c0e28cc32547a48c61dca74a902542e3e   https://d12.api.augmentcode.com/  2025-08-18T05:35:27Z
2025-08-11 13:35:50 - INFO - email:<EMAIL> === cookie:.eJxNjc1qwzAQhN9lz3bR6neVU9_EyNa6qLXWQXECpcm7V5geehmYYeabH5iu3GoSlgMuR7vzAGuqZfueJFWGC8AAH-XB8s-XfJ3uN25TyT3gmsr29BQjoUrWYKZVm5CddRbnXpddlr4k7VGh7YraeG9QqQFOzEnopO1RPwWDsxSVDfR-q6kdeV--uL3tshVh-FucxzYYE5VSIwYdRxtJj8SZxxnJutUtPvAMr187YkOq.aJmBNQ.U50eLTYB-8fFGo1PjVB7tJ6htNE
2025-08-11 13:35:50 - INFO - 
自动化流程成功完成！
2025-08-11 13:35:50 - INFO - 添加第1个
2025-08-11 13:36:10 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53823,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 13:36:11 - INFO - <EMAIL>
2025-08-11 13:36:11 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 13:36:28 - INFO - 获取 cap_value 验证码成功...
2025-08-11 13:36:32 - INFO - 找到 Turnstile...
2025-08-11 13:36:34 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 13:36:34 - INFO - 已执行拟人滚动: 112px
2025-08-11 13:36:35 - INFO - 已执行拟人操作完成，总停顿: 1.23秒
2025-08-11 13:36:37 - INFO - 登录后已执行更多拟人操作
2025-08-11 13:36:40 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 13:36:40 - INFO - 验证码已提交，等待跳转...
2025-08-11 13:36:40 - INFO - 已执行拟人滚动: -105px
2025-08-11 13:36:41 - INFO - 已执行拟人操作完成，总停顿: 0.88秒
2025-08-11 13:36:49 - INFO - 
第 1/5 次尝试注册...
2025-08-11 13:36:49 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 13:36:49 - INFO - 检测到 'Verifying you are human...'，执行鼠标左键点击并随机滑动多次
2025-08-11 13:36:56 - INFO - CDP: 已执行 2 轮鼠标点击/拖动/滚动
2025-08-11 13:36:57 - INFO - 成功！当前在订阅页面。
2025-08-11 13:36:57 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 13:37:02 - INFO - 代码信息: {
  "codeVerifier": "lfhNZSX__he9GQ_S2d5BI6_j1OC2mETvMpJL4ZMue4Q",
  "code_challenge": "s8LwEL065H7wpiJ2H-RjofVdd0pevLYQ_azg_mlfTLE",
  "state": "6249554b-a19f-4c4c-9989-6fc6a8873927"
}
2025-08-11 13:37:02 - INFO - ==================================================
2025-08-11 13:37:03 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_6dfe6dae0a7f5e3b2c4fe1468b40916f&state=6249554b-a19f-4c4c-9989-6fc6a8873927&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-11 13:37:05 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 13:37:05 - INFO - 添加session: <EMAIL>   ae21f2fe8703de9bfb40d5b4760d72ee20d32db989ea2e5e4d083062962ab595   https://d6.api.augmentcode.com/  2025-08-18T05:36:55Z
2025-08-11 13:37:05 - INFO - email:<EMAIL> === cookie:.eJxNjc0OwiAQhN9lz62Bsvx58k0IKYtBC21oa2LUd5c0HjzMYSYz37zALVSzL1Q2OG91pw6iz2l6uuIzwRmgg2t6UPnzKSxuX6m6FFpA2afprYy1hqsBBQ8mDkIH6VEOotXLXMa2RK0YKtScM8ssH4TiHRyYg9BIaU3-xrVEY5lUeFmzr1uYxzvV01ymVAh-i-NYmSD1aGKPkZkeG7K3AXmvjdCKmjBK-HwBMwZDXQ.aJmBgA.GO5RdgmdpC-lPD9OFYXNu9OkFNA
2025-08-11 13:37:05 - INFO - 
自动化流程成功完成！
2025-08-11 13:37:05 - INFO - 添加第2个
2025-08-11 13:37:25 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53983,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 13:37:25 - INFO - <EMAIL>
2025-08-11 13:37:25 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 13:37:36 - INFO - 获取 cap_value 验证码成功...
2025-08-11 13:37:40 - INFO - 找到 Turnstile...
2025-08-11 13:37:40 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 13:37:40 - INFO - 已执行拟人滚动: -32px
2025-08-11 13:37:41 - INFO - 已执行页面焦点操作
2025-08-11 13:37:42 - INFO - 已执行拟人操作完成，总停顿: 1.19秒
2025-08-11 13:37:44 - INFO - 登录后已执行更多拟人操作
2025-08-11 13:37:47 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 13:37:57 - INFO - 验证码已提交，等待跳转...
2025-08-11 13:37:57 - INFO - 已执行拟人滚动: 39px
2025-08-11 13:37:58 - INFO - 已执行拟人操作完成，总停顿: 1.02秒
2025-08-11 13:38:03 - INFO - 
第 1/5 次尝试注册...
2025-08-11 13:38:03 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 13:38:13 - INFO - 已执行拟人滚动: -92px
2025-08-11 13:38:15 - INFO - 已执行拟人操作完成，总停顿: 1.16秒
2025-08-11 13:38:15 - INFO - 成功！当前在订阅页面。
2025-08-11 13:38:15 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 13:38:20 - INFO - 代码信息: {
  "codeVerifier": "FUEzfkAMWlxjlIdQezGqoUbaqwoUjknJ3BkKyZQNVBo",
  "code_challenge": "PoIDN8od7sAKvlmoit1xPyjjbwzaGgpqvGVMsW38MXc",
  "state": "76edc29c-ffa4-4ab2-bba6-7ff67da4c298"
}
2025-08-11 13:38:20 - INFO - ==================================================
2025-08-11 13:38:20 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_b6f7d4c0acd3b949b0ade2b5511b062e&state=76edc29c-ffa4-4ab2-bba6-7ff67da4c298&tenant_url=https%3A%2F%2Fd7.api.augmentcode.com%2F
2025-08-11 13:38:22 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 13:38:22 - INFO - 添加session: <EMAIL>   490e5910e4ab67ad6eabbf0566b7e594195c5ef20044873d04daca02347f74f2   https://d7.api.augmentcode.com/  2025-08-18T05:38:03Z
2025-08-11 13:38:22 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgjAQRP9lz2BoS6HLyT8h23arVVpMBROj_ruEePA4k3lvXjDeuCTKnBcYlrJyBYFSnJ5jpsQwAFRwig_Ofzn627jeuYzRbwUnitO7M4hGkG6V8CZI1XvNZIPb5nnObiOVQNkLFFJ2vVS6RW0q2DW7YTOFy-LOotetwaZTeLwnKouf3ZXLYc5TzAw_Yj-2VtigVVM76aluBanamsbVXjYWO1ReGoTPF86IRUc.aJmBzg.0cnYymiZgjcoqu2FBIKg84l0pFU
2025-08-11 13:38:22 - INFO - 
自动化流程成功完成！
2025-08-11 13:38:22 - INFO - 添加第3个
2025-08-11 13:38:39 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54135,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 13:38:40 - INFO - <EMAIL>
2025-08-11 13:38:40 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 13:38:51 - INFO - 获取 cap_value 验证码成功...
2025-08-11 13:38:55 - INFO - 找到 Turnstile...
2025-08-11 13:38:56 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 13:38:56 - INFO - 已执行拟人滚动: -60px
2025-08-11 13:38:57 - INFO - 已执行拟人操作完成，总停顿: 0.88秒
2025-08-11 13:38:59 - INFO - 登录后已执行更多拟人操作
2025-08-11 13:39:02 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 13:39:02 - INFO - 验证码已提交，等待跳转...
2025-08-11 13:39:11 - INFO - 已执行拟人滚动: -117px
2025-08-11 13:39:11 - INFO - 已执行拟人操作完成，总停顿: 0.90秒
2025-08-11 13:39:12 - INFO - 
第 1/5 次尝试注册...
2025-08-11 13:39:12 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 13:39:21 - INFO - Traceback (most recent call last):

2025-08-11 13:39:21 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-11 13:39:21 - INFO - [1;35mConnectionResetError[0m: [35m[WinError 10054] 远程主机强迫关闭了一个现有的连接。[0m

2025-08-11 13:39:21 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-11 13:39:21 - INFO - Traceback (most recent call last):

2025-08-11 13:39:21 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1043[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1010[0m, in [35mmain[0m
    result = attempt_signup_with_retry(driver)

2025-08-11 13:39:21 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m838[0m, in [35mattempt_signup_with_retry[0m
    [31mWebDriverWait(driver, 1).until[0m[1;31m([0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mEC.presence_of_element_located((By.XPATH, "//*[contains(., 'Verifying you are human')]"))[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\support\wait.py"[0m, line [35m129[0m, in [35muntil[0m
    value = method(self._driver)

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\support\expected_conditions.py"[0m, line [35m104[0m, in [35m_predicate[0m
    return [31mdriver.find_element[0m[1;31m(*locator)[0m
           [31m~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m922[0m, in [35mfind_element[0m
    return [31mself.execute[0m[1;31m(Command.FIND_ELEMENT, {"using": by, "value": value})[0m["value"]
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m451[0m, in [35mexecute[0m
    response = cast(RemoteConnection, self.command_executor).execute(driver_command, params)

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m404[0m, in [35mexecute[0m
    return [31mself._request[0m[1;31m(command_info[0], url, body=data)[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m428[0m, in [35m_request[0m
    response = self._conn.request(method, url, body=body, headers=headers, timeout=self._client_config.timeout)

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m143[0m, in [35mrequest[0m
    return [31mself.request_encode_body[0m[1;31m([0m
           [31m~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mmethod, url, fields=fields, headers=headers, **urlopen_kw[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m278[0m, in [35mrequest_encode_body[0m
    return [31mself.urlopen[0m[1;31m(method, url, **extra_kw)[0m
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\poolmanager.py"[0m, line [35m459[0m, in [35murlopen[0m
    response = conn.urlopen(method, u.request_uri, **kw)

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-11 13:39:21 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1432[0m, in [35mgetresponse[0m
    [31mself.close[0m[1;31m()[0m
    [31m~~~~~~~~~~[0m[1;31m^^[0m

2025-08-11 13:39:21 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m369[0m, in [35mclose[0m
    def close(self) -> None:
    

2025-08-11 13:39:21 - INFO - [1;35mKeyboardInterrupt[0m

