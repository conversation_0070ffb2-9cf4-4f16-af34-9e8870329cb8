{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-14T11:37:51.591298Z", "time": 0, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "132235"}], "cookies": [], "content": {"size": 132235, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 132235}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "工作台"}, {"startedDateTime": "2025-08-14T11:37:51.596429Z", "time": 5.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DWNmc_MY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21479, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21479}, "cache": {}, "timings": {"send": 0, "wait": 5.299999997019768, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:37:51.596439Z", "time": 7.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CIEDqDGY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 468, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 468}, "cache": {}, "timings": {"send": 0, "wait": 7.799999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596442Z", "time": 8.100000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-B9PrHQl7.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4414, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4414}, "cache": {}, "timings": {"send": 0, "wait": 8.100000008940697, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596445Z", "time": 8.400000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-H8ttwM1S.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 474, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 474}, "cache": {}, "timings": {"send": 0, "wait": 8.400000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596448Z", "time": 5.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-BKOrM9G3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 146848, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 146848}, "cache": {}, "timings": {"send": 0, "wait": 5.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596450Z", "time": 12.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DjWl3JGw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 786361, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 786361}, "cache": {}, "timings": {"send": 0, "wait": 12.299999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596453Z", "time": 11.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BN5KR6Qs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5129, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5129}, "cache": {}, "timings": {"send": 0, "wait": 11.599999994039536, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596456Z", "time": 11.899999991059303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtKobjF2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14336, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14336}, "cache": {}, "timings": {"send": 0, "wait": 11.899999991059303, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596459Z", "time": 13.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-R6XwfvJN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3351, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3351}, "cache": {}, "timings": {"send": 0, "wait": 13.299999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596462Z", "time": 13.700000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DeQidb8Q.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6357, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6357}, "cache": {}, "timings": {"send": 0, "wait": 13.700000002980232, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596465Z", "time": 14, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-kZT51Bed.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2762, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2762}, "cache": {}, "timings": {"send": 0, "wait": 14, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596468Z", "time": 14.*************32, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-Bd2HX4Pl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5330, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5330}, "cache": {}, "timings": {"send": 0, "wait": 14.*************32, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596472Z", "time": 13.400000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BRMOCcEH.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 130305, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 130305}, "cache": {}, "timings": {"send": 0, "wait": 13.400000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596475Z", "time": 8.400000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-C_thiKp4.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3318, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 3318}, "cache": {}, "timings": {"send": 0, "wait": 8.400000005960464, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:37:51.596478Z", "time": 8.700000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DwV2T02E.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 50384, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 50384}, "cache": {}, "timings": {"send": 0, "wait": 8.700000002980232, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:37:51.596482Z", "time": 14, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_user_key-BQXKMoNP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2168, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2168}, "cache": {}, "timings": {"send": 0, "wait": 14, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596485Z", "time": 10.899999991059303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-ntgrtH_u.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1564, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 1564}, "cache": {}, "timings": {"send": 0, "wait": 10.899999991059303, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:37:51.596489Z", "time": 11.099999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtkFttLu.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 42774, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 42774}, "cache": {}, "timings": {"send": 0, "wait": 11.099999994039536, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:37:51.596493Z", "time": 11.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DDWJuWk8.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 9043, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 9043}, "cache": {}, "timings": {"send": 0, "wait": 11.299999997019768, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:37:51.596497Z", "time": 1.5999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/app/config", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 408, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 408}, "cache": {}, "timings": {"send": 0, "wait": 1.5999999940395355, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:37:51.596502Z", "time": 2, "request": {"method": "GET", "url": "http://127.0.0.1:45535/zh-CN-DTI-qNBp.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94467, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 94467}, "cache": {}, "timings": {"send": 0, "wait": 2, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:37:51.596505Z", "time": 3.7000000029802322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/list?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6465, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 6465}, "cache": {}, "timings": {"send": 0, "wait": 3.7000000029802322, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:37:51.596509Z", "time": 4.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/proxy/detection?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 654, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 654}, "cache": {}, "timings": {"send": 0, "wait": 4.799999997019768, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:37:51.596513Z", "time": 4.399999991059303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/core-info?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 406, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 406}, "cache": {}, "timings": {"send": 0, "wait": 4.399999991059303, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:37:51.596518Z", "time": 2.4000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_spin-Dat2nEi2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 822, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 822}, "cache": {}, "timings": {"send": 0, "wait": 2.4000000059604645, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:37:51.596521Z", "time": 2.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob1-CCAm7wau.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1143, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1143}, "cache": {}, "timings": {"send": 0, "wait": 2.5, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:37:51.596526Z", "time": 2.5999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob2-CEoTd5f3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1157, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1157}, "cache": {}, "timings": {"send": 0, "wait": 2.5999999940395355, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:37:51.596530Z", "time": 23.900000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/JetBrains%20Mono/JetBrainsMono-Bold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 278128, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 278128}, "cache": {}, "timings": {"send": 0, "wait": 23.900000005960464, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:37:51.596534Z", "time": 25.*************32, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Regular.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 407356, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 407356}, "cache": {}, "timings": {"send": 0, "wait": 25.*************32, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:37:51.596538Z", "time": 25.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Medium.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 411800, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 411800}, "cache": {}, "timings": {"send": 0, "wait": 25.299999997019768, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:37:51.596543Z", "time": 25.30000001192093, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-SemiBold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 414276, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 414276}, "cache": {}, "timings": {"send": 0, "wait": 25.30000001192093, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:37:51.596547Z", "time": 5.700000002980232, "request": {"method": "GET", "url": "http://127.0.0.1:45535/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 285778, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 285778}, "cache": {}, "timings": {"send": 0, "wait": 5.700000002980232, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:37:51.596552Z", "time": 2.*************322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_speed_test-2BHwaLbY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2258, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2258}, "cache": {}, "timings": {"send": 0, "wait": 2.*************322, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:37:51.596557Z", "time": 2.*************322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/US-BXoNwYaC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5807, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5807}, "cache": {}, "timings": {"send": 0, "wait": 2.*************322, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.359781Z", "time": 0, "request": {"method": "GET", "url": "https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBoSHRFTl9hdHJhakJ4TjZLOXIwNFhGWVVaT1IycElMUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDN0bFN5WW1VTEhwaGI1VGMzN0diVS04YjdJZDFVS05Vo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "48699"}], "cookies": [], "content": {"size": 48699, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 48699}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Code"}, {"startedDateTime": "2025-08-14T11:38:09.363635Z", "time": 0, "request": {"method": "GET", "url": "https://cdn.auth0.com/ulp/react-components/1.146.2/css/main.cdn.min.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:38:09.363652Z", "time": 0, "request": {"method": "GET", "url": "https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363661Z", "time": 184.79999999701977, "request": {"method": "GET", "url": "https://www.augmentcode.com/android-chrome-512x512.png", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "image/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 184.79999999701977, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:38:09.363669Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363677Z", "time": 1, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/v1/projects/ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg/settings", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:09.363685Z", "time": 177.59999999403954, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=login.augmentcode.com&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge&scrsrc=www.googletagmanager.com&frm=0&rnd=10149796.1755142686&dt=Augment%20Code&auid=**********.**********&navt=n&npa=0&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=1755142685960&tfd=893&apve=1&apvf=sb", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 177.59999999403954, "receive": 0}, "resourceType": "beacon"}, {"startedDateTime": "2025-08-14T11:38:09.363693Z", "time": 193.**************, "request": {"method": "GET", "url": "https://googleads.g.doubleclick.net/pagead/viewthroughconversion/***********/?random=1755142685958&cv=11&fst=1755142685958&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBoSHRFTl9hdHJhakJ4TjZLOXIwNFhGWVVaT1IycElMUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDN0bFN5WW1VTEhwaGI1VGMzN0diVS04YjdJZDFVS05Vo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBoSHRFTl9hdHJhakJ4TjZLOXIwNFhGWVVaT1IycElMUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDN0bFN5WW1VTEhwaGI1VGMzN0diVS04YjdJZDFVS05Vo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=**********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=4", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2515, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2515}, "cache": {}, "timings": {"send": 0, "wait": 193.**************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363702Z", "time": 196.59999999403954, "request": {"method": "GET", "url": "https://analytics.google.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=1755142685934&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=**********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBoSHRFTl9hdHJhakJ4TjZLOXIwNFhGWVVaT1IycElMUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDN0bFN5WW1VTEhwaGI1VGMzN0diVS04YjdJZDFVS05Vo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dr=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBoSHRFTl9hdHJhakJ4TjZLOXIwNFhGWVVaT1IycElMUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDN0bFN5WW1VTEhwaGI1VGMzN0diVS04YjdJZDFVS05Vo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dt=Augment%20Code&en=page_view&_ee=1&tfd=907", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 196.59999999403954, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:09.363711Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/ajs-destination.bundle.8e6b895db75187c55313.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363719Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/schemaFilter.bundle.1b218d13fed021531d4e.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363727Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363735Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363743Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/845/431110629a9fe8297174.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363752Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363761Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363770Z", "time": 0, "request": {"method": "GET", "url": "https://us.i.posthog.com/static/array.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363778Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_GIfRcxAmWBSIxniAKUhlM9pQ343wGGnAgC3iBMO1FSN/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:09.363788Z", "time": 224.*************3, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755142686016&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 224.*************3, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:09.363798Z", "time": 180.5, "request": {"method": "GET", "url": "https://www.google.com/pagead/1p-user-list/***********/?random=1755142685958&cv=11&fst=1755140400000&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBoSHRFTl9hdHJhakJ4TjZLOXIwNFhGWVVaT1IycElMUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDN0bFN5WW1VTEhwaGI1VGMzN0diVS04YjdJZDFVS05Vo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBoSHRFTl9hdHJhakJ4TjZLOXIwNFhGWVVaT1IycElMUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDN0bFN5WW1VTEhwaGI1VGMzN0diVS04YjdJZDFVS05Vo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=**********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=3&is_vtc=1&cid=CAQSigEA2abssxtSVqiOJjFUzgFyVnK1XGR6aMdshxIASctFOWqXuZtF6T5r6nG7dl6rOu-L_LKevYBPiSdpYHgywZ_lioVrEqkeP1-PpM2rO80RGiwZRwrR16JRnHzVPlefu2K_HVVD50updYlkNDiyovhNCGlrRQm5Fs1IxbRcLlzoX_x9hai9OOrT108&random=4098674772&rmt_tld=0&ipr=y", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 342, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 342}, "cache": {}, "timings": {"send": 0, "wait": 180.5, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:38:09.363807Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:57.362351Z", "time": 0, "request": {"method": "GET", "url": "https://app.augmentcode.com/account/subscription", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "38986"}], "cookies": [], "content": {"size": 38986, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 38986}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": ""}, {"startedDateTime": "2025-08-14T11:38:57.367228Z", "time": 6701, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-C-JJqdMW.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94361, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 94361}, "cache": {}, "timings": {"send": 0, "wait": 6701, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:38:57.367245Z", "time": 3001.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/tailwind-DxnphuB3.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5101, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 5101}, "cache": {}, "timings": {"send": 0, "wait": 3001.6000000089407, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:38:57.367257Z", "time": 226, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-D6aQ-Xs1.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 416, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 416}, "cache": {}, "timings": {"send": 0, "wait": 226, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:38:57.367267Z", "time": 2564.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/manifest-46feb8ab.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4124, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4124}, "cache": {}, "timings": {"send": 0, "wait": 2564.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.373968Z", "time": 1850.8999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/entry.client-C92V1BwZ.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1792, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1792}, "cache": {}, "timings": {"send": 0, "wait": 1850.8999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.373985Z", "time": 3184.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B7Ui2t93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5343, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5343}, "cache": {}, "timings": {"send": 0, "wait": 3184.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.373994Z", "time": 5248, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BS38kjqr.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21235, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21235}, "cache": {}, "timings": {"send": 0, "wait": 5248, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374002Z", "time": 5974.79999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-Bi4s4-Io.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 46139, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 46139}, "cache": {}, "timings": {"send": 0, "wait": 5974.79999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374010Z", "time": 416.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/QueryClientProvider-CeGnmbe-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 696, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 696}, "cache": {}, "timings": {"send": 0, "wait": 416.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374017Z", "time": 2703.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryClient.client-Dk3lS3wN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3850, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3850}, "cache": {}, "timings": {"send": 0, "wait": 2703.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374024Z", "time": 4601.20000000298, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/components--HLsvfrm.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13680, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13680}, "cache": {}, "timings": {"send": 0, "wait": 4601.20000000298, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374031Z", "time": 523.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/container-BlJCmUTg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 884, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 884}, "cache": {}, "timings": {"send": 0, "wait": 523.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374039Z", "time": 570.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/card-BBgKeY7L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 865, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 865}, "cache": {}, "timings": {"send": 0, "wait": 570.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374048Z", "time": 2018.699999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/client-only-C74SDDMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2093, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2093}, "cache": {}, "timings": {"send": 0, "wait": 2018.699999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374056Z", "time": 4783.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index.modern-950P1XoK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14011, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14011}, "cache": {}, "timings": {"send": 0, "wait": 4783.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374063Z", "time": 4814.399999991059, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/theme-C1ulz75E.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13876, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13876}, "cache": {}, "timings": {"send": 0, "wait": 4814.399999991059, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374072Z", "time": 554.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/link-CMt6MnuB.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 998, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 998}, "cache": {}, "timings": {"send": 0, "wait": 554.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374080Z", "time": 2765.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/flex-C9XhsxSj.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3855, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3855}, "cache": {}, "timings": {"send": 0, "wait": 2765.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374088Z", "time": 617.3000000119209, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/button-Dvrjyl3p.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 608, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 608}, "cache": {}, "timings": {"send": 0, "wait": 617.3000000119209, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374096Z", "time": 3276.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Toast-CG_NC-6_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5695, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5695}, "cache": {}, "timings": {"send": 0, "wait": 3276.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374106Z", "time": 663.7000000029802, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DrFu-skq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1110, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1110}, "cache": {}, "timings": {"send": 0, "wait": 663.7000000029802, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374114Z", "time": 694, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/jotaiStore.client-sdvKmlSn.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 430, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 430}, "cache": {}, "timings": {"send": 0, "wait": 694, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374122Z", "time": 708.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-C8U1uDHl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1132, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1132}, "cache": {}, "timings": {"send": 0, "wait": 708.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374133Z", "time": 739.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-BkoMXhYo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 462, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 462}, "cache": {}, "timings": {"send": 0, "wait": 739.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374143Z", "time": 832.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/spinner-Cq6egsy4.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1179, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1179}, "cache": {}, "timings": {"send": 0, "wait": 832.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374152Z", "time": 2158, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CI4icoal.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1970, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1970}, "cache": {}, "timings": {"send": 0, "wait": 2158, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374161Z", "time": 2172.3999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAuWbg93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1857, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1857}, "cache": {}, "timings": {"send": 0, "wait": 2172.3999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374170Z", "time": 801.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAyM6kBC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 766, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 766}, "cache": {}, "timings": {"send": 0, "wait": 801.1000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374179Z", "time": 847.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/get-subtree-8AxxbxX_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 598, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 598}, "cache": {}, "timings": {"send": 0, "wait": 847.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374188Z", "time": 877.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/base-button-Dk95TXPu.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1152, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1152}, "cache": {}, "timings": {"send": 0, "wait": 877.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374197Z", "time": 893.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B5mzPb5P.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 935, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 935}, "cache": {}, "timings": {"send": 0, "wait": 893.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374206Z", "time": 4007.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-icons.esm-g3l3pVh3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8914, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8914}, "cache": {}, "timings": {"send": 0, "wait": 4007.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374215Z", "time": 971.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/useQuery-BvSAfNQo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1398, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1398}, "cache": {}, "timings": {"send": 0, "wait": 971.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374229Z", "time": 2858.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/animations-CMbVnQEg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3734, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3734}, "cache": {}, "timings": {"send": 0, "wait": 2858.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374238Z", "time": 3323.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-Cup3efAs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6206, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6206}, "cache": {}, "timings": {"send": 0, "wait": 3323.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374248Z", "time": 4023.699999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/style-Bv9a6v44.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8859, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8859}, "cache": {}, "timings": {"send": 0, "wait": 4023.699999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374258Z", "time": 1032.7000000029802, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/BaseHeader-y1wz0aO3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1603, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1603}, "cache": {}, "timings": {"send": 0, "wait": 1032.7000000029802, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374268Z", "time": 1093.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/guards-C20ItfmI.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 963, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 963}, "cache": {}, "timings": {"send": 0, "wait": 1093.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374278Z", "time": 1124.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plans-D8s3V0en.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 510, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 510}, "cache": {}, "timings": {"send": 0, "wait": 1124.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374288Z", "time": 1153.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/subscription-creation-pending-Mylp5-_d.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1153.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374299Z", "time": 2297.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/string-CwzBSc0v.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3135, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3135}, "cache": {}, "timings": {"send": 0, "wait": 2297.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374309Z", "time": 4938.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/user-d_3utlAo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 16299, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 16299}, "cache": {}, "timings": {"send": 0, "wait": 4938.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374319Z", "time": 1169, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/skeleton-qwMe81ym.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 880, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 880}, "cache": {}, "timings": {"send": 0, "wait": 1169, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374329Z", "time": 1184.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/feature-flags.client-BVZhVN7G.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 431, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 431}, "cache": {}, "timings": {"send": 0, "wait": 1184.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374341Z", "time": 1216.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-D-DXDI2l.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1216.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374351Z", "time": 1231.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/box-DvlTT8Qh.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 824, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 824}, "cache": {}, "timings": {"send": 0, "wait": 1231.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374364Z", "time": 1261.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryOptions-Yjo86aMs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 721, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 721}, "cache": {}, "timings": {"send": 0, "wait": 1261.6000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374374Z", "time": 1276.7999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/toDate-qOSwr3PX.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 615, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 615}, "cache": {}, "timings": {"send": 0, "wait": 1276.7999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374387Z", "time": 1307.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/addLeadingZeros-6--iqVZy.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 456, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 456}, "cache": {}, "timings": {"send": 0, "wait": 1307.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374397Z", "time": 4490.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/proto3-Bmo7MjaP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 10903, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 10903}, "cache": {}, "timings": {"send": 0, "wait": 4490.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374406Z", "time": 1415.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/heading-Duq80h8F.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 991, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 991}, "cache": {}, "timings": {"send": 0, "wait": 1415.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374418Z", "time": 1446.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account--DlvNh9L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1172, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1172}, "cache": {}, "timings": {"send": 0, "wait": 1446.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374431Z", "time": 1492, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-BneX_s9R.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 745, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 745}, "cache": {}, "timings": {"send": 0, "wait": 1492, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374444Z", "time": 2407.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/ProgressPage-CfNQ5HtT.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1971, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1971}, "cache": {}, "timings": {"send": 0, "wait": 2407.6000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374454Z", "time": 3758.7000000029802, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout-s5dvxSMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 7715, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 7715}, "cache": {}, "timings": {"send": 0, "wait": 3758.7000000029802, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374465Z", "time": 1539, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/number-BS8GKe3y.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1719, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1719}, "cache": {}, "timings": {"send": 0, "wait": 1539, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374476Z", "time": 1554.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/icons--M48DCb3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1161, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1161}, "cache": {}, "timings": {"send": 0, "wait": 1554.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374486Z", "time": 1585.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plural-D9YAiM4O.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1040, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1040}, "cache": {}, "timings": {"send": 0, "wait": 1585.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374498Z", "time": 1631.199999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Enabled-BoZ8Au2f.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 860, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 860}, "cache": {}, "timings": {"send": 0, "wait": 1631.199999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374509Z", "time": 2996.3999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/PlanPicker-CZi5-vIO.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4367, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4367}, "cache": {}, "timings": {"send": 0, "wait": 2996.3999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374520Z", "time": 3773.8999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Card-BR5rB2rc.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6435, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6435}, "cache": {}, "timings": {"send": 0, "wait": 3773.8999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374531Z", "time": 1646.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-C5gnWpVx.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 387, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 387}, "cache": {}, "timings": {"send": 0, "wait": 1646.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374543Z", "time": 2469.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BEyGE8AK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1912, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1912}, "cache": {}, "timings": {"send": 0, "wait": 2469.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374554Z", "time": 1676.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/url-_DgIuZOw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 635, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 635}, "cache": {}, "timings": {"send": 0, "wait": 1676.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374565Z", "time": 1722.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/isBefore-DuJnhAXP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 446, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 446}, "cache": {}, "timings": {"send": 0, "wait": 1722.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374577Z", "time": 1737.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/badge-CrInsKkE.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 986, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 986}, "cache": {}, "timings": {"send": 0, "wait": 1737.6000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374587Z", "time": 1784.7000000029802, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constructFrom-DWjd9ymD.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 441, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 441}, "cache": {}, "timings": {"send": 0, "wait": 1784.7000000029802, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374599Z", "time": 3804.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Badge-CCBfROU-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6905, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6905}, "cache": {}, "timings": {"send": 0, "wait": 3804.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374611Z", "time": 3820.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DzvzAwJl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6526, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6526}, "cache": {}, "timings": {"send": 0, "wait": 3820.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374622Z", "time": 5059.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account.subscription-BBRL8heg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 17282, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 17282}, "cache": {}, "timings": {"send": 0, "wait": 5059.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374633Z", "time": 1872.************, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400..700,0..1,0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 705, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 705}, "cache": {}, "timings": {"send": 0, "wait": 1872.************, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:38:59.374657Z", "time": 229.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2118, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2118}, "cache": {}, "timings": {"send": 0, "wait": 229.6000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:38:59.374676Z", "time": 322.79999999701977, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/user", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 484, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 484}, "cache": {}, "timings": {"send": 0, "wait": 322.79999999701977, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374688Z", "time": 305.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/team/plan-change-pending", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 339, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 339}, "cache": {}, "timings": {"send": 0, "wait": 305.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374700Z", "time": 1003.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/subscription", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 800, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 800}, "cache": {}, "timings": {"send": 0, "wait": 1003.0999999940395, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374711Z", "time": 692.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/plans", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 943, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 943}, "cache": {}, "timings": {"send": 0, "wait": 692.6000000089407, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374723Z", "time": 272, "request": {"method": "GET", "url": "https://app.augmentcode.com/augment-logo.svg", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3975, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 3975}, "cache": {}, "timings": {"send": 0, "wait": 272, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:38:59.374767Z", "time": 202.**************, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=auth.augmentcode.com&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&scrsrc=www.googletagmanager.com&frm=0&rnd=*********.**********&auid=**********.**********&navt=n&npa=1&_tu=AAg&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l3l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=*************&tfd=10054&apve=1&apvf=f", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 202.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374781Z", "time": 402.**************, "request": {"method": "GET", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=*************&gcd=13l3l3l3l3l1&npa=1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=**********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&ngs=1&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&dr=https%3A%2F%2Fauth.augmentcode.com%2F&dt=&_tu=AAg&en=page_view&_ee=1&tfd=10064", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 402.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374794Z", "time": 529.*************, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/i", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 529.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374806Z", "time": 580.5, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/p", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 580.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374817Z", "time": 984.2999999970198, "request": {"method": "GET", "url": "https://analytics.twitter.com/i/adsct?txn_id=pva71&p_id=Twitter&tw_sale_amount=0&tw_order_quantity=0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 984.2999999970198, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:38:59.374829Z", "time": 1299.699999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/credits", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 381, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 381}, "cache": {}, "timings": {"send": 0, "wait": 1299.699999988079, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374842Z", "time": 473.80000001192093, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/payment", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 346, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 346}, "cache": {}, "timings": {"send": 0, "wait": 473.80000001192093, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374855Z", "time": 301.79999999701977, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/deletions", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 336, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 336}, "cache": {}, "timings": {"send": 0, "wait": 301.79999999701977, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374868Z", "time": 375.**************, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 375.**************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:59.374880Z", "time": 236.5, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755142715475&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 236.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374892Z", "time": 990.7999999970198, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/recorder.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 990.7999999970198, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:59.374906Z", "time": 490.6000000089407, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 490.6000000089407, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:59.374924Z", "time": 318.**************, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/web-vitals.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 318.**************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:38:59.374938Z", "time": 279.**************, "request": {"method": "GET", "url": "https://us.i.posthog.com/e/?ip=0&_=1755142718481&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 279.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374951Z", "time": 203.**************, "request": {"method": "GET", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=*************&gcd=13l3l3l3l3l1&npa=1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=**********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AEAAAAQ&ngs=1&_s=2&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&dr=https%3A%2F%2Fauth.augmentcode.com%2F&dt=&_tu=AAg&en=scroll&epn.percent_scrolled=90&_et=7&tfd=15078", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 203.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374964Z", "time": 364.**************, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=*************&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 364.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374977Z", "time": 372.70000000298023, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=1755142728719&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 372.70000000298023, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.374990Z", "time": 5385.70000000298, "request": {"method": "GET", "url": "https://us.i.posthog.com/s/?ip=0&_=1755142728720&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 5385.70000000298, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.375003Z", "time": 1411.2999999970198, "request": {"method": "GET", "url": "https://us.i.posthog.com/s/?ip=0&_=1755142732326&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1411.2999999970198, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.375017Z", "time": 368.3999999910593, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=1755142732327&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 368.3999999910593, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:38:59.375032Z", "time": 310.**************, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=1755142736894&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 310.**************, "receive": 0}, "resourceType": "fetch"}], "websockets": [], "pages": [{"startedDateTime": "2025-08-14T11:39:04.427198Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}