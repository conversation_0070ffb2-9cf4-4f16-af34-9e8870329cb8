{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-14T14:33:25.612248Z", "time": 0, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "132235"}], "cookies": [], "content": {"size": 132235, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 132235}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "工作台"}, {"startedDateTime": "2025-08-14T14:33:25.615921Z", "time": 5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DWNmc_MY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21479, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21479}, "cache": {}, "timings": {"send": 0, "wait": 5, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:25.615932Z", "time": 6.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-H8ttwM1S.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 474, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 474}, "cache": {}, "timings": {"send": 0, "wait": 6.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615937Z", "time": 6.5999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-B9PrHQl7.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4414, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4414}, "cache": {}, "timings": {"send": 0, "wait": 6.5999999940395355, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615941Z", "time": 7.0999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CIEDqDGY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 468, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 468}, "cache": {}, "timings": {"send": 0, "wait": 7.0999999940395355, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615945Z", "time": 5.0999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-BKOrM9G3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 146848, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 146848}, "cache": {}, "timings": {"send": 0, "wait": 5.0999999940395355, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615949Z", "time": 10.399999991059303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BN5KR6Qs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5129, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5129}, "cache": {}, "timings": {"send": 0, "wait": 10.399999991059303, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615953Z", "time": 10.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtKobjF2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14336, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14336}, "cache": {}, "timings": {"send": 0, "wait": 10.599999994039536, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615957Z", "time": 9.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DjWl3JGw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 786361, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 786361}, "cache": {}, "timings": {"send": 0, "wait": 9.799999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615962Z", "time": 11, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-R6XwfvJN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3351, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3351}, "cache": {}, "timings": {"send": 0, "wait": 11, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615966Z", "time": 11.400000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-Bd2HX4Pl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5330, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5330}, "cache": {}, "timings": {"send": 0, "wait": 11.400000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615970Z", "time": 11.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DeQidb8Q.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6357, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6357}, "cache": {}, "timings": {"send": 0, "wait": 11.600000008940697, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615975Z", "time": 11.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BRMOCcEH.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 130305, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 130305}, "cache": {}, "timings": {"send": 0, "wait": 11.299999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.615979Z", "time": 7.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DwV2T02E.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 50384, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 50384}, "cache": {}, "timings": {"send": 0, "wait": 7.5, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:33:25.615984Z", "time": 7.199999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-C_thiKp4.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3318, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 3318}, "cache": {}, "timings": {"send": 0, "wait": 7.199999988079071, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:33:25.615989Z", "time": 8.399999991059303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-ntgrtH_u.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1564, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 1564}, "cache": {}, "timings": {"send": 0, "wait": 8.399999991059303, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:33:25.615994Z", "time": 8.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtkFttLu.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 42774, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 42774}, "cache": {}, "timings": {"send": 0, "wait": 8.599999994039536, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:33:25.615999Z", "time": 9.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DDWJuWk8.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 9043, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 9043}, "cache": {}, "timings": {"send": 0, "wait": 9.299999997019768, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:33:25.616004Z", "time": 12.099999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_user_key-BQXKMoNP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2168, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2168}, "cache": {}, "timings": {"send": 0, "wait": 12.099999994039536, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.616009Z", "time": 12, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-kZT51Bed.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2762, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2762}, "cache": {}, "timings": {"send": 0, "wait": 12, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.616015Z", "time": 1.4000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/app/config", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 408, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 408}, "cache": {}, "timings": {"send": 0, "wait": 1.4000000059604645, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:33:25.616020Z", "time": 1.*************322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/zh-CN-DTI-qNBp.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94467, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 94467}, "cache": {}, "timings": {"send": 0, "wait": 1.*************322, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:25.616026Z", "time": 3.4000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/list?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6465, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 6465}, "cache": {}, "timings": {"send": 0, "wait": 3.4000000059604645, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:33:25.616031Z", "time": 3, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/proxy/detection?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 654, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 654}, "cache": {}, "timings": {"send": 0, "wait": 3, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:33:25.616037Z", "time": 3.2999999970197678, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/core-info?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 406, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 406}, "cache": {}, "timings": {"send": 0, "wait": 3.2999999970197678, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:33:25.616043Z", "time": 2.6000000089406967, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob2-CEoTd5f3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1157, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1157}, "cache": {}, "timings": {"send": 0, "wait": 2.6000000089406967, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:25.616049Z", "time": 2.5, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_spin-Dat2nEi2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 822, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 822}, "cache": {}, "timings": {"send": 0, "wait": 2.5, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:25.616056Z", "time": 3.1000000089406967, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob1-CCAm7wau.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1143, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1143}, "cache": {}, "timings": {"send": 0, "wait": 3.1000000089406967, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:25.616062Z", "time": 24.100000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/JetBrains%20Mono/JetBrainsMono-Bold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 278128, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 278128}, "cache": {}, "timings": {"send": 0, "wait": 24.100000008940697, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:33:25.616069Z", "time": 25, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Regular.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 407356, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 407356}, "cache": {}, "timings": {"send": 0, "wait": 25, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:33:25.616076Z", "time": 24.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Medium.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 411800, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 411800}, "cache": {}, "timings": {"send": 0, "wait": 24.599999994039536, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:33:25.616083Z", "time": 24.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-SemiBold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 414276, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 414276}, "cache": {}, "timings": {"send": 0, "wait": 24.799999997019768, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:33:25.616090Z", "time": 4.**************2, "request": {"method": "GET", "url": "http://127.0.0.1:45535/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 285778, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 285778}, "cache": {}, "timings": {"send": 0, "wait": 4.**************2, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:33:25.616097Z", "time": 2.0999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_speed_test-2BHwaLbY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2258, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2258}, "cache": {}, "timings": {"send": 0, "wait": 2.0999999940395355, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:25.616104Z", "time": 2.**************22, "request": {"method": "GET", "url": "http://127.0.0.1:45535/US-BXoNwYaC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5807, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5807}, "cache": {}, "timings": {"send": 0, "wait": 2.**************22, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.198453Z", "time": 0, "request": {"method": "GET", "url": "https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SAxVTNodTNKNW9ZRjlsTEFZSVAxMWs5VjJfNHhCSnJweKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFQyRXNNNmZ1eF9MclNOem55VGR1alBvZ0VBbzd4S2U0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "48695"}], "cookies": [], "content": {"size": 48695, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 48695}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Code"}, {"startedDateTime": "2025-08-14T14:33:46.202250Z", "time": 0, "request": {"method": "GET", "url": "https://cdn.auth0.com/ulp/react-components/1.146.2/css/main.cdn.min.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:33:46.202261Z", "time": 0, "request": {"method": "GET", "url": "https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202267Z", "time": 191.90000000596046, "request": {"method": "GET", "url": "https://www.augmentcode.com/android-chrome-512x512.png", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "image/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 191.90000000596046, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:33:46.202272Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202277Z", "time": 0.9000000059604645, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/v1/projects/ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg/settings", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0.9000000059604645, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:33:46.202282Z", "time": 198, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=login.augmentcode.com&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge&scrsrc=www.googletagmanager.com&frm=0&rnd=1444922538.1755153223&dt=Augment%20Code&auid=872353.**********&navt=n&npa=0&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=1755153222785&tfd=1061&apve=1&apvf=sb", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 198, "receive": 0}, "resourceType": "beacon"}, {"startedDateTime": "2025-08-14T14:33:46.202286Z", "time": 214.**************, "request": {"method": "GET", "url": "https://googleads.g.doubleclick.net/pagead/viewthroughconversion/***********/?random=1755153222784&cv=11&fst=1755153222784&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SAxVTNodTNKNW9ZRjlsTEFZSVAxMWs5VjJfNHhCSnJweKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFQyRXNNNmZ1eF9MclNOem55VGR1alBvZ0VBbzd4S2U0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SAxVTNodTNKNW9ZRjlsTEFZSVAxMWs5VjJfNHhCSnJweKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFQyRXNNNmZ1eF9MclNOem55VGR1alBvZ0VBbzd4S2U0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=872353.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=4", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2510, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2510}, "cache": {}, "timings": {"send": 0, "wait": 214.**************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202291Z", "time": 374.09999999403954, "request": {"method": "GET", "url": "https://analytics.google.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=1755153222757&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=**********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SAxVTNodTNKNW9ZRjlsTEFZSVAxMWs5VjJfNHhCSnJweKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFQyRXNNNmZ1eF9MclNOem55VGR1alBvZ0VBbzd4S2U0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dr=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SAxVTNodTNKNW9ZRjlsTEFZSVAxMWs5VjJfNHhCSnJweKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFQyRXNNNmZ1eF9MclNOem55VGR1alBvZ0VBbzd4S2U0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dt=Augment%20Code&en=page_view&_ee=1&tfd=1074", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 374.09999999403954, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:33:46.202297Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/ajs-destination.bundle.8e6b895db75187c55313.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202302Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/schemaFilter.bundle.1b218d13fed021531d4e.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202307Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202312Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202317Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/845/431110629a9fe8297174.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202323Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202328Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202333Z", "time": 0, "request": {"method": "GET", "url": "https://us.i.posthog.com/static/array.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202339Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_GIfRcxAmWBSIxniAKUhlM9pQ343wGGnAgC3iBMO1FSN/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:33:46.202345Z", "time": 238.3999999910593, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755153222836&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 238.3999999910593, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:33:46.202350Z", "time": 172.**************, "request": {"method": "GET", "url": "https://www.google.com/pagead/1p-user-list/***********/?random=1755153222784&cv=11&fst=1755151200000&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SAxVTNodTNKNW9ZRjlsTEFZSVAxMWs5VjJfNHhCSnJweKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFQyRXNNNmZ1eF9MclNOem55VGR1alBvZ0VBbzd4S2U0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SAxVTNodTNKNW9ZRjlsTEFZSVAxMWs5VjJfNHhCSnJweKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFQyRXNNNmZ1eF9MclNOem55VGR1alBvZ0VBbzd4S2U0o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=872353.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&data=event%3Dgtag.config&rfmt=3&fmt=3&is_vtc=1&cid=CAQSigEA2abss4ELYxayEk6siIRXxemarjv-nBTIjbP13y8AkHZS_OKYeG3-L74yc1d7-iFn608iyMBOuKcHPIQTU1Ur0DZ_dZHSZq_wNAdgd6niI792zy-RvWBufJJiG9GpVz6K49WU7rnmc0bOdV4XCDOv9KD6Gy8mhC_C7uUjD8-WHAxqS0h9MbVbnfE&random=756218480&rmt_tld=0&ipr=y", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 342, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 342}, "cache": {}, "timings": {"send": 0, "wait": 172.**************, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:33:46.202356Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:34:38.158036Z", "time": 0, "request": {"method": "GET", "url": "https://app.augmentcode.com/account/subscription", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "38988"}], "cookies": [], "content": {"size": 38988, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 38988}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": ""}, {"startedDateTime": "2025-08-14T14:34:38.164215Z", "time": 7228.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-C-JJqdMW.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94361, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 94361}, "cache": {}, "timings": {"send": 0, "wait": 7228.************, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:34:38.164234Z", "time": 706.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-D6aQ-Xs1.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 416, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 416}, "cache": {}, "timings": {"send": 0, "wait": 706.*************, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:34:38.164245Z", "time": 890.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/tailwind-DxnphuB3.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5101, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 5101}, "cache": {}, "timings": {"send": 0, "wait": 890.*************, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T14:34:38.164255Z", "time": 2449.*************, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400..700,0..1,0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 705, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 705}, "cache": {}, "timings": {"send": 0, "wait": 2449.*************, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T14:34:38.164265Z", "time": 414.*************3, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/manifest-46feb8ab.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4124, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4124}, "cache": {}, "timings": {"send": 0, "wait": 414.*************3, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164275Z", "time": 382.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/entry.client-C92V1BwZ.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1792, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1792}, "cache": {}, "timings": {"send": 0, "wait": 382.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164285Z", "time": 5885, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-Bi4s4-Io.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 46139, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 46139}, "cache": {}, "timings": {"send": 0, "wait": 5885, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164295Z", "time": 5532, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BS38kjqr.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21235, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21235}, "cache": {}, "timings": {"send": 0, "wait": 5532, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164306Z", "time": 3421.199999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B7Ui2t93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5343, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5343}, "cache": {}, "timings": {"send": 0, "wait": 3421.199999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164316Z", "time": 4877.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/components--HLsvfrm.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13680, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13680}, "cache": {}, "timings": {"send": 0, "wait": 4877.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164326Z", "time": 836.0999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/QueryClientProvider-CeGnmbe-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 696, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 696}, "cache": {}, "timings": {"send": 0, "wait": 836.0999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164332Z", "time": 2988.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryClient.client-Dk3lS3wN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3850, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3850}, "cache": {}, "timings": {"send": 0, "wait": 2988.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164338Z", "time": 2354.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/client-only-C74SDDMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2093, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2093}, "cache": {}, "timings": {"send": 0, "wait": 2354.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164344Z", "time": 4985.79999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index.modern-950P1XoK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14011, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14011}, "cache": {}, "timings": {"send": 0, "wait": 4985.79999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164351Z", "time": 850.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/container-BlJCmUTg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 884, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 884}, "cache": {}, "timings": {"send": 0, "wait": 850.6000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164357Z", "time": 896.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/card-BBgKeY7L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 865, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 865}, "cache": {}, "timings": {"send": 0, "wait": 896.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164364Z", "time": 911.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/link-CMt6MnuB.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 998, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 998}, "cache": {}, "timings": {"send": 0, "wait": 911.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164370Z", "time": 3006.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/flex-C9XhsxSj.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3855, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3855}, "cache": {}, "timings": {"send": 0, "wait": 3006.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164377Z", "time": 5015.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/theme-C1ulz75E.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13876, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13876}, "cache": {}, "timings": {"send": 0, "wait": 5015.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164383Z", "time": 957.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/button-Dvrjyl3p.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 608, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 608}, "cache": {}, "timings": {"send": 0, "wait": 957.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164390Z", "time": 3482.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Toast-CG_NC-6_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5695, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5695}, "cache": {}, "timings": {"send": 0, "wait": 3482.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164397Z", "time": 1035.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DrFu-skq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1110, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1110}, "cache": {}, "timings": {"send": 0, "wait": 1035.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164403Z", "time": 1050.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/jotaiStore.client-sdvKmlSn.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 430, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 430}, "cache": {}, "timings": {"send": 0, "wait": 1050.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164410Z", "time": 1083.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-BkoMXhYo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 462, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 462}, "cache": {}, "timings": {"send": 0, "wait": 1083.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164417Z", "time": 1098.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-C8U1uDHl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1132, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1132}, "cache": {}, "timings": {"send": 0, "wait": 1098.6000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164425Z", "time": 1176.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/spinner-Cq6egsy4.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1179, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1179}, "cache": {}, "timings": {"send": 0, "wait": 1176.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164432Z", "time": 2451, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CI4icoal.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1970, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1970}, "cache": {}, "timings": {"send": 0, "wait": 2451, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164440Z", "time": 2481.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAuWbg93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1857, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1857}, "cache": {}, "timings": {"send": 0, "wait": 2481.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164447Z", "time": 1191.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAyM6kBC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 766, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 766}, "cache": {}, "timings": {"send": 0, "wait": 1191.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164455Z", "time": 1210.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/get-subtree-8AxxbxX_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 598, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 598}, "cache": {}, "timings": {"send": 0, "wait": 1210.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164463Z", "time": 1238.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/base-button-Dk95TXPu.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1152, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1152}, "cache": {}, "timings": {"send": 0, "wait": 1238.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164470Z", "time": 1254.3999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B5mzPb5P.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 935, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 935}, "cache": {}, "timings": {"send": 0, "wait": 1254.3999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164478Z", "time": 1332.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/useQuery-BvSAfNQo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1398, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1398}, "cache": {}, "timings": {"send": 0, "wait": 1332.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164486Z", "time": 3528.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-Cup3efAs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6206, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6206}, "cache": {}, "timings": {"send": 0, "wait": 3528.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164493Z", "time": 4242.100000008941, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-icons.esm-g3l3pVh3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8914, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8914}, "cache": {}, "timings": {"send": 0, "wait": 4242.100000008941, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164501Z", "time": 1394.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/BaseHeader-y1wz0aO3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1603, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1603}, "cache": {}, "timings": {"send": 0, "wait": 1394.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164509Z", "time": 3115.3999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/animations-CMbVnQEg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3734, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3734}, "cache": {}, "timings": {"send": 0, "wait": 3115.3999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164517Z", "time": 4257.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/style-Bv9a6v44.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8859, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8859}, "cache": {}, "timings": {"send": 0, "wait": 4257.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164525Z", "time": 1471.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/guards-C20ItfmI.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 963, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 963}, "cache": {}, "timings": {"send": 0, "wait": 1471.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164532Z", "time": 2587.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/string-CwzBSc0v.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3135, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3135}, "cache": {}, "timings": {"send": 0, "wait": 2587.6000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164540Z", "time": 5173.***********, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/user-d_3utlAo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 16299, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 16299}, "cache": {}, "timings": {"send": 0, "wait": 5173.***********, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164549Z", "time": 1486.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plans-D8s3V0en.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 510, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 510}, "cache": {}, "timings": {"send": 0, "wait": 1486.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164557Z", "time": 1518, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/subscription-creation-pending-Mylp5-_d.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1518, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164566Z", "time": 1534.3999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/skeleton-qwMe81ym.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 880, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 880}, "cache": {}, "timings": {"send": 0, "wait": 1534.3999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164574Z", "time": 1565.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/feature-flags.client-BVZhVN7G.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 431, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 431}, "cache": {}, "timings": {"send": 0, "wait": 1565.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164582Z", "time": 1580.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/box-DvlTT8Qh.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 824, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 824}, "cache": {}, "timings": {"send": 0, "wait": 1580.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164591Z", "time": 1596.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-D-DXDI2l.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1596.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164602Z", "time": 1627.199999988079, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryOptions-Yjo86aMs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 721, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 721}, "cache": {}, "timings": {"send": 0, "wait": 1627.199999988079, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164614Z", "time": 1658.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/toDate-qOSwr3PX.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 615, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 615}, "cache": {}, "timings": {"send": 0, "wait": 1658.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164625Z", "time": 1673.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/addLeadingZeros-6--iqVZy.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 456, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 456}, "cache": {}, "timings": {"send": 0, "wait": 1673.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164636Z", "time": 3990.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout-s5dvxSMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 7715, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 7715}, "cache": {}, "timings": {"send": 0, "wait": 3990.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164648Z", "time": 4721.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/proto3-Bmo7MjaP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 10903, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 10903}, "cache": {}, "timings": {"send": 0, "wait": 4721.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164661Z", "time": 1764.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/heading-Duq80h8F.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 991, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 991}, "cache": {}, "timings": {"send": 0, "wait": 1764.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164673Z", "time": 2679.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/ProgressPage-CfNQ5HtT.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1971, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1971}, "cache": {}, "timings": {"send": 0, "wait": 2679.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164685Z", "time": 1795.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account--DlvNh9L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1172, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1172}, "cache": {}, "timings": {"send": 0, "wait": 1795.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164698Z", "time": 1810.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-BneX_s9R.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 745, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 745}, "cache": {}, "timings": {"send": 0, "wait": 1810.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164710Z", "time": 3236, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/PlanPicker-CZi5-vIO.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4367, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4367}, "cache": {}, "timings": {"send": 0, "wait": 3236, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164722Z", "time": 1871.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/number-BS8GKe3y.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1719, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1719}, "cache": {}, "timings": {"send": 0, "wait": 1871.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164734Z", "time": 4005.9000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Card-BR5rB2rc.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6435, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6435}, "cache": {}, "timings": {"send": 0, "wait": 4005.9000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164748Z", "time": 1903.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/icons--M48DCb3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1161, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1161}, "cache": {}, "timings": {"send": 0, "wait": 1903.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164763Z", "time": 1919.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plural-D9YAiM4O.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1040, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1040}, "cache": {}, "timings": {"send": 0, "wait": 1919.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164776Z", "time": 1950.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Enabled-BoZ8Au2f.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 860, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 860}, "cache": {}, "timings": {"send": 0, "wait": 1950.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164786Z", "time": 1996.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-C5gnWpVx.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 387, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 387}, "cache": {}, "timings": {"send": 0, "wait": 1996.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164795Z", "time": 2012.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/url-_DgIuZOw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 635, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 635}, "cache": {}, "timings": {"send": 0, "wait": 2012.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164804Z", "time": 2756.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BEyGE8AK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1912, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1912}, "cache": {}, "timings": {"send": 0, "wait": 2756.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164814Z", "time": 4036.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Badge-CCBfROU-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6905, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6905}, "cache": {}, "timings": {"send": 0, "wait": 4036.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164824Z", "time": 2058.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/isBefore-DuJnhAXP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 446, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 446}, "cache": {}, "timings": {"send": 0, "wait": 2058.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164833Z", "time": 2089.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/badge-CrInsKkE.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 986, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 986}, "cache": {}, "timings": {"send": 0, "wait": 2089.6000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164843Z", "time": 2135.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constructFrom-DWjd9ymD.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 441, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 441}, "cache": {}, "timings": {"send": 0, "wait": 2135.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164854Z", "time": 4052.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DzvzAwJl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6526, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6526}, "cache": {}, "timings": {"send": 0, "wait": 4052.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164863Z", "time": 5281.***********, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account.subscription-BBRL8heg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 17282, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 17282}, "cache": {}, "timings": {"send": 0, "wait": 5281.***********, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164879Z", "time": 354.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/user", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 483, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 483}, "cache": {}, "timings": {"send": 0, "wait": 354.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.164892Z", "time": 301.90000000596046, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/team/plan-change-pending", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 339, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 339}, "cache": {}, "timings": {"send": 0, "wait": 301.90000000596046, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.164902Z", "time": 876.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/subscription", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 800, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 800}, "cache": {}, "timings": {"send": 0, "wait": 876.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.164911Z", "time": 660.6000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/plans", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 943, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 943}, "cache": {}, "timings": {"send": 0, "wait": 660.6000000089407, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.164922Z", "time": 319.*************3, "request": {"method": "GET", "url": "https://app.augmentcode.com/augment-logo.svg", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3975, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 3975}, "cache": {}, "timings": {"send": 0, "wait": 319.*************3, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:34:38.164933Z", "time": 257.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2118, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2118}, "cache": {}, "timings": {"send": 0, "wait": 257.**************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T14:34:38.164943Z", "time": 1313.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/credits", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 381, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 381}, "cache": {}, "timings": {"send": 0, "wait": 1313.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.164953Z", "time": 426.3999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/payment", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 346, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 346}, "cache": {}, "timings": {"send": 0, "wait": 426.3999999910593, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.164964Z", "time": 287.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/deletions", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 336, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 336}, "cache": {}, "timings": {"send": 0, "wait": 287.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.165015Z", "time": 427.*************, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=auth.augmentcode.com&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&scrsrc=www.googletagmanager.com&frm=0&rnd=**********.**********&auid=872353.**********&navt=n&npa=1&_tu=AAg&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l3l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=*************&tfd=13215&apve=1&apvf=f", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 427.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.165035Z", "time": 509.*************, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/i", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 509.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.165050Z", "time": 894.0999999940395, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/p", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 894.0999999940395, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.165067Z", "time": 939.*************, "request": {"method": "GET", "url": "https://analytics.twitter.com/i/adsct?txn_id=pva71&p_id=Twitter&tw_sale_amount=0&tw_order_quantity=0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 939.*************, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T14:34:38.165083Z", "time": 417.*************, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 417.*************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:34:38.165100Z", "time": 293.19999998807907, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755153259372&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 293.19999998807907, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:38.165114Z", "time": 1002.1999999880791, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/recorder.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1002.1999999880791, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:34:42.211013Z", "time": 692, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 692, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:34:42.211047Z", "time": 568.1999999880791, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/web-vitals.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 568.1999999880791, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T14:34:42.211061Z", "time": 296.*************, "request": {"method": "GET", "url": "https://us.i.posthog.com/e/?ip=0&_=1755153262373&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 296.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:42.211074Z", "time": 434.*************, "request": {"method": "GET", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=*************&gcd=13l3l3l3l3l1&npa=1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=**********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&ngs=1&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&dr=https%3A%2F%2Fauth.augmentcode.com%2F&dt=&_tu=AAg&en=page_view&_ee=1&tfd=18225", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 434.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:42.211087Z", "time": 432.**************, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=*************&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 432.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T14:34:42.211103Z", "time": 371.**************, "request": {"method": "GET", "url": "https://us.i.posthog.com/i/v0/e/?ip=0&_=1755153272685&ver=1.260.0&compression=gzip-js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 371.**************, "receive": 0}, "resourceType": "fetch"}], "websockets": [], "pages": [{"startedDateTime": "2025-08-14T14:34:48.063859Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}