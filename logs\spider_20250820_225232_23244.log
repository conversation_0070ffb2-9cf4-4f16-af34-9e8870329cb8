2025-08-20 22:52:42 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62561,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 22:52:42 - INFO - 127.0.0.1:62561
2025-08-20 22:52:42 - INFO - <EMAIL>
2025-08-20 22:52:42 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 22:53:08 - INFO - 
主流程中发生严重错误: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608452191]
	(No symbol) [0x0x7ff6084ff83e]
	(No symbol) [0x0x7ff60851ff72]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 22:53:08 - INFO - 准备重启流程...
2025-08-20 22:53:11 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62614,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 22:53:11 - INFO - 127.0.0.1:62614
2025-08-20 22:53:14 - INFO - <EMAIL>
2025-08-20 22:53:14 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 22:53:31 - INFO - 获取 cap_value 验证码成功...
2025-08-20 22:53:35 - INFO - 找到 Turnstile...
2025-08-20 22:53:37 - INFO - 登录信息已提交，等待验证页面...
2025-08-20 22:53:40 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-20 22:53:49 - INFO - 验证码已提交，等待跳转...
2025-08-20 22:53:49 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-20 22:53:52 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 22:53:52 - INFO - 
第 1/5 次尝试注册...
2025-08-20 22:53:52 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-20 22:53:55 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-20 22:53:55 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-20 22:53:58 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62740,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 22:53:58 - INFO - 127.0.0.1:62740
2025-08-20 22:53:59 - INFO - <EMAIL>
2025-08-20 22:53:59 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 22:54:21 - INFO - 获取 cap_value 验证码成功...
2025-08-20 22:54:24 - INFO - 找到 Turnstile...
2025-08-20 22:54:24 - INFO - 登录信息已提交，等待验证页面...
2025-08-20 22:54:27 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-20 22:54:28 - INFO - 验证码已提交，等待跳转...
2025-08-20 22:54:35 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-20 22:54:45 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 22:54:45 - INFO - 
第 1/5 次尝试注册...
2025-08-20 22:54:45 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-20 22:54:48 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-20 22:54:48 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-20 22:54:57 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62852,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 22:54:57 - INFO - 127.0.0.1:62852
2025-08-20 22:54:58 - INFO - Traceback (most recent call last):

2025-08-20 22:54:58 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m849[0m, in [35mcreate_connection[0m
    [31msock.connect[0m[1;31m(sa)[0m
    [31m~~~~~~~~~~~~[0m[1;31m^^^^[0m

2025-08-20 22:54:58 - INFO - [1;35mTimeoutError[0m: [35mtimed out[0m

2025-08-20 22:54:58 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-20 22:54:58 - INFO - Traceback (most recent call last):

2025-08-20 22:54:58 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1014[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-20 22:54:58 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m961[0m, in [35mmain[0m
    driver = setup_driver()

2025-08-20 22:54:58 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m191[0m, in [35msetup_driver[0m
    driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

2025-08-20 22:54:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\chrome\webdriver.py"[0m, line [35m47[0m, in [35m__init__[0m
    [31msuper().__init__[0m[1;31m([0m
    [31m~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mbrowser_name=DesiredCapabilities.CHROME["browserName"],[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    ...<3 lines>...
        [1;31mkeep_alive=keep_alive,[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-20 22:54:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\chromium\webdriver.py"[0m, line [35m58[0m, in [35m__init__[0m
    [31mself.service.start[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 22:54:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\service.py"[0m, line [35m109[0m, in [35mstart[0m
    if [31mself.is_connectable[0m[1;31m()[0m:
       [31m~~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 22:54:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\service.py"[0m, line [35m126[0m, in [35mis_connectable[0m
    return [31mutils.is_connectable[0m[1;31m(self.port)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^[0m

2025-08-20 22:54:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\utils.py"[0m, line [35m99[0m, in [35mis_connectable[0m
    socket_ = socket.create_connection((host, port), 1)

2025-08-20 22:54:58 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m856[0m, in [35mcreate_connection[0m
    [31mexceptions.clear[0m[1;31m()[0m  # raise only the last error
    [31m~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 22:54:58 - INFO - [1;35mKeyboardInterrupt[0m

