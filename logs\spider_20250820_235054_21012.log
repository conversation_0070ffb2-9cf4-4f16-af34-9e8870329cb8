2025-08-20 23:50:58 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61077,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:50:58 - INFO - 127.0.0.1:61077
2025-08-20 23:50:59 - INFO - <EMAIL>
2025-08-20 23:50:59 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:51:14 - INFO - 获取 cap_value 验证码成功...
2025-08-20 23:51:15 - INFO - 找到 Turnstile...
2025-08-20 23:51:17 - INFO - 登录信息已提交，等待验证页面...
2025-08-20 23:51:21 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-20 23:51:28 - INFO - 验证码已提交，等待跳转...
2025-08-20 23:51:28 - INFO - 
第 1/5 次尝试注册...
2025-08-20 23:51:28 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-20 23:51:31 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-20 23:51:31 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-20 23:51:33 - INFO - Traceback (most recent call last):

2025-08-20 23:51:33 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m943[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-20 23:51:33 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m895[0m, in [35mmain[0m
    driver = setup_driver()

2025-08-20 23:51:33 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m172[0m, in [35msetup_driver[0m
    [31mclient.browser_local_cache[0m[1;31m([brwoser_id])[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-20 23:51:33 - INFO -   File [35m"e:\Roxyspider\RoxyClient.py"[0m, line [35m149[0m, in [35mbrowser_local_cache[0m
    return [31mself._post[0m[1;31m("/browser/clear_local_cache", {"dirIds": dirIds})[0m.json()
           [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:51:33 - INFO -   File [35m"e:\Roxyspider\RoxyClient.py"[0m, line [35m22[0m, in [35m_post[0m
    return [31mrequests.post[0m[1;31m(self.url + path, json=data, headers=self._build_headers())[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:51:33 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\api.py"[0m, line [35m115[0m, in [35mpost[0m
    return request("post", url, data=data, json=json, **kwargs)

2025-08-20 23:51:33 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\api.py"[0m, line [35m59[0m, in [35mrequest[0m
    return [31msession.request[0m[1;31m(method=method, url=url, **kwargs)[0m
           [31m~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:51:33 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m589[0m, in [35mrequest[0m
    resp = self.send(prep, **send_kwargs)

2025-08-20 23:51:33 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m703[0m, in [35msend[0m
    r = adapter.send(request, **kwargs)

2025-08-20 23:51:33 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\adapters.py"[0m, line [35m667[0m, in [35msend[0m
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )

2025-08-20 23:51:33 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-20 23:51:33 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-20 23:51:33 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-20 23:51:33 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:51:33 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:51:33 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-20 23:51:33 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-20 23:51:33 - INFO - [1;35mKeyboardInterrupt[0m

