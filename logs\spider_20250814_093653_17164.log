2025-08-14 09:36:53 - INFO - 测试 HAR 和 WebSocket 功能...
2025-08-14 09:36:53 - INFO - HAR 文件已保存: har_files\test_har_20250814_093653.har
2025-08-14 09:36:53 - INFO - 捕获了 1 个网络请求
2025-08-14 09:36:53 - INFO - 捕获了 1 个 WebSocket 连接
2025-08-14 09:36:53 - INFO -   WebSocket: wss://app.augmentcode.com/ws - 4 个消息帧
2025-08-14 09:36:53 - INFO - WebSocket 摘要已保存: websocket_logs\test_websocket_20250814_093653.json
2025-08-14 09:36:53 - INFO - HAR 和 WebSocket 功能测试成功！
2025-08-14 09:36:53 - INFO - HAR 文件: har_files\test_har_20250814_093653.har
2025-08-14 09:36:53 - INFO - WebSocket 摘要: websocket_logs\test_websocket_20250814_093653.json
2025-08-14 09:36:53 - INFO - WebSocket 测试统计: {'total_connections': 1, 'active_connections': 0, 'closed_connections': 1, 'total_frames': 4, 'sent_frames': 2, 'received_frames': 2}
2025-08-14 09:36:53 - INFO - HAR 和 WebSocket 功能测试完成
2025-08-14 09:36:53 - INFO - ==================================================
2025-08-14 09:36:57 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52946,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:36:57 - INFO - 性能日志配置已启用
2025-08-14 09:36:59 - INFO - 使用配置选项创建 driver 失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: cannot parse perfLoggingPrefs
from invalid argument: unrecognized performance logging option: enableTimeline
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff67848adac]
	(No symbol) [0x0x7ff67848a5c8]
	(No symbol) [0x0x7ff678488bc1]
	(No symbol) [0x0x7ff67848a5c8]
	(No symbol) [0x0x7ff678484a5a]
	(No symbol) [0x0x7ff6784e4062]
	(No symbol) [0x0x7ff6784e3977]
	(No symbol) [0x0x7ff6784e5880]
	(No symbol) [0x0x7ff6784e5630]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:36:59 - INFO - 尝试使用最简化的选项创建 driver...
2025-08-14 09:37:00 - INFO - WebDriver 使用简化选项创建成功
2025-08-14 09:37:00 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 09:37:00 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 09:37:00 - INFO - <EMAIL>
2025-08-14 09:37:00 - INFO - 性能日志不可用: Message: invalid argument: log type 'performance' not found
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff6784f0887]
	(No symbol) [0x0x7ff6784d8430]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:37:00 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 09:37:10 - INFO - 获取 cap_value 验证码成功...
2025-08-14 09:37:14 - INFO - 找到 Turnstile...
2025-08-14 09:37:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 09:37:18 - INFO - 性能日志不可用: Message: invalid argument: log type 'performance' not found
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff6784f0887]
	(No symbol) [0x0x7ff6784d8430]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:37:18 - INFO - 通过 CDP 记录页面: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBsZlpvWE1JZmlNMnNvbmVqWlpSN2NqOFoxNXd5MHlPQ6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHAxR3YwSUZic2VRM2gwQ2R2NENKSWVsS3F4Tmh3NW1mo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-08-14 09:37:19 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 09:37:19 - INFO - CDP:启用节流模式
2025-08-14 09:37:33 - INFO - 验证码已提交，等待跳转...
2025-08-14 09:37:33 - INFO - 性能日志不可用: Message: invalid argument: log type 'performance' not found
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff6784f0887]
	(No symbol) [0x0x7ff6784d8430]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:37:33 - INFO - 通过 CDP 记录页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=B157L4uNDDcAtNah81wzBN2NbFQ5b7Eicv9VS0GbU3Y&code_challenge=TFwbUZLwBWPlht97BivjtlAlK39CGjLLIbmS9LAf_8w&code_challenge_method=S256
2025-08-14 09:37:33 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 09:37:33 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 09:37:33 - INFO - ReCAPTCHA Token: 
2025-08-14 09:37:33 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 09:37:35 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:37:35 - INFO - 性能日志不可用: Message: invalid argument: log type 'performance' not found
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff6784f0887]
	(No symbol) [0x0x7ff6784d8430]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:37:35 - INFO - 
第 1/5 次尝试注册...
2025-08-14 09:37:35 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 09:37:36 - INFO - HAR 文件已保存: har_files\final_jgnmo1755135413_smartdocker.online_20250814_093736.har
2025-08-14 09:37:36 - INFO - 捕获了 3 个网络请求
2025-08-14 09:37:36 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 09:37:36 - INFO - WebSocket 摘要已保存: websocket_logs\final_jgnmo1755135413_smartdocker.online_20250814_093736_ws.json
2025-08-14 09:37:36 - INFO - Traceback (most recent call last):

2025-08-14 09:37:36 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m2137[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-14 09:37:36 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1922[0m, in [35mmain[0m
    result = attempt_signup_with_retry(driver)

2025-08-14 09:37:36 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1446[0m, in [35mattempt_signup_with_retry[0m
    [31mtime.sleep[0m[1;31m(3)[0m # 等待一下，让页面有时间响应
    [31m~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-14 09:37:36 - INFO - [1;35mKeyboardInterrupt[0m

