2025-08-12 10:51:33 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58385,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-12 10:51:34 - INFO - <EMAIL>
2025-08-12 10:51:34 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-12 10:51:43 - INFO - 请求 Turnstile 验证码 (第 1/4 次)...
2025-08-12 10:51:43 - INFO - 获取到 task_id: 8d9c542d-17f8-4b45-b3fd-f3badfd0c1de，开始轮询结果...
2025-08-12 10:51:43 - INFO - 验证码未就绪，等待 5.2 秒后重试...
2025-08-12 10:51:48 - INFO - 验证码未就绪，等待 6.3 秒后重试...
2025-08-12 10:51:55 - INFO - 获取验证码成功: 0.AnKux4BKfQgjN7d0zA...
2025-08-12 10:51:58 - INFO - 找到 Turnstile...
2025-08-12 10:52:00 - INFO - 登录信息已提交，等待验证页面...
2025-08-12 10:52:00 - INFO - 已执行拟人滚动: -95px
2025-08-12 10:52:01 - INFO - 已执行拟人操作完成，总停顿: 1.06秒
2025-08-12 10:52:03 - INFO - 登录后已执行更多拟人操作
2025-08-12 10:52:06 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-12 10:52:16 - INFO - 验证码已提交，等待跳转...
2025-08-12 10:52:16 - INFO - 已执行拟人滚动: 112px
2025-08-12 10:52:22 - INFO - 已执行页面焦点操作
2025-08-12 10:52:22 - INFO - 已执行拟人操作完成，总停顿: 1.04秒
2025-08-12 10:52:22 - INFO - 
第 1/5 次尝试注册...
2025-08-12 10:52:34 - INFO - 🚨 在页面元素中检测到 'Sign-up rejected' - 当前邮箱被拒绝！
2025-08-12 10:52:34 - INFO - 将关闭当前浏览器，使用新邮箱重新开始注册流程。
2025-08-12 10:52:34 - INFO - 检测到 Sign-up rejected，当前邮箱被拒绝，将重新开始使用新邮箱
2025-08-12 10:52:34 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-12 10:52:58 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58522,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-12 10:52:59 - INFO - <EMAIL>
2025-08-12 10:52:59 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-12 10:53:03 - INFO - 请求 Turnstile 验证码 (第 1/4 次)...
2025-08-12 10:53:03 - INFO - 获取到 task_id: 1b90de45-ec57-4c82-b93c-b14f50f26916，开始轮询结果...
2025-08-12 10:53:04 - INFO - 验证码未就绪，等待 5.4 秒后重试...
2025-08-12 10:53:09 - INFO - 获取验证码成功: 0.v42tRb16e4w1UVr_9s...
2025-08-12 10:53:12 - INFO - 找到 Turnstile...
2025-08-12 10:53:12 - INFO - 登录信息已提交，等待验证页面...
2025-08-12 10:53:12 - INFO - 已执行拟人滚动: 81px
2025-08-12 10:53:14 - INFO - 已执行页面焦点操作
2025-08-12 10:53:14 - INFO - 已执行拟人操作完成，总停顿: 0.63秒
2025-08-12 10:53:16 - INFO - 登录后已执行更多拟人操作
2025-08-12 10:53:19 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-12 10:53:29 - INFO - 验证码已提交，等待跳转...
2025-08-12 10:53:29 - INFO - 已执行拟人滚动: -141px
2025-08-12 10:53:33 - INFO - 已执行页面焦点操作
2025-08-12 10:53:34 - INFO - 已执行拟人操作完成，总停顿: 1.13秒
2025-08-12 10:53:34 - INFO - 
第 1/5 次尝试注册...
2025-08-12 10:53:44 - INFO - 🚨 在页面元素中检测到 'Sign-up rejected' - 当前邮箱被拒绝！
2025-08-12 10:53:44 - INFO - 将关闭当前浏览器，使用新邮箱重新开始注册流程。
2025-08-12 10:53:44 - INFO - 检测到 Sign-up rejected，当前邮箱被拒绝，将重新开始使用新邮箱
2025-08-12 10:53:44 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-12 10:54:09 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58670,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-12 10:54:09 - INFO - <EMAIL>
2025-08-12 10:54:09 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-12 10:54:14 - INFO - 请求 Turnstile 验证码 (第 1/4 次)...
2025-08-12 10:54:14 - INFO - 获取到 task_id: 157d80e1-e85c-4ff6-9176-3df275956784，开始轮询结果...
2025-08-12 10:54:14 - INFO - 验证码未就绪，等待 3.9 秒后重试...
2025-08-12 10:54:18 - INFO - 验证码未就绪，等待 5.2 秒后重试...
2025-08-12 10:54:23 - INFO - 获取验证码成功: 0.D0iB_TaP7VpF9RWV1N...
2025-08-12 10:54:26 - INFO - 找到 Turnstile...
2025-08-12 10:54:28 - INFO - 登录信息已提交，等待验证页面...
2025-08-12 10:54:28 - INFO - 已执行拟人滚动: -41px
2025-08-12 10:54:28 - INFO - 已执行页面焦点操作
2025-08-12 10:54:29 - INFO - 已执行拟人操作完成，总停顿: 1.16秒
2025-08-12 10:54:30 - INFO - 登录后已执行更多拟人操作
2025-08-12 10:54:33 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-12 10:54:43 - INFO - 验证码已提交，等待跳转...
2025-08-12 10:54:43 - INFO - 已执行拟人滚动: -145px
2025-08-12 10:54:48 - INFO - 已执行页面焦点操作
2025-08-12 10:54:48 - INFO - 已执行拟人操作完成，总停顿: 1.21秒
2025-08-12 10:54:48 - INFO - 
第 1/5 次尝试注册...
2025-08-12 10:54:59 - INFO - 🚨 在页面元素中检测到 'Sign-up rejected' - 当前邮箱被拒绝！
2025-08-12 10:54:59 - INFO - 将关闭当前浏览器，使用新邮箱重新开始注册流程。
2025-08-12 10:54:59 - INFO - 检测到 Sign-up rejected，当前邮箱被拒绝，将重新开始使用新邮箱
2025-08-12 10:54:59 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-12 10:55:23 - INFO - Traceback (most recent call last):

2025-08-12 10:55:23 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1484[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-12 10:55:23 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1433[0m, in [35mmain[0m
    driver = setup_driver()

2025-08-12 10:55:23 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m773[0m, in [35msetup_driver[0m
    rsp = client.browser_open(brwoser_id)

2025-08-12 10:55:23 - INFO -   File [35m"e:\Roxyspider\RoxyClient.py"[0m, line [35m121[0m, in [35mbrowser_open[0m
    return [31mself._post[0m[1;31m("/browser/open", {"dirId": dirId, "args": args})[0m.json()
           [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-12 10:55:23 - INFO -   File [35m"e:\Roxyspider\RoxyClient.py"[0m, line [35m22[0m, in [35m_post[0m
    return [31mrequests.post[0m[1;31m(self.url + path, json=data, headers=self._build_headers())[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-12 10:55:23 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\api.py"[0m, line [35m115[0m, in [35mpost[0m
    return request("post", url, data=data, json=json, **kwargs)

2025-08-12 10:55:23 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\api.py"[0m, line [35m59[0m, in [35mrequest[0m
    return [31msession.request[0m[1;31m(method=method, url=url, **kwargs)[0m
           [31m~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-12 10:55:23 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m589[0m, in [35mrequest[0m
    resp = self.send(prep, **send_kwargs)

2025-08-12 10:55:23 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m703[0m, in [35msend[0m
    r = adapter.send(request, **kwargs)

2025-08-12 10:55:23 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\adapters.py"[0m, line [35m667[0m, in [35msend[0m
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )

2025-08-12 10:55:23 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-12 10:55:23 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-12 10:55:23 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-12 10:55:23 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-12 10:55:23 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-12 10:55:23 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-12 10:55:23 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-12 10:55:23 - INFO - [1;35mKeyboardInterrupt[0m

