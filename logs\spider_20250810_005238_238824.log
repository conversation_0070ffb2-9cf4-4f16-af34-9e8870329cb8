2025-08-10 00:52:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64930,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 00:52:44 - INFO - <EMAIL>
2025-08-10 00:52:44 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 00:53:04 - INFO - 获取 cap_value 验证码成功...
2025-08-10 00:53:07 - INFO - 找到 Turnstile...
2025-08-10 00:53:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 00:53:09 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 00:53:16 - INFO - 验证码已提交，等待跳转...
2025-08-10 00:53:16 - INFO - 
第 1/5 次尝试注册...
2025-08-10 00:53:16 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 00:53:16 - INFO - 检测到 'Verifying you are human...'（首次），使用网络节流模式
2025-08-10 00:53:16 - INFO - 检测到人机验证，执行 CDP 网络节流 10 秒
2025-08-10 00:53:16 - INFO - 执行网络节流前的拟人操作...
2025-08-10 00:53:16 - INFO - 已执行拟人滚动: -50px
2025-08-10 00:53:22 - INFO - 已执行页面焦点操作
2025-08-10 00:53:23 - INFO - 已执行拟人操作完成，总停顿: 1.25秒
2025-08-10 00:53:23 - INFO - CDP: 已将网络设为节流模式（延迟8367ms），保持 10.2 秒……
2025-08-10 00:53:34 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 00:53:34 - INFO - 已执行拟人滚动: 10px
2025-08-10 00:53:34 - INFO - 已执行页面焦点操作
2025-08-10 00:53:35 - INFO - 已执行拟人操作完成，总停顿: 1.17秒
2025-08-10 00:53:35 - INFO - CDP: 网络节流已恢复为正常状态。
2025-08-10 00:53:35 - INFO - 成功！当前在订阅页面。
2025-08-10 00:53:35 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 00:53:35 - INFO - 代码信息: {
  "codeVerifier": "viBK-ecNaNOg0dsa4X48ouUCbY9n-X2hjp8PQBOvBdA",
  "code_challenge": "qCevOG5IhvniP8mqxTk2KbgUd0OwP1iKAkAzSYOIhnI",
  "state": "f6e5c0e5-613d-4a02-bdb2-1ccdfec30f73"
}
2025-08-10 00:53:35 - INFO - ==================================================
2025-08-10 00:53:35 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_704a2b007f662c67a3dac3177333a42d&state=f6e5c0e5-613d-4a02-bdb2-1ccdfec30f73&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-10 00:53:38 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 00:53:38 - INFO - 添加session: <EMAIL>   d50dfe2ba105cda64a25900ede2d452ed3ca13fccbf457c2a1fe550261fdd33a   https://d10.api.augmentcode.com/  2025-08-16T16:53:20Z
2025-08-10 00:53:38 - INFO - email:<EMAIL> === cookie:.eJxNjcsKwjAURP_lrlvJ--HKPwl53EigiaG2gqj_biguXM5w5swLXMe1-oZtg_O27jhB9rUsT9d8RTgDTHAtD2x_uaTu9juurqRRYPVleStjtY5ZoECWhE05UEKCtwNvtxbHUgrNhdFWSS2pVZayCQ7LIRiiZeupUz04abg0F6mkEfoUc4IfefxRTy1BHmaeE5uFRzYHjnpmaBiJjAemMny-Du1Arg.aJd9EA.bKqHONTs_0VdFiuKSnbY0KIFhiw
2025-08-10 00:53:38 - INFO - 
自动化流程成功完成！
2025-08-10 00:53:38 - INFO - 添加第1个
2025-08-10 00:54:02 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65166,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 00:54:02 - INFO - <EMAIL>
2025-08-10 00:54:02 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 00:54:15 - INFO - 获取 cap_value 验证码成功...
2025-08-10 00:54:18 - INFO - 找到 Turnstile...
2025-08-10 00:54:20 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 00:54:20 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 00:54:20 - INFO - 验证码已提交，等待跳转...
2025-08-10 00:54:20 - INFO - 
第 1/5 次尝试注册...
2025-08-10 00:54:20 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 00:54:27 - INFO - 检测到 'Verifying you are human...'（第2次），使用网络离线模式
2025-08-10 00:54:27 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 00:54:27 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 00:54:27 - INFO - 已执行拟人滚动: 119px
2025-08-10 00:54:28 - INFO - 已执行拟人操作完成，总停顿: 0.98秒
2025-08-10 00:54:34 - INFO - CDP: 已将网络设为离线，保持 8.9 秒……
2025-08-10 00:54:44 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 00:54:44 - INFO - 已执行拟人滚动: 92px
2025-08-10 00:54:45 - INFO - 已执行拟人操作完成，总停顿: 1.29秒
2025-08-10 00:54:45 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 00:54:45 - INFO - 成功！当前在订阅页面。
2025-08-10 00:54:45 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 00:54:45 - INFO - 代码信息: {
  "codeVerifier": "-ahhg5S7mT5vSL83R-V0iF0MxoUZo095SOcnNUUGhvg",
  "code_challenge": "E1OICO-62oW1zVw1KcXdwi2dNIwq8jMM0odG0EO9ZXE",
  "state": "374da8fe-4000-416c-af0d-4d1826df1dbf"
}
2025-08-10 00:54:45 - INFO - ==================================================
2025-08-10 00:54:45 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_6e93bd82c849685b6dfaf08faa18c577&state=374da8fe-4000-416c-af0d-4d1826df1dbf&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-10 00:54:47 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 00:54:47 - INFO - 添加session: <EMAIL>   c9ef08f72935a8781c358c9956132ac13d72980bdb2568257c6b55a897c12971   https://d10.api.augmentcode.com/  2025-08-16T16:54:32Z
2025-08-10 00:54:47 - INFO - email:<EMAIL> === cookie:.eJxNjd0OgjAMRt-l12AY62jHlW-yDFbMIkyDoPHv3V3QC7-bps3p-Z7gzjJPPklaoF3mVQoY_BTHu0t-EmgBCjjEq6S_PYazWy8yuxjyQSYfx1fDlijoTlDqgDYMnWrIo854OqU-f6LVzFhxjraKK9ZcwKbZDNn0OPr-psggGUaNe9PkSbt-CPAjv4Weu4q8KS2iKdEqKbmjvqSaajaKKtEC7w8qu0AX.aJd9Vg._i8Id1LNjzucYdMob11iDVTx2Hw
2025-08-10 00:54:47 - INFO - 
自动化流程成功完成！
2025-08-10 00:54:47 - INFO - 添加第2个
2025-08-10 00:55:04 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65324,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 00:55:05 - INFO - <EMAIL>
2025-08-10 00:55:05 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 00:55:19 - INFO - 获取 cap_value 验证码成功...
2025-08-10 00:55:22 - INFO - 找到 Turnstile...
2025-08-10 00:55:22 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 00:55:25 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 00:55:25 - INFO - 验证码已提交，等待跳转...
2025-08-10 00:55:32 - INFO - 
第 1/5 次尝试注册...
2025-08-10 00:55:32 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 00:55:32 - INFO - 检测到 'Verifying you are human...'（第3次），使用网络离线模式
2025-08-10 00:55:32 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 00:55:32 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 00:55:32 - INFO - 已执行拟人滚动: 38px
2025-08-10 00:55:33 - INFO - 已执行拟人操作完成，总停顿: 1.40秒
2025-08-10 00:55:39 - INFO - CDP: 已将网络设为离线，保持 7.6 秒……
2025-08-10 00:55:48 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 00:55:48 - INFO - 已执行拟人滚动: 94px
2025-08-10 00:55:49 - INFO - 已执行页面焦点操作
2025-08-10 00:55:49 - INFO - 已执行拟人操作完成，总停顿: 1.48秒
2025-08-10 00:55:49 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 00:55:49 - INFO - 成功！当前在订阅页面。
2025-08-10 00:55:49 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 00:55:49 - INFO - 代码信息: {
  "codeVerifier": "M_sqAX1brMRu0yjRrPHwU69nUx7lgf3rgWr4VQiFKFA",
  "code_challenge": "5wAykTEZ0woKwxtu4PRTMbLL7_4St84B4vkydMCbn_8",
  "state": "645b5989-c629-4f66-9296-6bd07c13c186"
}
2025-08-10 00:55:49 - INFO - ==================================================
2025-08-10 00:55:50 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_a989374228c21d057e2beaddd015d79f&state=645b5989-c629-4f66-9296-6bd07c13c186&tenant_url=https%3A%2F%2Fd16.api.augmentcode.com%2F
2025-08-10 00:55:52 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 00:55:52 - INFO - 添加session: <EMAIL>   e74a9a78f43f254014c572a377b5601073c4100e34fe97c62e84b9703188713d   https://d16.api.augmentcode.com/  2025-08-16T16:55:38Z
2025-08-10 00:55:52 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgjAQRP9lz2Ao3Xa3nPwTUujWNIFKEIxG_Xcb4sHTZCYzb17QL7LOPkveoNvWXSqIfk7Ts89-FugAKriku-Q_n8LS7zdZ-xRKILNP09uyIwo0CEob0IU4qBFJ-1LP1zyWJRvXMqkWbcuIBtmaCg7MQSik4TFMosggGUbHZ2OL0mmMAX7N4zAqX_Lg60ZQ1aiRa9aka9uIBCtRO27g8wU7-kBg.aJd9lg.lZjB9W4Rn2nTGTjRbXGRVaMiOqk
2025-08-10 00:55:52 - INFO - 
自动化流程成功完成！
2025-08-10 00:55:52 - INFO - 添加第3个
2025-08-10 00:56:14 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65479,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 00:56:15 - INFO - <EMAIL>
2025-08-10 00:56:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 00:56:28 - INFO - 获取 cap_value 验证码成功...
2025-08-10 00:56:31 - INFO - 找到 Turnstile...
2025-08-10 00:56:32 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 00:56:32 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 00:56:40 - INFO - 验证码已提交，等待跳转...
2025-08-10 00:56:40 - INFO - 
第 1/5 次尝试注册...
2025-08-10 00:56:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 00:56:40 - INFO - 检测到 'Verifying you are human...'（第4次），使用网络离线模式
2025-08-10 00:56:40 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 00:56:40 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 00:56:40 - INFO - 已执行拟人滚动: -138px
2025-08-10 00:56:46 - INFO - 已执行页面焦点操作
2025-08-10 00:56:47 - INFO - 已执行拟人操作完成，总停顿: 1.29秒
2025-08-10 00:56:47 - INFO - CDP: 已将网络设为离线，保持 9.1 秒……
2025-08-10 00:56:57 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 00:56:57 - INFO - 已执行拟人滚动: 42px
2025-08-10 00:56:59 - INFO - 已执行拟人操作完成，总停顿: 1.18秒
2025-08-10 00:56:59 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 00:56:59 - INFO - 成功！当前在订阅页面。
2025-08-10 00:56:59 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 00:56:59 - INFO - 代码信息: {
  "codeVerifier": "XqTR7GsJVbgI2VNIdGazT9maJrh__aZ0d_tnLdutY0A",
  "code_challenge": "jtZiruQ3HsNNp0NwixBflEiXPp6aVjRx_O1e-_Nv1I0",
  "state": "115bab9c-f0c3-4d5c-bf7a-eebd1e8bff39"
}
2025-08-10 00:56:59 - INFO - ==================================================
2025-08-10 00:56:59 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_bdba7c3d277b03a9d80dca42fe9d92db&state=115bab9c-f0c3-4d5c-bf7a-eebd1e8bff39&tenant_url=https%3A%2F%2Fd19.api.augmentcode.com%2F
2025-08-10 00:57:01 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 00:57:01 - INFO - 添加session: <EMAIL>   d8e488c0c9d47ae100c08466755c19adf6704d2160d08a090e8a70ab2eab3a5d   https://d19.api.augmentcode.com/  2025-08-16T16:56:45Z
2025-08-10 00:57:01 - INFO - email:<EMAIL> === cookie:.eJxNjcsOwiAURP_lrosByrMr_4TwuDXEglrbJo3675LGhcuZnDnzAnfHufiKdYFhmVfsYPQlT7urviAMAB1c8ob1L-d0d-sTZ5dTK7D4PL2VsVqnSFEgT8KmMXBuYo8Nr7ca21LShkjBmaWSK8q0FR0cmsPQTPv2CFfWGC2NVOYslTRCn-KY4Eceh72mgY1eEBVNJAJNIJ4LTvoglWXtn6oePl9Bo0Bb.aJd93A.BAuxSHlpHkXbQyfyWQo94xPh2jU
2025-08-10 00:57:01 - INFO - 
自动化流程成功完成！
2025-08-10 00:57:01 - INFO - 添加第4个
2025-08-10 00:57:17 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:49219,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 00:57:18 - INFO - <EMAIL>
2025-08-10 00:57:18 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 00:57:35 - INFO - 获取 cap_value 验证码成功...
2025-08-10 00:57:38 - INFO - 找到 Turnstile...
2025-08-10 00:57:40 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 00:57:40 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 00:57:40 - INFO - 验证码已提交，等待跳转...
2025-08-10 00:57:40 - INFO - 
第 1/5 次尝试注册...
2025-08-10 00:57:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 00:57:48 - INFO - 检测到 'Verifying you are human...'（第5次），使用网络离线模式
2025-08-10 00:57:48 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 00:57:48 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 00:57:48 - INFO - 已执行拟人滚动: 8px
2025-08-10 00:57:54 - INFO - 已执行页面焦点操作
2025-08-10 00:57:54 - INFO - 已执行拟人操作完成，总停顿: 1.19秒
2025-08-10 00:57:54 - INFO - CDP: 已将网络设为离线，保持 7.8 秒……
2025-08-10 00:58:03 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 00:58:03 - INFO - 已执行拟人滚动: 93px
2025-08-10 00:58:04 - INFO - 已执行拟人操作完成，总停顿: 1.34秒
2025-08-10 00:58:04 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 00:58:04 - INFO - 成功！当前在订阅页面。
2025-08-10 00:58:04 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 00:58:04 - INFO - 代码信息: {
  "codeVerifier": "D8jhGIUuuqNyzwhaiVHqrEAf_4BJr70kEKLxcaz3XEw",
  "code_challenge": "4gDdlyW9-crw4dnw7pI7TG28f60kEf3-kcvzQyQHB4k",
  "state": "942f81cb-58c8-4e89-9e1b-6d639d26304a"
}
2025-08-10 00:58:04 - INFO - ==================================================
2025-08-10 00:58:05 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_feeab0b5fd7dd80a59ba7d27ad4cc9ec&state=942f81cb-58c8-4e89-9e1b-6d639d26304a&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-10 00:58:07 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 00:58:07 - INFO - 添加session: <EMAIL>   99dff27a102998759766ab0a94b88c7bf131c9292b0476916c5e358528b96f2e   https://d13.api.augmentcode.com/  2025-08-16T16:57:52Z
2025-08-10 00:58:07 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2D6t93Wk29CkN2SGqgGwcSo725DPHicyTffvKC7yTL3RcoKx3XZpIHUz3l6dqWfBY4ADYz5IeUvZ751212WLnMtZO7z9PYhEomy4sSwi5zOpjbOV7xcy1CXXrkYMbhAkXT0RpFtYNfshmqa8jBeNKEjDN7qE_qK02FIDD9yP_RkWelkW50Utg4R24DMrQkGrQw2WHbw-QLr0T9u.aJd-Hg.3jy6q0mcUs55BaavanDDcJFJZGQ
2025-08-10 00:58:07 - INFO - 
自动化流程成功完成！
2025-08-10 00:58:07 - INFO - 添加第5个
2025-08-10 00:58:28 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:49354,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 00:58:28 - INFO - <EMAIL>
2025-08-10 00:58:28 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 00:58:44 - INFO - 获取 cap_value 验证码成功...
2025-08-10 00:58:46 - INFO - 找到 Turnstile...
2025-08-10 00:58:48 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 00:58:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 00:58:56 - INFO - 验证码已提交，等待跳转...
2025-08-10 00:58:56 - INFO - 
第 1/5 次尝试注册...
2025-08-10 00:58:56 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 00:58:56 - INFO - 检测到 'Verifying you are human...'（第6次），使用网络离线模式
2025-08-10 00:58:56 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 00:58:56 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 00:58:56 - INFO - 已执行拟人滚动: -111px
2025-08-10 00:59:03 - INFO - 已执行页面焦点操作
2025-08-10 00:59:03 - INFO - 已执行拟人操作完成，总停顿: 0.96秒
2025-08-10 00:59:03 - INFO - CDP: 已将网络设为离线，保持 8.3 秒……
2025-08-10 00:59:13 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 00:59:13 - INFO - 已执行拟人滚动: 60px
2025-08-10 00:59:14 - INFO - 已执行拟人操作完成，总停顿: 1.43秒
2025-08-10 00:59:14 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 00:59:14 - INFO - 成功！当前在订阅页面。
2025-08-10 00:59:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 00:59:14 - INFO - 代码信息: {
  "codeVerifier": "yVvdPWjMKzTfVks72fAkvJRO7_R0GfnaFCTo8kOLvro",
  "code_challenge": "jN1RXJ86dpD-UzPM4KdLRgru8NqjJ_TOQYe7DGW54tU",
  "state": "f530de5d-9b29-4c53-bd49-f91c57eab089"
}
2025-08-10 00:59:14 - INFO - ==================================================
2025-08-10 00:59:15 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_77ba4fecc4702cdb1e87f441ce4838e9&state=f530de5d-9b29-4c53-bd49-f91c57eab089&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-10 00:59:18 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 00:59:18 - INFO - 添加session: <EMAIL>   6a17ffccf0c4bc5d5d5038c37e8dcfa4a5fca0d699617b4f6f010d9fc0d5841f   https://d6.api.augmentcode.com/  2025-08-16T16:59:01Z
2025-08-10 00:59:18 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgjAQRP9lz2AKbLtbTv4JKd3F1EA1CBqj_ruEePA4kzdvXtBddZ5C1rxAu8yrFjCEKY3PLodJoQUo4JTumv9ykmu33nTukmyFTiGNb8eeSJEVtRb0MvS1Ritmw_Mlx22JaBokqtkZJjTokAvYNbthM0l8nJeKLJJlMvXROstIhzgI_Mj90LDvlTWUMfqqRE9DyYih7GNsxEgjYhx8vkirQUc.aJd-Yw.Xs7ng8WReVB33mhGGYnxAjkoPUY
2025-08-10 00:59:18 - INFO - 
自动化流程成功完成！
2025-08-10 00:59:18 - INFO - 添加第6个
2025-08-10 00:59:25 - INFO - Traceback (most recent call last):

2025-08-10 00:59:25 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m976[0m, in [35m<module>[0m
    [31mtime.sleep[0m[1;31m(random.uniform(10,20))[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-10 00:59:25 - INFO - [1;35mKeyboardInterrupt[0m

