import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from main import get_captcha,get_email,get_email_data,get_codeinfo,get_augtoken,add_session
from selenium.webdriver.common.action_chains import ActionChains
from RoxyClient import RoxyClient
import json
import re,os
import html
import logging
from logging.handlers import RotatingFileHandler
import string
import requests
import email
import html
import uuid
from email.utils import formatdate
s = requests.Session()


# 设置日志
def setup_logger():
    # 如果日志目录不存在则创建
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 配置日志器
    logger = logging.getLogger('Roxyspider')
    logger.setLevel(logging.DEBUG)

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 文件处理器（轮转日志文件，每个文件最大5MB，保留5个备份文件）
    file_handler = RotatingFileHandler(
        'logs/spider.log',
        maxBytes=5*1024*1024,  # 5MB
        backupCount=5,
        encoding='utf-8'  # 指定UTF-8编码
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# 初始化日志器
logger = setup_logger()
s = requests.Session()

def setup_cdp(driver):
    """通过 CDP 做一些常见的指纹与稳定性优化。"""
    try:
        # 1) 在新文档注入脚本，隐藏 webdriver 等指纹
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
Object.defineProperty(navigator, 'languages', { get: () => ['en-US','en'] });
Object.defineProperty(navigator, 'platform', { get: () => 'Win32' });
Object.defineProperty(navigator, 'plugins', { get: () => [1,2,3,4,5] });

const originalQuery = navigator.permissions && navigator.permissions.query;
if (originalQuery) {
  navigator.permissions.query = (parameters) => (
    parameters && parameters.name === 'notifications'
      ? Promise.resolve({ state: Notification.permission })
      : originalQuery(parameters)
  );
}

const getParameter = WebGLRenderingContext && WebGLRenderingContext.prototype.getParameter;
if (getParameter) {
  WebGLRenderingContext.prototype.getParameter = function(parameter){
    if (parameter === 37445) { return 'Intel Inc.'; }
    if (parameter === 37446) { return 'Intel Iris OpenGL Engine'; }
    return getParameter.call(this, parameter);
  };
}
"""
        })

        # 2) 启用网络、设置 UA 与请求头
        driver.execute_cdp_cmd("Network.enable", {})
        ua = driver.execute_script("return navigator.userAgent") or ""
        if "Headless" in ua:
            ua = ua.replace("Headless", "").strip()
        driver.execute_cdp_cmd("Network.setUserAgentOverride", {
            "userAgent": ua,
            "platform": "Windows"
        })
        driver.execute_cdp_cmd("Network.setExtraHTTPHeaders", {
            "headers": {
                "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7"
            }
        })

        # 3) 时区与 CSP
        try:
            driver.execute_cdp_cmd("Emulation.setTimezoneOverride", {"timezoneId": "America/Los_Angeles"})
        except Exception:
            pass
        try:
            driver.execute_cdp_cmd("Page.setBypassCSP", {"enabled": True})
        except Exception:
            pass

    except Exception as e:
        print(f"CDP 初始化失败: {e}")

def setup_driver():
    """初始化并返回一个 Chrome WebDriver 实例。"""
    # 这里可以添加更多的 WebDriver 配置，例如无头模式等
    # options = webdriver.ChromeOptions()
    # options.add_argument('--headless')
    # driver = webdriver.Chrome(options=options)
    brwoser_id = "1c42949de80fb5f609dc1fcee8fddde3"
    # 初始化客户端
    client = RoxyClient(port=50000,token="76b402c778930dd44bacc693cb2fb8d0")
    client.browser_local_cache([brwoser_id])
    client.browser_server_cache(22854,[brwoser_id])
    client.browser_random_env(22854,[brwoser_id])
    # 打开浏览器
    rsp = client.browser_open(brwoser_id)
    if rsp.get("code") != 0:
        print("浏览器打开失败:",rsp)
        exit(0)
    # 获取selenium的连接信息
    debuggerAddress = rsp.get("data").get("http")
    driverPath = rsp.get("data").get("driver")
    print(f"浏览器打开成功,debuggerAddress:{debuggerAddress},driverPath:{driverPath}")

    # selenium 连接代码
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_experimental_option("debuggerAddress", debuggerAddress)

    chrome_service = Service(driverPath)
    driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
    #driver = webdriver.Chrome()
    driver.implicitly_wait(10) # 设置一个隐式等待
    return driver


def get_captcha(url,retry=0):
    baseurl = "http://************:5000" #"http://************:5000"
    def _getcap_value(id):
        cap_value = s.get(baseurl + "/result",params={"id":id})
        try:
            if cap_value.text != "CAPTCHA_NOT_READY":
                return cap_value.json()["value"]
            else:
                time.sleep(5)
                #print(f"{get_current_method_name()} --获取 cap_value 失败，重试...")
                return _getcap_value(id)
        except Exception as e:
            #print(f"{get_current_method_name()}发生错误: ====== {cap_value.text}")
            return _getcap_value(id)


    task_id = s.get(baseurl + "/turnstile",params={"url":url,"sitekey":"0x4AAAAAAAQFNSW6xordsuIq"}).json()["task_id"]

    if task_id:
        cap_value = _getcap_value(task_id)
        # print(cap_value)
        if cap_value != "CAPTCHA_FAIL":
            print(f"获取 cap_value 验证码成功...")
            return cap_value
        else:
            print(f"获取验证码失败，重试次数: {retry+1}")
            return get_captcha(url,retry+1)


def get_email():
    #SWITCH_EMAIL = rando你是什么模型你是m.choice(list(ALLDOMAIN.keys()))
    baseurl = "https://es.slogo.eu.org"
    #s.proxies = {"http": "http://127.0.0.1:10808", "https": "http://127.0.0.1:10808"}
    def _generate_random_name():
        # 生成5位英文字符
        letters1 = ''.join(random.choices(string.ascii_lowercase, k=5))
        # 生成1-3个数字
        numbers = ''.join(random.choices(string.digits, k=random.randint(1, 3)))
        # 生成1-3个英文字符
        letters2 = ''.join(random.choices(string.ascii_lowercase, k=random.randint(1, 3)))
        # 组合成最终名称
        return letters1 + str(int(time.time()))

    def _fetch_email_data(name):
        try:
            res = requests.post(
                baseurl+"/admin/new_address",
                json={
                    "enablePrefix": True,
                    "name": name,
                    "domain": DOMAIN
                    #"domain": "smartdocker.online",
                },
                headers={
                    'x-admin-auth': "chen1234.",
                    "Content-Type": "application/json"
                },
                proxies={"http": "http://***********:10808", "https": "http://***********:10808"}
            )
            if res.status_code == 200:
                response_data = res.json()
                email = response_data.get("address", 0)
                jwt = response_data.get("jwt", 0)
                return email,jwt
            else:
                print(f"请求失败，状态码: {res.status_code}")
                return None
        except requests.RequestException as e:
            print(f"请求出现错误: {e}")
            return None
    return _fetch_email_data(_generate_random_name())

def is_time_within_range(email_date_str, target_date_str, minutes=1):
    """
    检查邮件日期是否在目标时间的指定分钟范围内

    参数:
        email_date_str: 邮件中的日期字符串，格式如 "Thu, 3 Jul 2025 04:10:52 +0000"
        target_date_str: 目标日期字符串，相同格式
        minutes: 允许的时间差（分钟）

    返回:
        布尔值，表示是否在时间范围内
    """
    from email.utils import parsedate_to_datetime
    from datetime import datetime, timedelta

    try:
        # 解析邮件日期字符串为datetime对象
        email_date = parsedate_to_datetime(email_date_str)

        # 解析目标日期字符串为datetime对象
        target_date = parsedate_to_datetime(target_date_str)

        # 计算时间差（绝对值）
        time_diff = abs((email_date - target_date).total_seconds())

        # 检查时间差是否在允许范围内（转换为秒）
        return time_diff <= (minutes * 60)
    except Exception as e:
        print(f"日期比较出错: {e}")
        return False

def extract_email_elements(email_message_str):
    code = None
    # 将字符串转换为字节类型（如果原始字符串不是字节类型的话）
    email_message_bytes = email_message_str.encode('utf-8')

    # 使用BytesParser解析字节字符串为Message对象
    msg = email.message_from_bytes(email_message_bytes)

    # 访问邮件的各个部分
    if "<EMAIL>" in msg['From']:


        # 检查邮件日期是否在当前时间附近1分钟内
        target_time = formatdate(localtime=False, usegmt=True)  # 获取当前时间，格式如 "Thu, 3 Jul 2025 04:10:52 GMT"
        if is_time_within_range(msg['Date'], target_time, minutes=2):
            # 访问邮件正文
            if msg.is_multipart():
                for part in msg.walk():
                    # 每个part都是一个Message对象，我们可以检查其内容类型
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))

                    # 打印文本内容
                    if content_type == "text/plain" and "attachment" not in content_disposition:
                        body = part.get_payload(decode=True).decode()  # 解码并获取文本内容
                        match = re.search(r'Your verification code is: (\d{6})', body)
                        if match:
                            code = match.group(1)
        else:
            pass
            print(f'{msg['Date']} 时间不在最近 {target_time }')




    return code

def get_email_data(jwt):
    baseurl = "https://es.slogo.eu.org"
    code = None
    if jwt:

        res = s.get(
            baseurl+"/api/mails",
            params={"limit":10,"offset":0},
            proxies={"http": "http://***********:10808", "https": "http://***********:10808"},
            headers={
                "Authorization": f"Bearer {jwt}",
                # "x-custom-auth": "<你的网站密码>", # 如果启用了自定义密码
                "Content-Type": "application/json"
        })

        results = res.json().get("results", 0)
        if results:
            raw = results[0].get("raw", 0)
            #print(f"{get_current_method_name()} -- results -- success")
            code = extract_email_elements(raw)
        else:
            #print(f"{get_current_method_name()} -- no results")
            return get_email_data(jwt)

    return code


def get_augtoken(url,code_verifier):

    from urllib.parse import urlparse, parse_qs
    _query_str = urlparse(url).query  # 提取 ? 后面的部分
    query_str = _query_str.replace("&amp;", "&")
    # 解析参数并转为字典（重复 key 会变成数组）
    params = parse_qs(query_str)
    tenant_url = params.get("tenant_url",0)[0]
    code = params.get("code",0)[0]
    data = {}
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Augment.vscode-augment/0.482.1 (win32; x64; 10.0.19045) vscode/1.95.3",
        "x-request-id": str(uuid.uuid4()),
        "x-request-session-id": str(uuid.uuid4()),
        "x-api-version": "2"
    }
    body = {
        "grant_type": "authorization_code",
        "client_id": "augment-vscode-extension",
        "code_verifier": code_verifier,
        "redirect_uri": "vscode://augment.vscode-augment/auth/result",
        "code": code
    }
    res = s.post(tenant_url+"token",headers=headers,json=body).json()
    access_token = res.get("access_token", 0)
    if not access_token:
        return None
    data["accessToken"] = access_token
    headers["Authorization"] = f"Bearer {access_token}"
    res_info = s.post(tenant_url+"subscription-info",headers=headers,json={}).json()
    end_date = res_info.get("subscription",0).get("ActiveSubscription",0).get("end_date", 0)
    if not end_date:
        return None
    data["end_date"] = end_date
    data["tenantURL"] = tenant_url

    return data


def add_session(email,augmentSession:dict,expire_time="",other=""):
    data = {
            "email":email,
            "augmentSession":augmentSession,
            "expire_time":expire_time,
            "other":other
        }
    res =s.post("https://aug.202578.xyz/add_session",
                 headers={"X-User-ID": "admin"},
                 json=data
                 )
    if res.status_code == 200:
        print(f"添加session成功: {res.json()}")
    else:
        print(f"添加session失败: {res.text}")


def human_like_typing(element, text):
    """模拟人类打字，在每个字符之间加入随机延迟。"""
    for char in text:
        element.send_keys(char)
        time.sleep(random.uniform(0.05, 0.15))

def human_like_mouse_move_and_click(driver, element):
    """
    NEW: 模拟人类的鼠标移动轨迹和点击。
    它会先移动到元素附近，停顿一下，再移动到元素中心并点击。
    """
    actions = ActionChains(driver)

    # 步骤 1: 移动到元素附近的一个随机位置
    offset_x = random.randint(-30, 30)
    offset_y = random.randint(-30, 30)
    actions.move_to_element_with_offset(element, offset_x, offset_y)
    actions.pause(random.uniform(0.2, 0.5))

    # 步骤 2: 精准移动到元素中心
    actions.move_to_element(element)
    actions.pause(random.uniform(0.1, 0.3))

    # 步骤 3: 点击
    actions.click()

    # 执行所有链式操作
    actions.perform()
    print(
        f"已模拟人类轨迹点击元素: {element.tag_name} (ID: {element.get_attribute('id')}, Class: {element.get_attribute('class')})")


def login(driver, username):
    """
    执行登录步骤。
    1. 打开登录页面
    2. 输入用户名
    3. 点击登录按钮
    """
    print("步骤 1 & 2: 正在打开登录页面并输入用户名...")
    driver.get("https://app.augmentcode.com/account")
    try:
        # 等待用户名输入框加载完成并输入
        username_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "username"))
        )
        capvalue = get_captcha(driver.current_url)
        captchainbpx = driver.find_element(By.NAME, 'captcha')
        username_input.clear()
        human_like_typing(username_input,username)
        #username_input.send_keys(username)

        # 找到并点击登录按钮
        login_button = driver.find_element(By.NAME, "action")


        if captchainbpx and login_button:
            print(f"找到 Turnstile...")
            driver.execute_script("arguments[0].value = '{}';".format(capvalue), captchainbpx)
            login_button.click()
            print("登录信息已提交，等待验证页面...")
            return True
        return False
    except TimeoutException:
        print("错误：登录页面加载超时或找不到登录元素。")
        return False

def verify_email(driver, email_code):
    """
    执行邮箱验证步骤。
    1. 输入验证码
    2. 点击验证按钮
    """
    print("步骤 3 & 4: 正在输入邮箱验证码...")
    try:
        # 等待验证码输入框加载完成并输入
        code_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "code"))
        )
        code_input.clear()
        code_input.send_keys(email_code)

        # 找到并点击验证按钮
        verify_button = driver.find_element(By.NAME, "action")
        verify_button.click()
        print("验证码已提交，等待跳转...")
        return True
    except TimeoutException:
        print("错误：验证页面加载超时或找不到验证元素。")
        return False

def attempt_signup_with_retry(driver):
    """
    执行注册的最后步骤，并包含重试逻辑。
    - 勾选复选框
    - 点击注册按钮
    - 如果失败，则根据错误信息进行重试或返回错误状态
    """
    max_retries = 5
    final_url = "https://app.augmentcode.com/account/subscription"
    for attempt in range(max_retries):
        print(f"\n第 {attempt + 1}/{max_retries} 次尝试注册...")
        try:
            # 步骤 5: 查找并勾选复选框，然后点击注册按钮
            print("步骤 5: 正在勾选复选框并点击注册按钮...")
            if "Verifying you are human..." in driver.page_source:
                print("等待5秒 Verifying you are human...")
                logger.info(driver.page_source)
                time.sleep(5)
            if driver.current_url == final_url:
                print("成功！当前在订阅页面。")
                return "SUCCESS"


            # # 等待复选框出现并确保它没有被选中
            # checkbox_mark = WebDriverWait(driver, 20).until(
            #     EC.presence_of_element_located((By.CLASS_NAME, "c-checkbox--mark"))
            # )
            # # 使用JS点击以防止元素被遮挡
            # driver.execute_script("arguments[0].click();", checkbox_mark)
            # print("复选框已勾选。")

            # # 等待注册按钮可点击
            # signup_button = driver.find_element(By.ID, "signup-button")
            # human_like_mouse_move_and_click(driver,signup_button)
            # #signup_button.click()
            # print("注册按钮已点击，等待页面加载...")

            # 等待页面跳转后的结果
            time.sleep(5) # 等待一下，让页面有时间响应

            # 步骤 6: 检查是否出现 "Sign-up rejected"
            try:
                rejection_element = driver.find_element(By.XPATH, "/html/body/div/div/div/div")
                if "Sign-up rejected" in rejection_element.text:
                    print(f"检测到 'Sign-up rejected'。准备回退并刷新。")

                    # 回退页面
                    driver.back()
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.ID, "signup-button"))
                    )
                    print("已回退到上一页。")

                    # 步骤 7: 检查是否出现 "Oops!" 错误
                    if "Oops!, something went wrong" in driver.page_source:
                        print("检测到 'Oops!, something went wrong' 错误。")
                        return "RESTART" # 返回一个特殊信号，表示需要从头开始

                    # 随机刷新


                    driver.refresh()
                    sleep_time = random.uniform(3, 5)
                    print(f"   等待 {sleep_time:.2f} 秒...")
                    time.sleep(random.uniform(7, 10))
                    # WebDriverWait(driver, 20).until(
                    #     EC.element_to_be_clickable((By.ID, "signup-button"))
                    # )
                    # for i in range(refresh_count):
                    #     driver.refresh()
                    #     sleep_time = random.uniform(3, 5)
                    #     print(f"  第 {i + 1} 次刷新后，等待 {sleep_time:.2f} 秒...")
                    #     time.sleep(sleep_time)
                    #     WebDriverWait(driver, 20).until(
                    #         EC.element_to_be_clickable((By.ID, "signup-button"))
                    #     )

                    print("刷新完成，继续下一次尝试。")
                    continue # 继续 for 循环的下一次迭代

            except NoSuchElementException:
                # 如果找不到拒绝元素，说明可能成功了，跳出循环进行URL检查
                print("未检测到 'Sign-up rejected'。")
                pass

            # 步骤 8: 检查最终的 URL
            print("步骤 8: 检查当前 URL 是否为订阅页面...")

            # 等待URL变为目标URL，最多等待10秒
            WebDriverWait(driver, 10).until(EC.url_to_be(final_url))

            if driver.current_url == final_url:
                print("成功！已跳转到订阅页面。")
                return "SUCCESS"
            else:
                # 如果URL不匹配，但也没有拒绝信息，可能是一个未知的状态
                print(f"警告：当前 URL 为 {driver.current_url}，与预期不符。")
                # 这种情况也视为一次失败的尝试
                driver.back() # 尝试回退以进行下一次重试
                WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.ID, "signup-button"))
                )


        except TimeoutException as e:
            print(f"在尝试注册时发生超时错误: {e}")
            logger.error(f"在尝试注册时发生超时错误: {e}")
            #logger.error(driver.page_source)
            # 超时也可能意味着需要重试，先回退
            try:
                driver.back()
                WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.ID, "signup-button"))
                )
            except Exception as back_e:
                print(f"回退失败: {back_e}，可能需要重启流程。")
                return "RESTART"
        except Exception as e:
            print(f"在尝试注册时发生未知错误: {e}")
            logger.error(f"在尝试注册时发生未知错误: {e}")
            #logger.error(driver.page_source)
            return "RESTART" # 发生其他严重错误时，最好从头开始

    print("已达到最大重试次数，注册失败。")
    return "FAILURE"

def get_vscode(driver,email):
    data = get_codeinfo()
    print(f"代码信息: {json.dumps(data, indent=2)}")
    print("=" * 50)
    if data:
        params = {
            "response_type": "code",
            "code_challenge": data["code_challenge"],
            "code_challenge_method": "S256",
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult",
            "state": data["state"],
            "scope": "email",
            "prompt": "login"
        }
        query_str = "&".join([f"{k}={v}" for k, v in params.items()])
        driver.get("https://auth.augmentcode.com/terms-accept" + f"?{query_str}")
        #cookie = tab.cookies().as_dict().get("session", "")
        cookie = driver.get_cookie("session").get("value","")
        res = driver.page_source
        # res = s.get(url="https://auth.augmentcode.com/terms-accept",params=params,cookies=cookie).text
        if "vscode://" in res:
            match = re.search(r'href="(vscode://[^"]+)"', res)
            if match:
                decoded_url = html.unescape(match.group(1))
                print("Extracted URL:", decoded_url)
                tokeninfo = get_augtoken(decoded_url, data["codeVerifier"])
                if tokeninfo:
                    accessToken = tokeninfo.get("accessToken", 0)
                    tenantURL = tokeninfo.get("tenantURL", 0)
                    end_date = tokeninfo.get("end_date", 0)
                    try:
                        ###smartdocker.online 565847.cfd
                        add_session(email.replace(DOMAIN,"565.com"),{
                            "accessToken":accessToken,
                            "tenantURL":tenantURL,
                            "scopes":["email"]
                            },expire_time=end_date,other=cookie)
                        print(f"添加session: {email}   {accessToken}   {tenantURL}  {end_date}")
                        print(f"email:{email} === cookie:{cookie}")
                    except Exception as e:
                        print("添加会话出错")
                        logging.info(accessToken,tenantURL)
                        print({
                            "accessToken":accessToken,
                            "tenantURL":tenantURL,
                            "scopes":["email"]}
                            ,expire_time=end_date,other=cookie)




def main():
    """主函数，控制整个自动化流程"""
    # 请在这里替换为您的真实信息
    USERNAME = "<EMAIL>"
    EMAIL_CODE = "123456" # 这是一个示例验证码
    email_jwt = get_email()
    #email_jwt = "<EMAIL>","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.xkrN3GTv4GD_mOooycAcAdpQM_VfIOZiS7LaeuCYSsY"
    if not email_jwt:
        return "FAILURE"
    email, jwt = email_jwt

    while True:
        try:
            driver = setup_driver()
            logger.info(email)
            if not login(driver, email):
                # 如果登录失败，则没有必要继续
                raise Exception("登录步骤失败")
            code = get_email_data(jwt)
            if not verify_email(driver, code):
                # 如果验证失败，则没有必要继续
                raise Exception("邮箱验证步骤失败")

            # 进入最关键的注册和重试步骤
            result = attempt_signup_with_retry(driver)

            if result == "SUCCESS":
                print("\n进入订阅页面，开始获取vscode_url")
                try:
                    get_vscode(driver,email)
                    print("\n自动化流程成功完成！")
                    break # 成功，跳出 while 循环
                except Exception as e:
                    get_augtoken(driver,email)
            elif result == "RESTART":
                print("\n检测到严重错误，将关闭浏览器并重新开始整个流程...")
                # continue 会自动进入下一次 while 循环
            else: # result == "FAILURE"
                print("\n所有重试均告失败，流程终止。")
                break # 失败，跳出 while 循环

        except Exception as e:
            print(f"\n主流程中发生严重错误: {e}")
            print("准备重启流程...")

            # if driver:
            #     driver.quit()
        # 如果需要重启，在外层循环的下一次迭代开始前等待一下
        time.sleep(200)


if __name__ == "__main__":
    n = 0
    DOMAIN = "565847.cfd"
    while True:
        try:
            main()
            n+=1
            print(f"添加第{n}个")
            time.sleep(random.uniform(100,200))
            #time.sleep(random.uniform(60,100))
        except Exception as e:
            print(f"  报错 \n{e}")
            # if n >= 10:
            #     break
