import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from main import get_captcha,get_email,get_email_data,get_codeinfo,get_augtoken,add_session
from selenium.webdriver.common.action_chains import ActionChains
from RoxyClient import RoxyClient
import json
import re,os
import html
import logging
from logging.handlers import RotatingFileHandler
import string
import requests
import email
import html
import uuid
from email.utils import formatdate
from datetime import datetime
s = requests.Session()

# 全局变量用于存储网络日志
network_logs = []
websocket_logs = []
har_data = {
    "log": {
        "version": "1.2",
        "creator": {
            "name": "RoxySpider",
            "version": "1.0"
        },
        "entries": [],
        "websockets": []  # 添加 WebSocket 数据
    }
}


# 设置日志
def setup_logger():
    # 如果日志目录不存在则创建
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 全局清理：移除 root 和已存在 logger 的所有 handler，避免触发遗留的文件句柄
    try:
        for name in list(logging.Logger.manager.loggerDict.keys()):
            lg = logging.getLogger(name)
            for h in list(getattr(lg, 'handlers', [])):
                try:
                    h.close()
                except Exception:
                    pass
                lg.removeHandler(h)
        for h in list(logging.root.handlers):
            try:
                h.close()
            except Exception:
                pass
            logging.root.removeHandler(h)
    except Exception:
        pass

    # 配置日志器
    logger = logging.getLogger('Roxyspider')
    logger.setLevel(logging.DEBUG)

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 文件处理器（轮转日志文件，每个文件最大5MB，保留5个备份文件）
    # 为避免 Windows 上被其他进程占用，默认使用带时间戳+PID 的独立日志文件
    unique_log = os.path.join('logs', f"spider_{time.strftime('%Y%m%d_%H%M%S')}_{os.getpid()}.log")
    file_handler = RotatingFileHandler(
        unique_log,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=5,
        encoding='utf-8',      # 指定UTF-8编码
        delay=True
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志器（避免重复添加）
    logger.handlers.clear()
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 禁止向 root logger 传播，避免其他进程残留的 file handler（如 spider.log）被触发
    logger.propagate = False

    # 避免 logging 打印 handler 内部异常堆栈
    logging.raiseExceptions = False

    return logger

# 初始化日志器
logger = setup_logger()
s = requests.Session()

# 将所有 print 同步写入 logger.info
try:
    import builtins as _builtins
    _original_print = _builtins.print
    def _print_and_log(*args, **kwargs):
        sep = kwargs.get('sep', ' ')
        try:
            msg = sep.join(str(a) for a in args)
        except Exception:
            msg = sep.join(repr(a) for a in args)
        # 先原样打印到控制台
        _original_print(*args, **kwargs)
        # 再写入日志（info 级别）
        try:
            logger.info(msg)
        except Exception:
            pass
    _builtins.print = _print_and_log
except Exception:
    pass


def setup_network_monitoring(driver):
    """启用网络监控，捕获所有请求、响应和 WebSocket 连接"""
    global network_logs, websocket_logs

    try:
        # 启用网络域
        driver.execute_cdp_cmd('Network.enable', {})

        # 启用运行时域（用于获取响应体）
        driver.execute_cdp_cmd('Runtime.enable', {})

        # 启用页面域（用于 WebSocket 监控）
        driver.execute_cdp_cmd('Page.enable', {})

        print("网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...")

    except Exception as e:
        print(f"启用网络监控失败: {e}")

def process_websocket_event(driver, event_type, params):
    """处理 WebSocket 事件并添加到日志中"""
    global websocket_logs

    try:
        if event_type == 'Network.webSocketCreated':
            # WebSocket 连接创建
            request_id = params['requestId']
            url = params['url']
            initiator = params.get('initiator', {})

            ws_entry = {
                "requestId": request_id,
                "url": url,
                "initiator": initiator,
                "createdDateTime": datetime.now().isoformat() + "Z",
                "frames": [],
                "status": "created"
            }

            websocket_logs.append(ws_entry)
            print(f"WebSocket 连接创建: {url}")

        elif event_type == 'Network.webSocketHandshakeResponseReceived':
            # WebSocket 握手响应
            request_id = params['requestId']
            response = params['response']
            timestamp = params['timestamp']

            # 查找对应的 WebSocket 连接
            for ws_entry in websocket_logs:
                if ws_entry.get('requestId') == request_id:
                    ws_entry['handshakeResponse'] = {
                        "status": response['status'],
                        "statusText": response['statusText'],
                        "headers": response.get('headers', {}),
                        "timestamp": timestamp
                    }
                    ws_entry['status'] = "handshake_completed"
                    print(f"WebSocket 握手完成: {ws_entry['url']}")
                    break

        elif event_type == 'Network.webSocketFrameReceived':
            # WebSocket 接收帧
            request_id = params['requestId']
            timestamp = params['timestamp']
            response = params['response']

            frame_data = {
                "type": "received",
                "timestamp": timestamp,
                "opcode": response.get('opcode', 1),
                "mask": response.get('mask', False),
                "payloadData": response.get('payloadData', ''),
                "payloadLength": len(response.get('payloadData', ''))
            }

            # 查找对应的 WebSocket 连接并添加帧
            for ws_entry in websocket_logs:
                if ws_entry.get('requestId') == request_id:
                    ws_entry['frames'].append(frame_data)
                    break

        elif event_type == 'Network.webSocketFrameSent':
            # WebSocket 发送帧
            request_id = params['requestId']
            timestamp = params['timestamp']
            response = params['response']

            frame_data = {
                "type": "sent",
                "timestamp": timestamp,
                "opcode": response.get('opcode', 1),
                "mask": response.get('mask', True),
                "payloadData": response.get('payloadData', ''),
                "payloadLength": len(response.get('payloadData', ''))
            }

            # 查找对应的 WebSocket 连接并添加帧
            for ws_entry in websocket_logs:
                if ws_entry.get('requestId') == request_id:
                    ws_entry['frames'].append(frame_data)
                    break

        elif event_type == 'Network.webSocketClosed':
            # WebSocket 连接关闭
            request_id = params['requestId']
            timestamp = params['timestamp']

            # 查找对应的 WebSocket 连接并标记为关闭
            for ws_entry in websocket_logs:
                if ws_entry.get('requestId') == request_id:
                    ws_entry['closedDateTime'] = datetime.fromtimestamp(timestamp).isoformat() + "Z"
                    ws_entry['status'] = "closed"
                    print(f"WebSocket 连接关闭: {ws_entry['url']}")
                    break

    except Exception as e:
        print(f"处理 WebSocket 事件时出错: {e}")

def process_network_event(driver, event_type, params):
    """处理网络事件并添加到 HAR 数据中"""
    global har_data, network_logs

    try:
        if event_type == 'Network.requestWillBeSent':
            # 请求开始
            request_id = params['requestId']
            request = params['request']
            timestamp = params['timestamp']

            entry = {
                "startedDateTime": datetime.fromtimestamp(timestamp).isoformat() + "Z",
                "time": 0,  # 将在响应完成时计算
                "request": {
                    "method": request['method'],
                    "url": request['url'],
                    "httpVersion": "HTTP/1.1",
                    "headers": [{"name": k, "value": v} for k, v in request.get('headers', {}).items()],
                    "queryString": [],
                    "cookies": [],
                    "headersSize": -1,
                    "bodySize": len(request.get('postData', ''))
                },
                "response": {},
                "cache": {},
                "timings": {
                    "send": 0,
                    "wait": 0,
                    "receive": 0
                },
                "_requestId": request_id,
                "_startTime": timestamp
            }

            # 添加请求体
            if 'postData' in request:
                entry['request']['postData'] = {
                    "mimeType": request.get('headers', {}).get('content-type', 'text/plain'),
                    "text": request['postData']
                }

            # 存储到网络日志中
            network_logs.append(entry)

        elif event_type == 'Network.responseReceived':
            # 响应接收
            request_id = params['requestId']
            response = params['response']
            timestamp = params['timestamp']

            # 查找对应的请求
            for entry in network_logs:
                if entry.get('_requestId') == request_id:
                    entry['response'] = {
                        "status": response['status'],
                        "statusText": response['statusText'],
                        "httpVersion": response.get('protocol', 'HTTP/1.1'),
                        "headers": [{"name": k, "value": v} for k, v in response.get('headers', {}).items()],
                        "cookies": [],
                        "content": {
                            "size": response.get('encodedDataLength', 0),
                            "mimeType": response.get('mimeType', 'text/html')
                        },
                        "redirectURL": "",
                        "headersSize": -1,
                        "bodySize": response.get('encodedDataLength', 0)
                    }

                    # 计算时间
                    if '_startTime' in entry:
                        entry['time'] = (timestamp - entry['_startTime']) * 1000  # 转换为毫秒
                        entry['timings']['wait'] = entry['time']

                    break

        elif event_type == 'Network.loadingFinished':
            # 加载完成，尝试获取响应体
            request_id = params['requestId']

            try:
                # 获取响应体
                response_body = driver.execute_cdp_cmd('Network.getResponseBody', {'requestId': request_id})

                # 查找对应的请求并添加响应体
                for entry in network_logs:
                    if entry.get('_requestId') == request_id:
                        if 'response' in entry and 'content' in entry['response']:
                            entry['response']['content']['text'] = response_body.get('body', '')
                            if response_body.get('base64Encoded'):
                                entry['response']['content']['encoding'] = 'base64'
                        break

            except Exception as e:
                # 某些请求可能无法获取响应体，这是正常的
                pass

    except Exception as e:
        print(f"处理网络事件时出错: {e}")

def save_har_file(filename=None):
    """保存 HAR 文件，包含网络请求和 WebSocket 连接"""
    global har_data, network_logs, websocket_logs

    try:
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"network_capture_{timestamp}.har"

        # 确保 HAR 目录存在
        har_dir = "har_files"
        if not os.path.exists(har_dir):
            os.makedirs(har_dir)

        filepath = os.path.join(har_dir, filename)

        # 清理网络日志中的内部字段
        cleaned_entries = []
        for entry in network_logs:
            cleaned_entry = {k: v for k, v in entry.items() if not k.startswith('_')}
            cleaned_entries.append(cleaned_entry)

        # 处理 WebSocket 数据
        cleaned_websockets = []
        for ws_entry in websocket_logs:
            cleaned_ws = {k: v for k, v in ws_entry.items()}
            cleaned_websockets.append(cleaned_ws)

        # 更新 HAR 数据
        har_data['log']['entries'] = cleaned_entries
        har_data['log']['websockets'] = cleaned_websockets
        har_data['log']['pages'] = [{
            "startedDateTime": datetime.now().isoformat() + "Z",
            "id": "page_1",
            "title": "RoxySpider Capture",
            "pageTimings": {
                "onContentLoad": -1,
                "onLoad": -1
            }
        }]

        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(har_data, f, indent=2, ensure_ascii=False)

        print(f"HAR 文件已保存: {filepath}")
        print(f"捕获了 {len(cleaned_entries)} 个网络请求")
        print(f"捕获了 {len(cleaned_websockets)} 个 WebSocket 连接")

        # 显示 WebSocket 连接详情
        for ws in cleaned_websockets:
            frame_count = len(ws.get('frames', []))
            print(f"  WebSocket: {ws.get('url', 'Unknown')} - {frame_count} 个消息帧")

        return filepath

    except Exception as e:
        print(f"保存 HAR 文件时出错: {e}")
        return None

def save_websocket_summary(filename=None):
    """单独保存 WebSocket 连接摘要"""
    global websocket_logs

    try:
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"websocket_summary_{timestamp}.json"

        # 确保目录存在
        ws_dir = "websocket_logs"
        if not os.path.exists(ws_dir):
            os.makedirs(ws_dir)

        filepath = os.path.join(ws_dir, filename)

        # 创建 WebSocket 摘要
        summary = {
            "captureTime": datetime.now().isoformat() + "Z",
            "totalConnections": len(websocket_logs),
            "connections": []
        }

        for ws_entry in websocket_logs:
            frames = ws_entry.get('frames', [])
            sent_frames = [f for f in frames if f.get('type') == 'sent']
            received_frames = [f for f in frames if f.get('type') == 'received']

            connection_summary = {
                "url": ws_entry.get('url', ''),
                "status": ws_entry.get('status', ''),
                "createdDateTime": ws_entry.get('createdDateTime', ''),
                "closedDateTime": ws_entry.get('closedDateTime', ''),
                "totalFrames": len(frames),
                "sentFrames": len(sent_frames),
                "receivedFrames": len(received_frames),
                "handshakeResponse": ws_entry.get('handshakeResponse', {}),
                "frames": frames  # 包含完整的帧数据
            }

            summary["connections"].append(connection_summary)

        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"WebSocket 摘要已保存: {filepath}")
        return filepath

    except Exception as e:
        print(f"保存 WebSocket 摘要时出错: {e}")
        return None

def setup_cdp(driver):
    """通过 CDP 做一些常见的反自动化指纹优化，并启用网络监控。"""
    try:
        # 首先启用网络监控
        setup_network_monitoring(driver)

        # 1) 在新文档注入脚本，隐藏 webdriver 等指纹
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': r'''
            Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            // 伪造常见属性
            Object.defineProperty(navigator, 'languages', { get: () => ['en-US','en'] });
            Object.defineProperty(navigator, 'platform', { get: () => 'Win32' });
            Object.defineProperty(navigator, 'plugins', { get: () => [1,2,3,4,5] });

            // 增强硬件指纹伪装
            Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => 8 });
            Object.defineProperty(navigator, 'deviceMemory', { get: () => 8 });
            Object.defineProperty(navigator, 'maxTouchPoints', { get: () => 0 });

            // 网络连接信息伪装
            if (navigator.connection) {
              Object.defineProperty(navigator.connection, 'downlink', { get: () => 10 });
              Object.defineProperty(navigator.connection, 'effectiveType', { get: () => '4g' });
              Object.defineProperty(navigator.connection, 'rtt', { get: () => 50 });
            }

            // 修补 permissions.query 以避免暴露为 "denied"
            const originalQuery = navigator.permissions && navigator.permissions.query;
            if (originalQuery) {
              navigator.permissions.query = (parameters) => (
                parameters && parameters.name === 'notifications'
                  ? Promise.resolve({ state: Notification.permission })
                  : originalQuery(parameters)
              );
            }

            // 轻度伪造 WebGL Vendor/Renderer
            const getParameter = WebGLRenderingContext && WebGLRenderingContext.prototype.getParameter;
            if (getParameter) {
              WebGLRenderingContext.prototype.getParameter = function(parameter){
                if (parameter === 37445) { return 'Intel Inc.'; }
                if (parameter === 37446) { return 'Intel Iris OpenGL Engine'; }
                return getParameter.call(this, parameter);
              }
            }

            // Canvas 指纹轻微噪声
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function(...args) {
              const ctx = this.getContext('2d');
              if (ctx) {
                const imageData = ctx.getImageData(0, 0, this.width, this.height);
                for (let i = 0; i < imageData.data.length; i += 4) {
                  if (Math.random() < 0.001) {
                    imageData.data[i] = Math.min(255, imageData.data[i] + Math.floor(Math.random() * 3) - 1);
                  }
                }
                ctx.putImageData(imageData, 0, 0);
              }
              return originalToDataURL.apply(this, args);
            };

            // AudioContext 指纹轻微噪声
            if (window.AudioContext || window.webkitAudioContext) {
              const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
              const originalCreateAnalyser = OriginalAudioContext.prototype.createAnalyser;
              OriginalAudioContext.prototype.createAnalyser = function() {
                const analyser = originalCreateAnalyser.call(this);
                const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                analyser.getFloatFrequencyData = function(array) {
                  originalGetFloatFrequencyData.call(this, array);
                  for (let i = 0; i < array.length; i++) {
                    if (Math.random() < 0.001) {
                      array[i] += (Math.random() - 0.5) * 0.0001;
                    }
                  }
                };
                return analyser;
              };
            }
            '''
        })

        # 2) 网络与 UA、语言等（Network.enable 已在监控中启用）
        ua = driver.execute_script('return navigator.userAgent') or ''
        if 'Headless' in ua:
            ua = ua.replace('Headless', '').strip()
        driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            'userAgent': ua,
            'platform': 'Windows'
        })
        driver.execute_cdp_cmd('Network.setExtraHTTPHeaders', {
            'headers': {
                'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7'
            }
        })

        # 3) 时区与 CSP
        try:
            driver.execute_cdp_cmd('Emulation.setTimezoneOverride', {'timezoneId': 'America/Los_Angeles'})
        except Exception:
            pass
        try:
            driver.execute_cdp_cmd('Page.setBypassCSP', {'enabled': True})
        except Exception:
            pass

    except Exception as e:
        print(f"CDP 初始化失败: {e}")




def simulate_human_behavior(driver):
    """模拟人类行为：轻微滚动、随机停顿、页面交互"""
    try:
        # 随机轻微滚动（模拟用户查看页面）
        try:
            scroll_amount = random.randint(-150, 150)
            driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            print(f"已执行拟人滚动: {scroll_amount}px")
        except Exception as scroll_error:
            print(f"滚动操作跳过: {scroll_error}")

        # 短暂停顿
        pause_time = random.uniform(0.4, 1.0)
        time.sleep(pause_time)

        # 模拟页面焦点变化（更安全的交互方式）
        try:
            # 随机点击页面空白区域或body（不移动鼠标）
            if random.choice([True, False]):
                driver.execute_script("document.body.focus();")
                print("已执行页面焦点操作")
        except Exception as focus_error:
            print(f"焦点操作跳过: {focus_error}")

        # 再次随机停顿
        final_pause = random.uniform(0.2, 0.6)
        time.sleep(final_pause)
        print(f"已执行拟人操作完成，总停顿: {pause_time + final_pause:.2f}秒")

    except Exception as e:
        print(f"拟人操作部分失败，继续执行: {e}")
        # 即使失败也要有基本的停顿
        time.sleep(random.uniform(0.3, 0.7))
def cdp_block_network_temporarily(driver, seconds: int = 10):
    """使用 CDP 临时阻塞网络一段时间（默认 10 秒），然后自动恢复。
    如果 Network 还未启用，会先执行 Network.enable。
    增加拟人操作以降低检测风险。
    """
    try:
        # 网络阻塞前的拟人操作
        print("执行网络阻塞前的拟人操作...")
        simulate_human_behavior(driver)

        # 确保已启用 Network 域
        try:
            driver.execute_cdp_cmd('Network.enable', {})
        except Exception:
            pass

        # 添加随机抖动到阻塞时间
        actual_seconds = seconds + random.uniform(-1, 2)

        # 阻塞网络（离线）
        driver.execute_cdp_cmd('Network.emulateNetworkConditions', {
            'offline': True,
            'latency': 0,
            'downloadThroughput': 0,
            'uploadThroughput': 0
        })
        print(f"CDP: 已将网络设为离线，保持 {actual_seconds:.1f} 秒……")
        time.sleep(actual_seconds)
    finally:
        # 恢复网络（在线），给出一个合理的吞吐以避免继续被限速
        try:
            driver.execute_cdp_cmd('Network.emulateNetworkConditions', {
                'offline': False,
                'latency': 0,
                # 约 5MB/s（单位：bytes/s）
                'downloadThroughput': 5 * 1024 * 1024,
                'uploadThroughput': 5 * 1024 * 1024
            })
            # 恢复后的拟人操作（不刷新页面，避免过于明显）
            time.sleep(random.uniform(0.8, 1.5))  # 随机停顿

            # 网络恢复后的拟人操作
            print("执行网络恢复后的拟人操作...")
            simulate_human_behavior(driver)

        except Exception as e:
            print(f"CDP: 恢复网络失败: {e}")
        else:
            print("CDP: 网络已恢复为在线状态。")

def cdp_throttle_network_temporarily(driver, seconds: int = 10, latency_ms: int = 8000):
    """使用 CDP 临时节流网络（高延迟+低带宽），然后恢复。
    比完全离线更温和，适合首次触发。
    """
    try:
        # 节流前的拟人操作
        print("执行网络节流前的拟人操作...")
        simulate_human_behavior(driver)

        # 确保已启用 Network 域
        try:
            driver.execute_cdp_cmd('Network.enable', {})
        except Exception:
            pass

        # 添加随机抖动
        actual_seconds = seconds + random.uniform(-1, 2)
        actual_latency = latency_ms + random.randint(-1000, 2000)

        # 节流网络（高延迟+低带宽）
        driver.execute_cdp_cmd('Network.emulateNetworkConditions', {
            'offline': False,
            'latency': actual_latency,
            'downloadThroughput': 64 * 1024,  # 64KB/s
            'uploadThroughput': 32 * 1024,    # 32KB/s
            'connectionType': 'cellular3g'
        })
        print(f"CDP: 已将网络设为节流模式（延迟{actual_latency}ms），保持 {actual_seconds:.1f} 秒……")
        time.sleep(actual_seconds)
    finally:
        # 恢复网络
        try:
            driver.execute_cdp_cmd('Network.emulateNetworkConditions', {
                'offline': False,
                'latency': 0,
                'downloadThroughput': 5 * 1024 * 1024,
                'uploadThroughput': 5 * 1024 * 1024
            })

            # 恢复后的拟人操作
            time.sleep(random.uniform(0.5, 1.2))
            print("执行网络恢复后的拟人操作...")
            simulate_human_behavior(driver)

        except Exception as e:
            print(f"CDP: 恢复网络失败: {e}")
        else:
            print("CDP: 网络节流已恢复为正常状态。")

# 全局计数器，用于跟踪触发次数
_human_verification_count = 0

def setup_driver():
    """初始化并返回一个 Chrome WebDriver 实例，启用网络监控。"""
    # 这里可以添加更多的 WebDriver 配置，例如无头模式等
    # options = webdriver.ChromeOptions()
    # options.add_argument('--headless')
    # driver = webdriver.Chrome(options=options)
    brwoser_id = "1c42949de80fb5f609dc1fcee8fddde3"
    # 初始化客户端
    client = RoxyClient(port=50000,token="76b402c778930dd44bacc693cb2fb8d0")
    client.browser_local_cache([brwoser_id])
    client.browser_server_cache(22854,[brwoser_id])
    client.browser_random_env(22854,[brwoser_id])
    # 打开浏览器
    rsp = client.browser_open(brwoser_id)
    if rsp.get("code") != 0:
        print("浏览器打开失败:",rsp)
        exit(0)
    # 获取selenium的连接信息
    debuggerAddress = rsp.get("data").get("http")
    driverPath = rsp.get("data").get("driver")
    print(f"浏览器打开成功,debuggerAddress:{debuggerAddress},driverPath:{driverPath}")

    # selenium 连接代码 - 使用最简化的配置
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_experimental_option("debuggerAddress", debuggerAddress)

    chrome_service = Service(driverPath)

    try:
        # 使用简化选项创建 driver，不启用性能日志
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
        print("WebDriver 创建成功（简化模式）")
    except Exception as e:
        print(f"WebDriver 创建失败: {e}")
        raise e

    driver.implicitly_wait(10) # 设置一个隐式等待

    # 应用 CDP 调整和网络监控
    setup_cdp(driver)

    return driver


def get_captcha(url,retry=0):
    baseurl = "http://************:5000" #"http://************:5000"
    def _getcap_value(id):
        cap_value = s.get(baseurl + "/result",params={"id":id})
        try:
            if cap_value.text != "CAPTCHA_NOT_READY":
                return cap_value.json()["value"]
            else:
                time.sleep(5)
                #print(f"{get_current_method_name()} --获取 cap_value 失败，重试...")
                return _getcap_value(id)
        except Exception as e:
            #print(f"{get_current_method_name()}发生错误: ====== {cap_value.text}")
            return _getcap_value(id)


    task_id = s.get(baseurl + "/turnstile",params={"url":url,"sitekey":"0x4AAAAAAAQFNSW6xordsuIq"}).json()["task_id"]

    if task_id:
        cap_value = _getcap_value(task_id)
        # print(cap_value)
        if cap_value != "CAPTCHA_FAIL":
            print(f"获取 cap_value 验证码成功...")
            return cap_value
        else:
            print(f"获取验证码失败，重试次数: {retry+1}")
            return get_captcha(url,retry+1)




def get_email():
    #SWITCH_EMAIL = rando你是什么模型你是m.choice(list(ALLDOMAIN.keys()))
    baseurl = "https://es.slogo.eu.org"
    #s.proxies = {"http": "http://127.0.0.1:10808", "https": "http://127.0.0.1:10808"}
    def _generate_random_name():
        # 生成5位英文字符
        letters1 = ''.join(random.choices(string.ascii_lowercase, k=5))
        # 生成1-3个数字
        numbers = ''.join(random.choices(string.digits, k=random.randint(1, 3)))
        # 生成1-3个英文字符
        letters2 = ''.join(random.choices(string.ascii_lowercase, k=random.randint(1, 3)))
        # 组合成最终名称
        return letters1 + str(int(time.time()))

    def _fetch_email_data(name):
        try:
            res = requests.post(
                baseurl+"/admin/new_address",
                json={
                    "enablePrefix": True,
                    "name": name,
                    "domain": DOMAIN
                    #"domain": "smartdocker.online",
                },
                headers={
                    'x-admin-auth': "chen1234.",
                    "Content-Type": "application/json"
                },
                proxies={"http": "http://***********:10808", "https": "http://***********:10808"}
            )
            if res.status_code == 200:
                response_data = res.json()
                email = response_data.get("address", 0)
                jwt = response_data.get("jwt", 0)
                return email,jwt
            else:
                print(f"请求失败，状态码: {res.status_code}")
                return None
        except requests.RequestException as e:
            print(f"请求出现错误: {e}")
            return None
    return _fetch_email_data(_generate_random_name())

def is_time_within_range(email_date_str, target_date_str, minutes=1):
    """
    检查邮件日期是否在目标时间的指定分钟范围内

    参数:
        email_date_str: 邮件中的日期字符串，格式如 "Thu, 3 Jul 2025 04:10:52 +0000"
        target_date_str: 目标日期字符串，相同格式
        minutes: 允许的时间差（分钟）

    返回:
        布尔值，表示是否在时间范围内
    """
    from email.utils import parsedate_to_datetime
    from datetime import datetime, timedelta

    try:
        # 解析邮件日期字符串为datetime对象
        email_date = parsedate_to_datetime(email_date_str)

        # 解析目标日期字符串为datetime对象
        target_date = parsedate_to_datetime(target_date_str)

        # 计算时间差（绝对值）
        time_diff = abs((email_date - target_date).total_seconds())

        # 检查时间差是否在允许范围内（转换为秒）
        return time_diff <= (minutes * 60)
    except Exception as e:
        print(f"日期比较出错: {e}")
        return False

def extract_email_elements(email_message_str):
    code = None
    # 将字符串转换为字节类型（如果原始字符串不是字节类型的话）
    email_message_bytes = email_message_str.encode('utf-8')

    # 使用BytesParser解析字节字符串为Message对象
    msg = email.message_from_bytes(email_message_bytes)

    # 访问邮件的各个部分
    if "<EMAIL>" in msg['From']:


        # 检查邮件日期是否在当前时间附近1分钟内
        target_time = formatdate(localtime=False, usegmt=True)  # 获取当前时间，格式如 "Thu, 3 Jul 2025 04:10:52 GMT"
        if is_time_within_range(msg['Date'], target_time, minutes=2):
            # 访问邮件正文
            if msg.is_multipart():
                for part in msg.walk():
                    # 每个part都是一个Message对象，我们可以检查其内容类型
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))

                    # 打印文本内容
                    if content_type == "text/plain" and "attachment" not in content_disposition:
                        body = part.get_payload(decode=True).decode()  # 解码并获取文本内容
                        match = re.search(r'Your verification code is: (\d{6})', body)
                        if match:
                            code = match.group(1)
        else:
            pass
            print(f'{msg['Date']} 时间不在最近 {target_time }')




    return code

def get_email_data(jwt):
    baseurl = "https://es.slogo.eu.org"
    code = None
    if jwt:

        res = s.get(
            baseurl+"/api/mails",
            params={"limit":10,"offset":0},
            proxies={"http": "http://***********:10808", "https": "http://***********:10808"},
            headers={
                "Authorization": f"Bearer {jwt}",
                # "x-custom-auth": "<你的网站密码>", # 如果启用了自定义密码
                "Content-Type": "application/json"
        })

        results = res.json().get("results", 0)
        if results:
            raw = results[0].get("raw", 0)
            #print(f"{get_current_method_name()} -- results -- success")
            code = extract_email_elements(raw)
        else:
            #print(f"{get_current_method_name()} -- no results")
            return get_email_data(jwt)

    return code


def get_augtoken(url,code_verifier):

    from urllib.parse import urlparse, parse_qs
    _query_str = urlparse(url).query  # 提取 ? 后面的部分
    query_str = _query_str.replace("&amp;", "&")
    # 解析参数并转为字典（重复 key 会变成数组）
    params = parse_qs(query_str)
    tenant_url = params.get("tenant_url",0)[0]
    code = params.get("code",0)[0]
    data = {}
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Augment.vscode-augment/0.482.1 (win32; x64; 10.0.19045) vscode/1.95.3",
        "x-request-id": str(uuid.uuid4()),
        "x-request-session-id": str(uuid.uuid4()),
        "x-api-version": "2"
    }
    body = {
        "grant_type": "authorization_code",
        "client_id": "augment-vscode-extension",
        "code_verifier": code_verifier,
        "redirect_uri": "vscode://augment.vscode-augment/auth/result",
        "code": code
    }
    res = s.post(tenant_url+"token",headers=headers,json=body).json()
    access_token = res.get("access_token", 0)
    if not access_token:
        return None
    data["accessToken"] = access_token
    headers["Authorization"] = f"Bearer {access_token}"
    res_info = s.post(tenant_url+"subscription-info",headers=headers,json={}).json()
    end_date = res_info.get("subscription",0).get("ActiveSubscription",0).get("end_date", 0)
    if not end_date:
        return None
    data["end_date"] = end_date
    data["tenantURL"] = tenant_url

    return data


def add_session(email,augmentSession:dict,expire_time="",other=""):
    data = {
            "email":email,
            "augmentSession":augmentSession,
            "expire_time":expire_time,
            "other":other
        }
    res =s.post("https://aug.202578.xyz/add_session",
                 headers={"X-User-ID": "admin"},
                 json=data
                 )
    if res.status_code == 200:
        print(f"添加session成功: {res.json()}")
    else:
        print(f"添加session失败: {res.text}")

def wait_for_page_load(driver, timeout=30):
    try:
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
    except TimeoutException:
        print(f"页面在{timeout}秒内未完全加载")


def human_like_typing(element, text):
    """模拟人类打字，在每个字符之间加入随机延迟。"""
    for char in text:
        element.send_keys(char)
        time.sleep(random.uniform(0.05, 0.15))

def human_like_mouse_move_and_click(driver, element):
    """
    NEW: 模拟人类的鼠标移动轨迹和点击。
    它会先移动到元素附近，停顿一下，再移动到元素中心并点击。
    """
    actions = ActionChains(driver)

    # 步骤 1: 移动到元素附近的一个随机位置
    offset_x = random.randint(-30, 30)
    offset_y = random.randint(-30, 30)
    actions.move_to_element_with_offset(element, offset_x, offset_y)
    actions.pause(random.uniform(0.2, 0.5))

    # 步骤 2: 精准移动到元素中心
    actions.move_to_element(element)
    actions.pause(random.uniform(0.1, 0.3))

    # 步骤 3: 点击
    actions.click()

    # 执行所有链式操作
    actions.perform()
    print(
        f"已模拟人类轨迹点击元素: {element.tag_name} (ID: {element.get_attribute('id')}, Class: {element.get_attribute('class')})")


def wait_for_human_verification(driver, timeout=60):
    """
    使用显式等待，持续检测页面是否包含“Verifying you are human”字符串。

    参数:
    driver (WebDriver): 正在使用的 Selenium WebDriver 实例。
    timeout (int): 最长等待时间（秒）。

    返回值:
    bool: 如果在超时时间内检测到字符串，则返回 True；否则返回 False。
    """
    print(f"正在等待页面出现 'Verifying you are human' 字符串，最长等待时间：{timeout} 秒...")

    try:
        # 定义一个预期条件：检查页面源代码是否包含特定字符串
        wait = WebDriverWait(driver, timeout)
        wait.until(lambda d: "Verifying you are human" in d.page_source)

        print("检测到 'Verifying you are human' 字符串！")
        return True

    except Exception as e:
        print(f"在 {timeout} 秒内未检测到 'Verifying you are human' 字符串。")
        return False


def bypass_human_verification(driver, duration=10):
    """
    通过在浏览器窗口内执行一系列强制鼠标操作来尝试绕过人类验证。
    这些操作不依赖于页面上的具体元素，因此不受元素可见性的影响。

    参数:
    driver (WebDriver): 正在使用的 Selenium WebDriver 实例。
    duration (int): 鼠标滑动操作的持续时间（秒）。
    """
    print("页面加载完成，等待 ReCAPTCHA 令牌生成...")

    try:
        # 立即尝试获取令牌，不进行额外的显式等待
        captcha_token = driver.find_element(By.ID, "g-recaptcha-response").get_attribute("value")

        if captcha_token:
            print("成功获取到 ReCAPTCHA 令牌:", captcha_token)
        else:
            print("令牌为空，可能页面已经跳转或加载失败。")

    except Exception as e:
        print("在验证页面上找不到 ReCAPTCHA 令牌，可能已跳转到下一个页面:", e)
        # 在这里可以添加逻辑来检查是否已经到达用户首页
        if "用户首页的某个URL或标题" in driver.current_url:
            print("已成功跳转到用户首页。")

    try:
        # 检查页面内容是否包含验证字符串
        captcha_token = driver.execute_script("return document.getElementById('g-recaptcha-response').value;")
        print("ReCAPTCHA Token:", captcha_token)
        if "Verifying you are human" in driver.page_source:
            print("检测到人类验证页面，开始执行强制鼠标操作...")

            actions = ActionChains(driver)

            # 获取窗口大小，以便在窗口范围内操作
            window_size = driver.get_window_size()
            width, height = window_size['width'], window_size['height']

            # 步骤1: 鼠标左键按下并在屏幕上无规律滑动
            start_x = random.randint(100, width - 100)
            start_y = random.randint(100, height - 100)

            actions.move_by_offset(start_x, start_y).click_and_hold().perform()

            end_time = time.time() + duration
            while time.time() < end_time:
                next_x = random.randint(50, width - 50)
                next_y = random.randint(50, height - 50)

                actions.move_by_offset(next_x - start_x, next_y - start_y).perform()
                start_x, start_y = next_x, next_y

                time.sleep(random.uniform(0.1, 0.5))

            print("鼠标左键按住并无规律滑动操作完成。")

            # 步骤2: 释放鼠标左键
            actions.release().perform()
            print("鼠标左键放开操作完成。")

            # 步骤3: 在页面上随机点击一次
            click_x = random.randint(50, width - 50)
            click_y = random.randint(50, height - 50)
            actions.move_by_offset(click_x - start_x, click_y - start_y).click().perform()
            print("鼠标左键点击页面操作完成。")

            # 步骤4: 再次执行鼠标左键按下
            final_press_x = random.randint(50, width - 50)
            final_press_y = random.randint(50, height - 50)
            actions.move_by_offset(final_press_x - click_x, final_press_y - click_y).click_and_hold().perform()
            print("再次执行鼠标左键按下操作完成。")

            # 等待验证结果
            time.sleep(3)
            if "Verifying you are human" not in driver.page_source:
                print("验证成功，已进入目标页面。")
                return True
            else:
                print("验证失败，请手动检查。")
                return False

        return False  # 如果没有检测到验证页面，直接返回 False

    except Exception as e:
        print(f"执行鼠标操作时发生错误: {e}")
        return False


def bypass_human_verification_force(driver, duration=10):
    """
    通过在浏览器窗口内执行一系列强制鼠标操作，尝试绕过人类验证。
    这些操作不依赖于页面元素，可以避免元素不可交互的错误。

    参数:
    driver (WebDriver): 正在使用的 Selenium WebDriver 实例。
    duration (int): 鼠标滑动操作的持续时间（秒）。

    返回值:
    bool: 如果在操作后验证通过，则返回 True；否则返回 False。
    """
    try:
        # 检查页面内容是否包含验证字符串
        if "Verifying you are human" in driver.page_source:
            print("检测到人类验证页面，开始执行强制鼠标操作...")

            # 初始化 ActionChains
            actions = ActionChains(driver)

            # 获取窗口大小，以便在窗口范围内操作
            window_size = driver.get_window_size()
            width, height = window_size['width'], window_size['height']

            # 步骤1: 鼠标左键按下并在屏幕上无规律滑动
            start_x = random.randint(100, width - 100)
            start_y = random.randint(100, height - 100)

            # 使用 move_by_offset 从当前位置移动到随机起始点，并按下左键
            actions.move_by_offset(start_x, start_y).click_and_hold().perform()

            print("鼠标左键已按下，开始随机滑动...")
            end_time = time.time() + duration
            while time.time() < end_time:
                # 随机生成下一个移动位置
                next_x = random.randint(50, width - 50)
                next_y = random.randint(50, height - 50)

                # 移动鼠标到新位置
                actions.move_by_offset(next_x - start_x, next_y - start_y).perform()
                start_x, start_y = next_x, next_y

                # 每次移动后随机暂停，模拟真实用户行为
                time.sleep(random.uniform(0.1, 0.5))

            print("鼠标无规律滑动操作完成。")

            # 步骤2: 释放鼠标左键
            actions.release().perform()
            print("鼠标左键已释放。")

            # 步骤3: 在页面上随机点击一次
            click_x = random.randint(50, width - 50)
            click_y = random.randint(50, height - 50)
            # 移动到新的随机位置并点击
            actions.move_by_offset(click_x - start_x, click_y - start_y).click().perform()
            print("鼠标左键在页面上随机点击一次。")

            # 步骤4: 再次执行鼠标左键按下
            final_press_x = random.randint(50, width - 50)
            final_press_y = random.randint(50, height - 50)
            actions.move_by_offset(final_press_x - click_x, final_press_y - click_y).click_and_hold().perform()
            print("再次执行鼠标左键按下操作完成。")

            # 等待几秒钟，观察验证是否通过
            time.sleep(3)
            if "Verifying you are human" not in driver.page_source:
                print("验证成功，已进入目标页面。")
                return True
            else:
                print("验证失败，请手动检查。")
                return False

        print("未检测到验证页面，跳过操作。")
        return False

    except Exception as e:
        print(f"执行鼠标操作时发生错误: {e}")
        return False


def bypass_human_verification_drag(driver, duration=10):
    """
    鼠标左键按住不松开，在浏览器窗口内持续随机拖动特定时长。
    这个操作不依赖于页面元素，可以避免元素不可交互的错误。

    参数:
    driver (WebDriver): 正在使用的 Selenium WebDriver 实例。
    duration (int): 鼠标拖动操作的持续时间（秒）。

    返回值:
    bool: 如果在操作后验证通过，则返回 True；否则返回 False。
    """
    try:
        if "Verifying you are human" in driver.page_source:
            print(f"检测到人类验证页面，开始执行鼠标左键按住并随机拖动 {duration} 秒...")

            actions = ActionChains(driver)

            # 获取窗口大小，以便在窗口范围内操作
            window_size = driver.get_window_size()
            width, height = window_size['width'], window_size['height']

            # 随机选择一个起始点，按下鼠标左键
            start_x = random.randint(100, width - 100)
            start_y = random.randint(100, height - 100)

            actions.move_by_offset(start_x, start_y).click_and_hold().perform()

            end_time = time.time() + duration
            while time.time() < end_time:
                # 随机生成下一个移动的偏移量，而不是绝对位置
                offset_x = random.randint(-50, 50)
                offset_y = random.randint(-50, 50)

                actions.move_by_offset(offset_x, offset_y).perform()

                # 每次移动后随机暂停，模拟真实用户行为
                time.sleep(random.uniform(0.1, 0.3))

            # 释放鼠标左键
            actions.release().perform()
            print("鼠标左键按住并随机拖动操作完成。")

            # 等待几秒钟，观察验证是否通过
            time.sleep(3)
            if "Verifying you are human" not in driver.page_source:
                print("验证成功，已进入目标页面。")
                return True
            else:
                print("验证失败，请手动检查。")
                return False

        print("未检测到验证页面，跳过操作。")
        return False

    except Exception as e:
        print(f"执行鼠标拖动操作时发生错误: {e}")
        return False
def login(driver, username):
    """
    执行登录步骤。
    1. 打开登录页面
    2. 输入用户名
    3. 点击登录按钮
    """
    print("步骤 1 & 2: 正在打开登录页面并输入用户名...")
    driver.get("https://app.augmentcode.com/account")
    try:
        # 等待用户名输入框加载完成并输入
        username_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "username"))
        )
        capvalue = get_captcha(driver.current_url)
        captchainbpx = driver.find_element(By.NAME, 'captcha')
        username_input.clear()
        human_like_typing(username_input,username)
        #username_input.send_keys(username)

        # 找到并点击登录按钮
        login_button = driver.find_element(By.NAME, "action")


        if captchainbpx and login_button:
            print(f"找到 Turnstile...")
            driver.execute_script("arguments[0].value = '{}';".format(capvalue), captchainbpx)
            login_button.click()
            print("登录信息已提交，等待验证页面...")
            return True
        return False
    except TimeoutException:
        print("错误：登录页面加载超时或找不到登录元素。")
        return False

def verify_email(driver, email_code):
    """
    执行邮箱验证步骤。
    1. 输入验证码
    2. 点击验证按钮
    """
    print("步骤 3 & 4: 正在输入邮箱验证码...")
    try:
        # 等待验证码输入框加载完成并输入
        code_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "code"))
        )
        code_input.clear()
        code_input.send_keys(email_code)

        # 找到并点击验证按钮
        verify_button = driver.find_element(By.NAME, "action")
        driver.execute_cdp_cmd('Network.emulateNetworkConditions', {
            'offline': False,
            'latency': 10,
            'downloadThroughput': 64 * 1024,  # 64KB/s
            'uploadThroughput': 32 * 1024,  # 32KB/s
            'connectionType': 'cellular3g'
        })
        print("CDP:启用节流模式")
        verify_button.click()
        print("验证码已提交，等待跳转...")

        return True
    except TimeoutException:
        print("错误：验证页面加载超时或找不到验证元素。")
        return False


def bypass_human_verification_with_api(driver, timeout=10):
    """
    检测人类验证页面，并使用外部 API 绕过 ReCAPTCHA。

    参数:
    driver (WebDriver): 正在使用的 Selenium WebDriver 实例。
    timeout (int): 最长等待时间（秒）。

    返回值:
    bool: 如果成功注入代码，则返回 True；否则返回 False。
    """
    from tool import createtask,getTaskResult
    try:
        print("开始检测人类验证页面...")
        # 显式等待，检测页面是否出现“Verifying you are human”
        # wait = WebDriverWait(driver, timeout)
        # wait.until(lambda d: "Verifying you are human" in d.page_source)

        print("检测到人类验证页面，开始获取 ReCAPTCHA 密钥和 URL。")

        # 1. 获取当前页面的 URL
        current_url = driver.current_url

        # 3. 调用你的 ReCAPTCHA 解决方案服务
        task_id = createtask(current_url)
        print(task_id)
        if not task_id:
            print("创建任务失败。")
            return False

        # 4. 轮询获取 ReCAPTCHA 结果
        recaptcha_code = getTaskResult(task_id)
        if not recaptcha_code:
            print("获取验证码失败。")
            return False

        print("成功获取 ReCAPTCHA 令牌。")

        # 5. 找到 g-recaptcha-response 元素
        recaptcha_response_element = driver.find_element(By.ID, 'g-recaptcha-response')

        # 6. 使用 JavaScript 将获取的令牌值注入到元素中
        driver.execute_script(f'arguments[0].value = "{recaptcha_code}";', recaptcha_response_element)
        print("ReCAPTCHA 令牌已成功注入页面。")
        # 7. 可能会有提交表单的逻辑，你需要在此处添加
        # 例如：driver.find_element(By.ID, 'submit-button').click()

        return True

    except TimeoutException:
        print(f"在 {timeout} 秒内未检测到验证页面。")
        return False
    except NoSuchElementException as e:
        print(f"无法找到关键元素: {e}")
        return False
    except Exception as e:
        print(f"执行绕过操作时发生错误: {e}")
        return False




def attempt_signup_with_retry(driver):
    """
    执行注册的最后步骤，并包含重试逻辑。
    - 勾选复选框
    - 点击注册按钮
    - 如果失败，则根据错误信息进行重试或返回错误状态
    """
    max_retries = 5
    final_url = "https://app.augmentcode.com/account/subscription"
    for attempt in range(max_retries):
        print(f"\n第 {attempt + 1}/{max_retries} 次尝试注册...")
        try:
            # 步骤 5: 查找并勾选复选框，然后点击注册按钮
            print("步骤 5: 正在勾选复选框并点击注册按钮...")

            if driver.current_url == final_url:
                print("成功！当前在订阅页面。")
                return "SUCCESS"

            # 等待页面跳转后的结果
            time.sleep(3) # 等待一下，让页面有时间响应

            # 捕获注册尝试后的网络日志
            enhanced_network_capture(driver, "注册尝试后")

            # 步骤 6: 检查是否出现 "Sign-up rejected"
            try:
                rejection_element = driver.find_element(By.XPATH, "/html/body/div/div/div/div")
                if "Sign-up rejected" in rejection_element.text:
                    print(f"检测到 'Sign-up rejected'。等待30秒后重试。")
                    # 捕获拒绝情况下的网络日志
                    periodic_network_capture(driver)
                    time.sleep(30)
                    return "RESTART"

                # 步骤 7: 检查是否出现 "Oops!" 错误
                if "Oops!, something went wrong" in driver.page_source:
                    print("检测到 'Oops!, something went wrong' 错误。")
                    return "RESTART" # 返回一个特殊信号，表示需要从头开始

                # 随机刷新


                driver.refresh()
                sleep_time = random.uniform(3, 5)
                print(f"   等待 {sleep_time:.2f} 秒...")
                time.sleep(random.uniform(7, 10))

                print("刷新完成，继续下一次尝试。")
                continue # 继续 for 循环的下一次迭代

            except NoSuchElementException:
                # 如果找不到拒绝元素，说明可能成功了，跳出循环进行URL检查
                print("未检测到 'Sign-up rejected'。")
                pass

            # 步骤 8: 检查最终的 URL
            print("步骤 8: 检查当前 URL 是否为订阅页面...")

            # 等待URL变为目标URL，最多等待10秒
            WebDriverWait(driver, 10).until(EC.url_to_be(final_url))

            if driver.current_url == final_url:
                print("成功！已跳转到订阅页面。")
                return "SUCCESS"
            else:
                # 如果URL不匹配，但也没有拒绝信息，可能是一个未知的状态
                print(f"警告：当前 URL 为 {driver.current_url}，与预期不符。")
                # 这种情况也视为一次失败的尝试
                driver.back() # 尝试回退以进行下一次重试
                WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.ID, "signup-button"))
                )


        except TimeoutException as e:
            print(f"在尝试注册时发生超时错误: {e}")
            logger.error(f"在尝试注册时发生超时错误: {e}")
            #logger.error(driver.page_source)
            # 超时也可能意味着需要重试，先回退
            return "RESTART"
        except Exception as e:
            print(f"在尝试注册时发生未知错误: {e}")
            logger.error(f"在尝试注册时发生未知错误: {e}")
            #logger.error(driver.page_source)
            return "RESTART" # 发生其他严重错误时，最好从头开始

    print("已达到最大重试次数，注册失败。")
    return "FAILURE"

def get_vscode(driver,email):
    data = get_codeinfo()
    print(f"代码信息: {json.dumps(data, indent=2)}")
    print("=" * 50)
    if data:
        params = {
            "response_type": "code",
            "code_challenge": data["code_challenge"],
            "code_challenge_method": "S256",
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult",
            "state": data["state"],
            "scope": "email",
            "prompt": "login"
        }
        query_str = "&".join([f"{k}={v}" for k, v in params.items()])
        driver.get("https://auth.augmentcode.com/terms-accept" + f"?{query_str}")
        #cookie = tab.cookies().as_dict().get("session", "")
        cookie = driver.get_cookie("session").get("value","")
        res = driver.page_source
        # res = s.get(url="https://auth.augmentcode.com/terms-accept",params=params,cookies=cookie).text
        if "vscode://" in res:
            match = re.search(r'href="(vscode://[^"]+)"', res)
            if match:
                decoded_url = html.unescape(match.group(1))
                print("Extracted URL:", decoded_url)
                tokeninfo = get_augtoken(decoded_url, data["codeVerifier"])
                if tokeninfo:
                    accessToken = tokeninfo.get("accessToken", 0)
                    tenantURL = tokeninfo.get("tenantURL", 0)
                    end_date = tokeninfo.get("end_date", 0)
                    try:
                        ###smartdocker.online 565847.cfd
                        add_session(email.replace(DOMAIN,"smail.com"),{
                            "accessToken":accessToken,
                            "tenantURL":tenantURL,
                            "scopes":["email"]
                            },expire_time=end_date,other=cookie)
                        print(f"添加session: {email}   {accessToken}   {tenantURL}  {end_date}")
                        print(f"email:{email} === cookie:{cookie}")
                    except Exception as e:
                        print("添加会话出错")
                        logging.info(accessToken,tenantURL)
                        print({
                            "accessToken":accessToken,
                            "tenantURL":tenantURL,
                            "scopes":["email"]},expire_time=end_date,other=cookie)




def start_network_capture(driver):
    """开始捕获网络事件（基础模式）"""
    global network_logs, websocket_logs

    # 清空之前的日志
    network_logs = []
    websocket_logs = []

    try:
        print("启动网络监控（基础模式 - 不依赖性能日志）")

        # 立即捕获当前页面状态
        capture_network_via_cdp(driver)
        detect_websockets_in_page(driver)

        print("网络事件监听器已启动（基础模式）")
    except Exception as e:
        print(f"启动网络捕获失败: {e}")

def enhanced_network_capture(driver, action_description=""):
    """增强的网络捕获，在关键操作前后调用"""
    try:
        if action_description:
            print(f"网络捕获: {action_description}")

        # 捕获当前网络状态
        capture_network_via_cdp(driver)
        detect_websockets_in_page(driver)

        # 检查页面中的 AJAX 请求
        try:
            ajax_info = driver.execute_script("""
                var ajaxInfo = {
                    xhrCount: 0,
                    fetchCount: 0,
                    activeRequests: []
                };

                // 检查是否有活跃的 XMLHttpRequest
                if (window.XMLHttpRequest) {
                    // 这里只能检测到我们能访问的信息
                    ajaxInfo.hasXHR = true;
                }

                // 检查是否有 fetch API
                if (window.fetch) {
                    ajaxInfo.hasFetch = true;
                }

                // 检查页面中是否有 AJAX 相关的脚本
                var scripts = document.getElementsByTagName('script');
                for (var i = 0; i < scripts.length; i++) {
                    var script = scripts[i];
                    if (script.textContent &&
                        (script.textContent.includes('XMLHttpRequest') ||
                         script.textContent.includes('fetch(') ||
                         script.textContent.includes('$.ajax') ||
                         script.textContent.includes('axios'))) {
                        ajaxInfo.hasAjaxCode = true;
                        break;
                    }
                }

                return ajaxInfo;
            """)

            if ajax_info.get('hasAjaxCode'):
                print("检测到页面包含 AJAX 代码")

        except Exception as ajax_error:
            print(f"AJAX 检测失败: {ajax_error}")

    except Exception as e:
        print(f"增强网络捕获失败: {e}")

def capture_network_logs(driver):
    """使用 CDP 直接捕获网络日志，不依赖性能日志"""
    global network_logs, websocket_logs

    try:
        # 直接使用 CDP 捕获，不再尝试性能日志
        capture_network_via_cdp(driver)

        # 检测页面中的 WebSocket 连接
        detect_websockets_in_page(driver)

    except Exception as e:
        print(f"捕获网络日志时出错: {e}")

def detect_websockets_in_page(driver):
    """检测页面中的 WebSocket 连接"""
    try:
        # 通过 JavaScript 检测 WebSocket 连接
        ws_info = driver.execute_script("""
            var wsInfo = {
                connections: [],
                scripts: []
            };

            // 检查全局 WebSocket 对象
            if (window.WebSocket) {
                wsInfo.hasWebSocket = true;
            }

            // 检查页面源码中的 WebSocket URL
            var pageContent = document.documentElement.outerHTML;
            var wsMatches = pageContent.match(/wss?:\\/\\/[^\\s"']+/g);
            if (wsMatches) {
                wsInfo.connections = wsMatches;
            }

            // 检查脚本标签中的 WebSocket 相关内容
            var scripts = document.getElementsByTagName('script');
            for (var i = 0; i < scripts.length; i++) {
                var script = scripts[i];
                if (script.src && (script.src.includes('ws://') || script.src.includes('wss://'))) {
                    wsInfo.scripts.push(script.src);
                } else if (script.textContent &&
                          (script.textContent.includes('WebSocket') ||
                           script.textContent.includes('ws://') ||
                           script.textContent.includes('wss://'))) {
                    // 提取脚本中的 WebSocket URL
                    var scriptWsMatches = script.textContent.match(/wss?:\\/\\/[^\\s"']+/g);
                    if (scriptWsMatches) {
                        wsInfo.connections = wsInfo.connections.concat(scriptWsMatches);
                    }
                }
            }

            return wsInfo;
        """)

        # 记录检测到的 WebSocket 连接
        if ws_info.get('connections'):
            for ws_url in set(ws_info['connections']):  # 去重
                record_websocket_connection(ws_url, "detected_in_content")

        if ws_info.get('scripts'):
            for script_url in ws_info['scripts']:
                record_websocket_connection(script_url, "detected_in_script")

        if ws_info.get('hasWebSocket'):
            print("页面支持 WebSocket")

    except Exception as e:
        print(f"WebSocket 检测失败: {e}")

def capture_network_via_cdp(driver):
    """通过 CDP 直接捕获网络事件"""
    global network_logs, websocket_logs

    try:
        # 记录当前页面
        current_url = driver.current_url
        if current_url and current_url != "data:,":
            # 检查是否已经记录过这个 URL
            url_exists = any(entry.get('request', {}).get('url') == current_url for entry in network_logs)
            if not url_exists:
                # 获取页面的基本信息
                try:
                    page_title = driver.title
                    page_source_length = len(driver.page_source)
                    user_agent = driver.execute_script("return navigator.userAgent")
                except Exception:
                    page_title = "Unknown"
                    page_source_length = 0
                    user_agent = "Unknown"

                basic_entry = {
                    "startedDateTime": datetime.now().isoformat() + "Z",
                    "time": 0,
                    "request": {
                        "method": "GET",
                        "url": current_url,
                        "httpVersion": "HTTP/1.1",
                        "headers": [
                            {"name": "User-Agent", "value": user_agent},
                            {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}
                        ],
                        "queryString": [],
                        "cookies": [],
                        "headersSize": -1,
                        "bodySize": 0
                    },
                    "response": {
                        "status": 200,
                        "statusText": "OK",
                        "httpVersion": "HTTP/1.1",
                        "headers": [
                            {"name": "Content-Type", "value": "text/html; charset=utf-8"},
                            {"name": "Content-Length", "value": str(page_source_length)}
                        ],
                        "cookies": [],
                        "content": {
                            "size": page_source_length,
                            "mimeType": "text/html",
                            "text": ""  # 不保存完整页面内容以节省空间
                        },
                        "redirectURL": "",
                        "headersSize": -1,
                        "bodySize": page_source_length
                    },
                    "cache": {},
                    "timings": {
                        "send": 0,
                        "wait": 0,
                        "receive": 0
                    },
                    "pageTitle": page_title
                }

                network_logs.append(basic_entry)
                print(f"记录页面访问: {current_url} (标题: {page_title})")

        # 尝试通过 CDP 获取更多网络信息
        try:
            # 获取当前页面的资源信息
            resources = driver.execute_script("""
                var resources = [];
                if (window.performance && window.performance.getEntriesByType) {
                    var entries = window.performance.getEntriesByType('resource');
                    for (var i = 0; i < entries.length; i++) {
                        var entry = entries[i];
                        resources.push({
                            name: entry.name,
                            type: entry.initiatorType,
                            startTime: entry.startTime,
                            duration: entry.duration,
                            transferSize: entry.transferSize || 0
                        });
                    }
                }
                return resources;
            """)

            # 为每个资源创建网络记录
            for resource in resources:
                resource_url = resource.get('name', '')
                if resource_url and not any(entry.get('request', {}).get('url') == resource_url for entry in network_logs):
                    resource_entry = {
                        "startedDateTime": datetime.now().isoformat() + "Z",
                        "time": resource.get('duration', 0),
                        "request": {
                            "method": "GET",
                            "url": resource_url,
                            "httpVersion": "HTTP/1.1",
                            "headers": [],
                            "queryString": [],
                            "cookies": [],
                            "headersSize": -1,
                            "bodySize": 0
                        },
                        "response": {
                            "status": 200,
                            "statusText": "OK",
                            "httpVersion": "HTTP/1.1",
                            "headers": [],
                            "cookies": [],
                            "content": {
                                "size": resource.get('transferSize', 0),
                                "mimeType": get_mime_type_from_url(resource_url)
                            },
                            "redirectURL": "",
                            "headersSize": -1,
                            "bodySize": resource.get('transferSize', 0)
                        },
                        "cache": {},
                        "timings": {
                            "send": 0,
                            "wait": resource.get('duration', 0),
                            "receive": 0
                        },
                        "resourceType": resource.get('type', 'other')
                    }

                    network_logs.append(resource_entry)

            if resources:
                print(f"记录了 {len(resources)} 个页面资源")

        except Exception as resource_error:
            print(f"获取页面资源失败: {resource_error}")

    except Exception as e:
        print(f"CDP 网络捕获失败: {e}")

def get_mime_type_from_url(url):
    """根据 URL 推断 MIME 类型"""
    try:
        if url.endswith('.js'):
            return 'application/javascript'
        elif url.endswith('.css'):
            return 'text/css'
        elif url.endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
            return 'image/*'
        elif url.endswith(('.woff', '.woff2', '.ttf')):
            return 'font/*'
        elif url.endswith('.json'):
            return 'application/json'
        elif url.endswith('.xml'):
            return 'application/xml'
        else:
            return 'text/html'
    except Exception:
        return 'application/octet-stream'

def get_websocket_stats():
    """获取 WebSocket 连接统计信息"""
    global websocket_logs

    stats = {
        "total_connections": len(websocket_logs),
        "active_connections": 0,
        "closed_connections": 0,
        "total_frames": 0,
        "sent_frames": 0,
        "received_frames": 0
    }

    for ws_entry in websocket_logs:
        if ws_entry.get('status') == 'closed':
            stats["closed_connections"] += 1
        else:
            stats["active_connections"] += 1

        frames = ws_entry.get('frames', [])
        stats["total_frames"] += len(frames)

        for frame in frames:
            if frame.get('type') == 'sent':
                stats["sent_frames"] += 1
            elif frame.get('type') == 'received':
                stats["received_frames"] += 1

    return stats

def periodic_network_capture(driver):
    """定期捕获网络日志（兼容性函数）"""
    try:
        # 使用新的增强捕获方法
        enhanced_network_capture(driver, "定期捕获")
    except Exception as e:
        print(f"定期网络捕获出错: {e}")

def record_current_page(driver):
    """记录当前页面的基本信息"""
    global network_logs

    try:
        current_url = driver.current_url
        if not current_url or current_url == "data:,":
            return

        # 检查是否已经记录过这个 URL
        url_exists = any(entry.get('request', {}).get('url') == current_url for entry in network_logs)
        if url_exists:
            return

        # 创建页面记录
        page_entry = {
            "startedDateTime": datetime.now().isoformat() + "Z",
            "time": 0,
            "request": {
                "method": "GET",
                "url": current_url,
                "httpVersion": "HTTP/1.1",
                "headers": [],
                "queryString": [],
                "cookies": [],
                "headersSize": -1,
                "bodySize": 0
            },
            "response": {
                "status": 200,
                "statusText": "OK",
                "httpVersion": "HTTP/1.1",
                "headers": [{"name": "Content-Type", "value": "text/html"}],
                "cookies": [],
                "content": {
                    "size": 0,
                    "mimeType": "text/html"
                },
                "redirectURL": "",
                "headersSize": -1,
                "bodySize": 0
            },
            "cache": {},
            "timings": {
                "send": 0,
                "wait": 0,
                "receive": 0
            }
        }

        network_logs.append(page_entry)
        print(f"记录页面访问: {current_url}")

    except Exception as e:
        print(f"记录当前页面失败: {e}")

def record_websocket_connection(url, status="detected"):
    """手动记录 WebSocket 连接"""
    global websocket_logs

    try:
        # 检查是否已经记录过这个 WebSocket URL
        ws_exists = any(ws.get('url') == url for ws in websocket_logs)
        if ws_exists:
            return

        ws_entry = {
            "requestId": f"manual-ws-{len(websocket_logs) + 1}",
            "url": url,
            "initiator": {"type": "manual"},
            "createdDateTime": datetime.now().isoformat() + "Z",
            "status": status,
            "frames": []
        }

        websocket_logs.append(ws_entry)
        print(f"记录 WebSocket 连接: {url}")

    except Exception as e:
        print(f"记录 WebSocket 连接失败: {e}")

def main():
    """主函数，控制整个自动化流程"""
    # 请在这里替换为您的真实信息
    email_jwt = get_email()
    if not email_jwt:
        return "FAILURE"
    email, jwt = email_jwt

    driver = None
    har_saved = False

    while True:
        try:
            driver = setup_driver()

            # 启动网络捕获
            start_network_capture(driver)

            # 检测常见的 WebSocket 连接
            try:
                # 检查页面中是否有 WebSocket 连接
                ws_scripts = driver.execute_script("""
                    var wsConnections = [];
                    // 检查是否有 WebSocket 对象
                    if (window.WebSocket) {
                        // 尝试检测页面中的 WebSocket 连接
                        var scripts = document.getElementsByTagName('script');
                        for (var i = 0; i < scripts.length; i++) {
                            var script = scripts[i];
                            if (script.src && (script.src.includes('ws://') || script.src.includes('wss://'))) {
                                wsConnections.push(script.src);
                            }
                        }
                    }
                    return wsConnections;
                """)

                for ws_url in ws_scripts:
                    record_websocket_connection(ws_url, "detected_in_page")

            except Exception as ws_detect_error:
                print(f"WebSocket 检测失败: {ws_detect_error}")

            logger.info(email)

            # 增强网络捕获
            enhanced_network_capture(driver, "主函数开始")

            if not login(driver, email):
                # 如果登录失败，则没有必要继续
                raise Exception("登录步骤失败")

            # 捕获登录后的网络日志
            time.sleep(3)
            enhanced_network_capture(driver, "登录完成后")

            code = get_email_data(jwt)
            if not verify_email(driver, code):
                # 如果验证失败，则没有必要继续
                raise Exception("邮箱验证步骤失败")

            # 捕获验证后的网络日志
            enhanced_network_capture(driver, "邮箱验证完成后")

            try:
                driver.execute_script(
                    "document.getElementById('action-form').addEventListener('submit', function(e) { e.preventDefault(); });")
            except Exception as e:
                pass
            bypass_human_verification(driver, duration=30)

            # 捕获验证过程中的网络日志
            enhanced_network_capture(driver, "人类验证过程中")

            # 进入最关键的注册和重试步骤
            result = attempt_signup_with_retry(driver)

            if result == "SUCCESS":
                print("\n进入订阅页面，开始获取vscode_url")
                try:
                    bypass_human_verification_drag(driver, duration=10)
                    time.sleep(3)

                    get_vscode(driver,email)
                    print("\n自动化流程成功完成！")

                    # 保存 HAR 文件和 WebSocket 摘要
                    if not har_saved:
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        har_filename = f"success_{email.replace('@', '_')}_{timestamp}.har"
                        ws_filename = f"success_{email.replace('@', '_')}_{timestamp}_ws.json"

                        save_har_file(har_filename)
                        save_websocket_summary(ws_filename)

                        # 显示 WebSocket 统计信息
                        ws_stats = get_websocket_stats()
                        print(f"WebSocket 统计: {ws_stats}")

                        har_saved = True

                    break # 成功，跳出 while 循环
                except Exception as e:
                    get_augtoken(driver,email)
            elif result == "RESTART":
                print("\n检测到严重错误，将关闭浏览器并重新开始整个流程...")

                # 保存错误情况下的 HAR 文件和 WebSocket 摘要
                if not har_saved:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    har_filename = f"error_{email.replace('@', '_')}_{timestamp}.har"
                    ws_filename = f"error_{email.replace('@', '_')}_{timestamp}_ws.json"

                    save_har_file(har_filename)
                    save_websocket_summary(ws_filename)
                    har_saved = True

                continue #会自动进入下一次 while 循环
            else: # result == "FAILURE"
                print("\n所有重试均告失败，流程终止。")

                # 保存失败情况下的 HAR 文件和 WebSocket 摘要
                if not har_saved:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    har_filename = f"failure_{email.replace('@', '_')}_{timestamp}.har"
                    ws_filename = f"failure_{email.replace('@', '_')}_{timestamp}_ws.json"

                    save_har_file(har_filename)
                    save_websocket_summary(ws_filename)
                    har_saved = True

                break # 失败，跳出 while 循环

        except Exception as e:
            print(f"\n主流程中发生严重错误: {e}")
            print("准备重启流程...")

            # 保存异常情况下的 HAR 文件和 WebSocket 摘要
            if not har_saved:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                har_filename = f"exception_{email.replace('@', '_')}_{timestamp}.har"
                ws_filename = f"exception_{email.replace('@', '_')}_{timestamp}_ws.json"

                save_har_file(har_filename)
                save_websocket_summary(ws_filename)
                har_saved = True

        finally:
            # 确保在程序结束时保存 HAR 文件和 WebSocket 摘要
            if driver and not har_saved:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                har_filename = f"final_{email.replace('@', '_')}_{timestamp}.har"
                ws_filename = f"final_{email.replace('@', '_')}_{timestamp}_ws.json"

                save_har_file(har_filename)
                save_websocket_summary(ws_filename)

def test_har_functionality():
    """测试 HAR 和 WebSocket 功能"""
    global network_logs, websocket_logs, har_data

    # 模拟一些网络请求数据
    test_entry = {
        "startedDateTime": datetime.now().isoformat() + "Z",
        "time": 1000,
        "request": {
            "method": "GET",
            "url": "https://app.augmentcode.com/account",
            "httpVersion": "HTTP/1.1",
            "headers": [{"name": "User-Agent", "value": "Test Browser"}],
            "queryString": [],
            "cookies": [],
            "headersSize": -1,
            "bodySize": 0
        },
        "response": {
            "status": 200,
            "statusText": "OK",
            "httpVersion": "HTTP/1.1",
            "headers": [{"name": "Content-Type", "value": "text/html"}],
            "cookies": [],
            "content": {
                "size": 1024,
                "mimeType": "text/html",
                "text": "<html><body>Test</body></html>"
            },
            "redirectURL": "",
            "headersSize": -1,
            "bodySize": 1024
        },
        "cache": {},
        "timings": {
            "send": 0,
            "wait": 1000,
            "receive": 0
        }
    }

    network_logs.append(test_entry)

    # 模拟 WebSocket 连接数据
    test_websocket = {
        "requestId": "test-ws-001",
        "url": "wss://app.augmentcode.com/ws",
        "initiator": {"type": "script"},
        "createdDateTime": datetime.now().isoformat() + "Z",
        "status": "closed",
        "handshakeResponse": {
            "status": 101,
            "statusText": "Switching Protocols",
            "headers": {
                "Upgrade": "websocket",
                "Connection": "Upgrade",
                "Sec-WebSocket-Accept": "test-accept-key"
            },
            "timestamp": time.time()
        },
        "frames": [
            {
                "type": "sent",
                "timestamp": time.time(),
                "opcode": 1,
                "mask": True,
                "payloadData": '{"type":"auth","token":"test-token"}',
                "payloadLength": 35
            },
            {
                "type": "received",
                "timestamp": time.time() + 0.1,
                "opcode": 1,
                "mask": False,
                "payloadData": '{"type":"auth_success","user_id":"12345"}',
                "payloadLength": 40
            },
            {
                "type": "sent",
                "timestamp": time.time() + 1,
                "opcode": 1,
                "mask": True,
                "payloadData": '{"type":"ping"}',
                "payloadLength": 15
            },
            {
                "type": "received",
                "timestamp": time.time() + 1.1,
                "opcode": 1,
                "mask": False,
                "payloadData": '{"type":"pong"}',
                "payloadLength": 15
            }
        ],
        "closedDateTime": datetime.now().isoformat() + "Z"
    }

    websocket_logs.append(test_websocket)

    # 保存测试 HAR 文件和 WebSocket 摘要
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    test_har_filename = f"test_har_{timestamp}.har"
    test_ws_filename = f"test_websocket_{timestamp}.json"

    saved_har = save_har_file(test_har_filename)
    saved_ws = save_websocket_summary(test_ws_filename)

    if saved_har and saved_ws:
        print(f"HAR 和 WebSocket 功能测试成功！")
        print(f"HAR 文件: {saved_har}")
        print(f"WebSocket 摘要: {saved_ws}")

        # 显示统计信息
        ws_stats = get_websocket_stats()
        print(f"WebSocket 测试统计: {ws_stats}")
    else:
        print("HAR 或 WebSocket 功能测试失败！")

if __name__ == "__main__":
    n = 0
    DOMAIN = "smartdocker.online"#"565847.cfd"

    # 测试 HAR 功能
    print("测试 HAR 和 WebSocket 功能...")
    try:
        test_har_functionality()
        print("HAR 和 WebSocket 功能测试完成")

        # 显示当前网络捕获状态
        print(f"当前网络日志数量: {len(network_logs)}")
        print(f"当前 WebSocket 日志数量: {len(websocket_logs)}")

    except Exception as test_error:
        print(f"HAR 功能测试失败: {test_error}")
    print("=" * 50)

    while True:
        try:
            main()
            n+=1
            print(f"添加第{n}个")
            #time.sleep(random.uniform(100,200))
            time.sleep(random.uniform(10,20))
        except Exception as e:
            print(f"  报错 \n{e}")
            # 即使出错也保存 HAR 文件和 WebSocket 摘要
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                error_har_filename = f"error_main_{timestamp}.har"
                error_ws_filename = f"error_main_{timestamp}_ws.json"

                save_har_file(error_har_filename)
                save_websocket_summary(error_ws_filename)
            except Exception as har_error:
                print(f"保存错误 HAR/WebSocket 文件失败: {har_error}")
        # if n >= 5:
        #     break
