2025-08-20 11:40:50 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51557,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 11:40:50 - INFO - 127.0.0.1:51557
2025-08-20 11:40:51 - INFO - <EMAIL>
2025-08-20 11:40:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 11:41:37 - INFO - 获取 cap_value 验证码成功...
2025-08-20 11:41:40 - INFO - 找到 Turnstile...
2025-08-20 11:41:40 - INFO - 登录信息已提交，等待验证页面...
2025-08-20 11:41:45 - INFO - Traceback (most recent call last):

2025-08-20 11:41:45 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1014[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-20 11:41:45 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m967[0m, in [35mmain[0m
    code = get_email_data(jwt)

2025-08-20 11:41:45 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m363[0m, in [35mget_email_data[0m
    return get_email_data(jwt)

2025-08-20 11:41:45 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m363[0m, in [35mget_email_data[0m
    return get_email_data(jwt)

2025-08-20 11:41:45 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m363[0m, in [35mget_email_data[0m
    return get_email_data(jwt)

2025-08-20 11:41:45 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m346[0m, in [35mget_email_data[0m
    res = s.get(
        baseurl+"/api/mails",
    ...<5 lines>...
            "Content-Type": "application/json"
    })

2025-08-20 11:41:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m602[0m, in [35mget[0m
    return [31mself.request[0m[1;31m("GET", url, **kwargs)[0m
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 11:41:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m589[0m, in [35mrequest[0m
    resp = self.send(prep, **send_kwargs)

2025-08-20 11:41:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\sessions.py"[0m, line [35m703[0m, in [35msend[0m
    r = adapter.send(request, **kwargs)

2025-08-20 11:41:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\requests\adapters.py"[0m, line [35m667[0m, in [35msend[0m
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )

2025-08-20 11:41:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-20 11:41:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-20 11:41:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-20 11:41:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 11:41:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 11:41:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-20 11:41:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-20 11:41:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\ssl.py"[0m, line [35m1304[0m, in [35mrecv_into[0m
    return [31mself.read[0m[1;31m(nbytes, buffer)[0m
           [31m~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^[0m

2025-08-20 11:41:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\ssl.py"[0m, line [35m1138[0m, in [35mread[0m
    return [31mself._sslobj.read[0m[1;31m(len, buffer)[0m
           [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^[0m

2025-08-20 11:41:45 - INFO - [1;35mKeyboardInterrupt[0m

