2025-08-20 23:21:25 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55664,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:21:25 - INFO - 127.0.0.1:55664
2025-08-20 23:21:25 - INFO - <EMAIL>
2025-08-20 23:21:25 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:21:42 - INFO - 获取 cap_value 验证码成功...
2025-08-20 23:21:44 - INFO - 找到 Turnstile...
2025-08-20 23:21:47 - INFO - 
主流程中发生严重错误: Message: stale element reference: stale element not found
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#staleelementreferenceexception
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608490224]
	(No symbol) [0x0x7ff60848ecf3]
	(No symbol) [0x0x7ff6084824e9]
	(No symbol) [0x0x7ff60848039f]
	(No symbol) [0x0x7ff60848482c]
	(No symbol) [0x0x7ff6084848ff]
	(No symbol) [0x0x7ff6084ca08b]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff6084c2c16]
	(No symbol) [0x0x7ff6084f8680]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 23:21:47 - INFO - 准备重启流程...
2025-08-20 23:21:50 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55890,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:21:50 - INFO - 127.0.0.1:55890
2025-08-20 23:21:50 - INFO - <EMAIL>
2025-08-20 23:21:50 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:22:00 - INFO - Traceback (most recent call last):

2025-08-20 23:22:00 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-20 23:22:00 - INFO - [1;35mConnectionResetError[0m: [35m[WinError 10054] 远程主机强迫关闭了一个现有的连接。[0m

2025-08-20 23:22:00 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-20 23:22:00 - INFO - Traceback (most recent call last):

2025-08-20 23:22:00 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1004[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m958[0m, in [35mmain[0m
    if not [31mlogin[0m[1;31m(driver, email)[0m:
           [31m~~~~~[0m[1;31m^^^^^^^^^^^^^^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m694[0m, in [35mlogin[0m
    [31mdriver.get[0m[1;31m("https://app.augmentcode.com/account")[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m479[0m, in [35mget[0m
    [31mself.execute[0m[1;31m(Command.GET, {"url": url})[0m
    [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m451[0m, in [35mexecute[0m
    response = cast(RemoteConnection, self.command_executor).execute(driver_command, params)

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m404[0m, in [35mexecute[0m
    return [31mself._request[0m[1;31m(command_info[0], url, body=data)[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m428[0m, in [35m_request[0m
    response = self._conn.request(method, url, body=body, headers=headers, timeout=self._client_config.timeout)

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m143[0m, in [35mrequest[0m
    return [31mself.request_encode_body[0m[1;31m([0m
           [31m~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mmethod, url, fields=fields, headers=headers, **urlopen_kw[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m278[0m, in [35mrequest_encode_body[0m
    return [31mself.urlopen[0m[1;31m(method, url, **extra_kw)[0m
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\poolmanager.py"[0m, line [35m459[0m, in [35murlopen[0m
    response = conn.urlopen(method, u.request_uri, **kw)

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-20 23:22:00 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1432[0m, in [35mgetresponse[0m
    [31mself.close[0m[1;31m()[0m
    [31m~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:22:00 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m369[0m, in [35mclose[0m
    def close(self) -> None:
    

2025-08-20 23:22:00 - INFO - [1;35mKeyboardInterrupt[0m

