2025-08-11 15:04:21 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62325,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 15:04:21 - INFO - <EMAIL>
2025-08-11 15:04:21 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 15:04:32 - INFO - 获取 cap_value 验证码成功...
2025-08-11 15:04:36 - INFO - 找到 Turnstile...
2025-08-11 15:04:39 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 15:04:39 - INFO - 已执行拟人滚动: -35px
2025-08-11 15:04:39 - INFO - 已执行页面焦点操作
2025-08-11 15:04:40 - INFO - 已执行拟人操作完成，总停顿: 1.02秒
2025-08-11 15:04:41 - INFO - 登录后已执行更多拟人操作
2025-08-11 15:04:44 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 15:04:53 - INFO - 验证码已提交，等待跳转...
2025-08-11 15:04:53 - INFO - 已执行拟人滚动: -40px
2025-08-11 15:04:54 - INFO - 已执行页面焦点操作
2025-08-11 15:04:54 - INFO - 已执行拟人操作完成，总停顿: 1.12秒
2025-08-11 15:05:01 - INFO - 
第 1/5 次尝试注册...
2025-08-11 15:05:01 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 15:05:01 - INFO - 开始执行30秒持续拟人操作...
2025-08-11 15:05:03 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:05:05 - INFO - 已执行拟人滚动: -90px
2025-08-11 15:05:06 - INFO - 已执行拟人操作完成，总停顿: 1.33秒
2025-08-11 15:05:08 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:05:10 - INFO - 已执行拟人滚动: 97px
2025-08-11 15:05:11 - INFO - 已执行页面焦点操作
2025-08-11 15:05:11 - INFO - 已执行拟人操作完成，总停顿: 1.37秒
2025-08-11 15:05:13 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:05:15 - INFO - 已执行拟人滚动: -108px
2025-08-11 15:05:16 - INFO - 已执行拟人操作完成，总停顿: 1.05秒
2025-08-11 15:05:17 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:05:21 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:05:24 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:05:26 - INFO - 已执行拟人滚动: 37px
2025-08-11 15:05:27 - INFO - 已执行拟人操作完成，总停顿: 1.29秒
2025-08-11 15:05:29 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:05:30 - INFO - 已执行拟人滚动: 48px
2025-08-11 15:05:31 - INFO - 已执行页面焦点操作
2025-08-11 15:05:32 - INFO - 已执行拟人操作完成，总停顿: 1.31秒
2025-08-11 15:05:32 - INFO - 30秒拟人操作完成
2025-08-11 15:05:32 - INFO - 成功！当前在订阅页面。
2025-08-11 15:05:32 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-11 15:05:37 - INFO - 代码信息: {
  "codeVerifier": "mYmdGnnmKDkXilQFcTckyj7W5KVu3laxDjGyJ_qkPpE",
  "code_challenge": "S9BQOljYjgZ3wH8NkdhNKYNjIMThBejNiBk3uUz2Tsg",
  "state": "7f9d2e9e-a15c-4861-ae5b-41d694e62a04"
}
2025-08-11 15:05:37 - INFO - ==================================================
2025-08-11 15:05:37 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_8cfbf5aaee63414437821087f4ec2084&state=7f9d2e9e-a15c-4861-ae5b-41d694e62a04&tenant_url=https%3A%2F%2Fd18.api.augmentcode.com%2F
2025-08-11 15:05:39 - INFO - 添加session成功: {'status': 'success'}
2025-08-11 15:05:39 - INFO - 添加session: <EMAIL>   e0730a43893cbef8618feaaeda43359b8f8df590216c386db8a00bc3758d4cb2   https://d18.api.augmentcode.com/  2025-08-18T07:05:40.381665207Z
2025-08-11 15:05:39 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mBK2-nDlX9CCjOQRlq0oolR_92GuHB5X-e-oL9wSSFz3uC4lTs3MIUUl2efQ2I4AjQwxwfnPx3p0t9vXPpI1eAU4vI2zntvhNGqIzdJZWmwArWu9bzmsS6NELqTTgrvbM2lUaqBHbMTKul6jjR3FrXz6BBPtxTKRut45nJY8xIzw2-xHysTJjNqarUgbLWUUzsohW0gstZKKRAJPl9Fd0QA.aJmWQw.mE_gInn1fx7Sk4tOmjMJbJBM324
2025-08-11 15:05:39 - INFO - 
自动化流程成功完成！
2025-08-11 15:05:39 - INFO - 添加第1个
2025-08-11 15:06:05 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62565,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-11 15:06:05 - INFO - <EMAIL>
2025-08-11 15:06:05 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-11 15:06:24 - INFO - 获取 cap_value 验证码成功...
2025-08-11 15:06:28 - INFO - 找到 Turnstile...
2025-08-11 15:06:30 - INFO - 登录信息已提交，等待验证页面...
2025-08-11 15:06:30 - INFO - 已执行拟人滚动: -74px
2025-08-11 15:06:31 - INFO - 已执行拟人操作完成，总停顿: 0.89秒
2025-08-11 15:06:32 - INFO - 登录后已执行更多拟人操作
2025-08-11 15:06:36 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-11 15:06:36 - INFO - 验证码已提交，等待跳转...
2025-08-11 15:06:45 - INFO - 已执行拟人滚动: 92px
2025-08-11 15:06:46 - INFO - 已执行拟人操作完成，总停顿: 0.77秒
2025-08-11 15:06:46 - INFO - 
第 1/5 次尝试注册...
2025-08-11 15:06:46 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-11 15:06:46 - INFO - 开始执行30秒持续拟人操作...
2025-08-11 15:06:48 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:06:51 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:06:55 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:06:56 - INFO - 已执行拟人滚动: -56px
2025-08-11 15:06:57 - INFO - 已执行页面焦点操作
2025-08-11 15:06:57 - INFO - 已执行拟人操作完成，总停顿: 1.50秒
2025-08-11 15:06:59 - INFO - CDP: 已执行 1 轮鼠标按下/拖动/放开/单击/双击/滚动
2025-08-11 15:07:00 - INFO - 已执行拟人滚动: -28px
2025-08-11 15:07:01 - INFO - 已执行页面焦点操作
2025-08-11 15:07:02 - INFO - 已执行拟人操作完成，总停顿: 1.16秒
2025-08-11 15:07:02 - INFO - CDP 拟人鼠标操作失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff7b33b6e75+26517]
	(No symbol) [0x0x7ff7b3310780]
	(No symbol) [0x0x7ff7b3199dea]
	(No symbol) [0x0x7ff7b3185f15]
	(No symbol) [0x0x7ff7b31aabf4]
	(No symbol) [0x0x7ff7b321fa85]
	(No symbol) [0x0x7ff7b323ff72]
	(No symbol) [0x0x7ff7b3218243]
	(No symbol) [0x0x7ff7b31e1431]
	(No symbol) [0x0x7ff7b31e21c3]
	GetHandleVerifier [0x0x7ff7b37484ad+3767757]
	GetHandleVerifier [0x0x7ff7b376bb03+3912739]
	GetHandleVerifier [0x0x7ff7b376009d+3865021]
	GetHandleVerifier [0x0x7ff7b349827e+949150]
	(No symbol) [0x0x7ff7b331c59f]
	(No symbol) [0x0x7ff7b3317f54]
	(No symbol) [0x0x7ff7b3318109]
	(No symbol) [0x0x7ff7b3306c68]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-11 15:07:04 - INFO - 滚动操作跳过: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff7b33b6e75+26517]
	(No symbol) [0x0x7ff7b3310780]
	(No symbol) [0x0x7ff7b3199c1c]
	(No symbol) [0x0x7ff7b31e055f]
	(No symbol) [0x0x7ff7b3218332]
	(No symbol) [0x0x7ff7b3212e53]
	(No symbol) [0x0x7ff7b3211f19]
	(No symbol) [0x0x7ff7b3164b05]
	GetHandleVerifier [0x0x7ff7b37484ad+3767757]
	GetHandleVerifier [0x0x7ff7b376bb03+3912739]
	GetHandleVerifier [0x0x7ff7b376009d+3865021]
	GetHandleVerifier [0x0x7ff7b349827e+949150]
	(No symbol) [0x0x7ff7b331c59f]
	(No symbol) [0x0x7ff7b3163b00]
	GetHandleVerifier [0x0x7ff7b37d84c8+4357608]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-11 15:07:04 - INFO - 焦点操作跳过: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff7b33b6e75+26517]
	(No symbol) [0x0x7ff7b3310780]
	(No symbol) [0x0x7ff7b3199c1c]
	(No symbol) [0x0x7ff7b31e055f]
	(No symbol) [0x0x7ff7b3218332]
	(No symbol) [0x0x7ff7b3212e53]
	(No symbol) [0x0x7ff7b3211f19]
	(No symbol) [0x0x7ff7b3164b05]
	GetHandleVerifier [0x0x7ff7b37484ad+3767757]
	GetHandleVerifier [0x0x7ff7b376bb03+3912739]
	GetHandleVerifier [0x0x7ff7b376009d+3865021]
	GetHandleVerifier [0x0x7ff7b349827e+949150]
	(No symbol) [0x0x7ff7b331c59f]
	(No symbol) [0x0x7ff7b3163b00]
	GetHandleVerifier [0x0x7ff7b37d84c8+4357608]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-11 15:07:05 - INFO - 已执行拟人操作完成，总停顿: 1.08秒
2025-08-11 15:07:05 - INFO - CDP 拟人鼠标操作失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff7b33b6e75+26517]
	(No symbol) [0x0x7ff7b3310780]
	(No symbol) [0x0x7ff7b3199c1c]
	(No symbol) [0x0x7ff7b31e055f]
	(No symbol) [0x0x7ff7b3218332]
	(No symbol) [0x0x7ff7b3212e53]
	(No symbol) [0x0x7ff7b3211f19]
	(No symbol) [0x0x7ff7b3164b05]
	GetHandleVerifier [0x0x7ff7b37484ad+3767757]
	GetHandleVerifier [0x0x7ff7b376bb03+3912739]
	GetHandleVerifier [0x0x7ff7b376009d+3865021]
	GetHandleVerifier [0x0x7ff7b349827e+949150]
	(No symbol) [0x0x7ff7b331c59f]
	(No symbol) [0x0x7ff7b3163b00]
	GetHandleVerifier [0x0x7ff7b37d84c8+4357608]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-11 15:07:06 - INFO - 滚动操作跳过: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff7b33b6e75+26517]
	(No symbol) [0x0x7ff7b3310780]
	(No symbol) [0x0x7ff7b3199c1c]
	(No symbol) [0x0x7ff7b31e055f]
	(No symbol) [0x0x7ff7b3218332]
	(No symbol) [0x0x7ff7b3212e53]
	(No symbol) [0x0x7ff7b3211f19]
	(No symbol) [0x0x7ff7b3164b05]
	GetHandleVerifier [0x0x7ff7b37484ad+3767757]
	GetHandleVerifier [0x0x7ff7b376bb03+3912739]
	GetHandleVerifier [0x0x7ff7b376009d+3865021]
	GetHandleVerifier [0x0x7ff7b349827e+949150]
	(No symbol) [0x0x7ff7b331c59f]
	(No symbol) [0x0x7ff7b3163b00]
	GetHandleVerifier [0x0x7ff7b37d84c8+4357608]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-11 15:07:07 - INFO - 已执行拟人操作完成，总停顿: 1.08秒
2025-08-11 15:07:07 - INFO - CDP 拟人鼠标操作失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff7b33b6e75+26517]
	(No symbol) [0x0x7ff7b3310780]
	(No symbol) [0x0x7ff7b3199c1c]
	(No symbol) [0x0x7ff7b31e055f]
	(No symbol) [0x0x7ff7b3218332]
	(No symbol) [0x0x7ff7b3212e53]
	(No symbol) [0x0x7ff7b3211f19]
	(No symbol) [0x0x7ff7b3164b05]
	GetHandleVerifier [0x0x7ff7b37484ad+3767757]
	GetHandleVerifier [0x0x7ff7b376bb03+3912739]
	GetHandleVerifier [0x0x7ff7b376009d+3865021]
	GetHandleVerifier [0x0x7ff7b349827e+949150]
	(No symbol) [0x0x7ff7b331c59f]
	(No symbol) [0x0x7ff7b3163b00]
	GetHandleVerifier [0x0x7ff7b37d84c8+4357608]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-11 15:07:08 - INFO - 滚动操作跳过: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff7b33b6e75+26517]
	(No symbol) [0x0x7ff7b3310780]
	(No symbol) [0x0x7ff7b3199c1c]
	(No symbol) [0x0x7ff7b31e055f]
	(No symbol) [0x0x7ff7b3218332]
	(No symbol) [0x0x7ff7b3212e53]
	(No symbol) [0x0x7ff7b3211f19]
	(No symbol) [0x0x7ff7b3164b05]
	GetHandleVerifier [0x0x7ff7b37484ad+3767757]
	GetHandleVerifier [0x0x7ff7b376bb03+3912739]
	GetHandleVerifier [0x0x7ff7b376009d+3865021]
	GetHandleVerifier [0x0x7ff7b349827e+949150]
	(No symbol) [0x0x7ff7b331c59f]
	(No symbol) [0x0x7ff7b3163b00]
	GetHandleVerifier [0x0x7ff7b37d84c8+4357608]
	BaseThreadInitThunk [0x0x7ffae4bce8d7+23]
	RtlUserThreadStart [0x0x7ffae6e3c34c+44]

2025-08-11 15:07:08 - INFO - Traceback (most recent call last):

2025-08-11 15:07:08 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1074[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-11 15:07:08 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m1041[0m, in [35mmain[0m
    result = attempt_signup_with_retry(driver)

2025-08-11 15:07:08 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m877[0m, in [35mattempt_signup_with_retry[0m
    [31msimulate_human_behavior[0m[1;31m(driver)[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^[0m

2025-08-11 15:07:08 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m338[0m, in [35msimulate_human_behavior[0m
    [31mtime.sleep[0m[1;31m(pause_time)[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^[0m

2025-08-11 15:07:08 - INFO - [1;35mKeyboardInterrupt[0m

