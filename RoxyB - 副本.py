import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_driver():
    """初始化并返回一个 Chrome WebDriver 实例。"""
    # 这里可以添加更多的 WebDriver 配置，例如无头模式等
    # options = webdriver.ChromeOptions()
    # options.add_argument('--headless')
    # driver = webdriver.Chrome(options=options)
    driver = webdriver.Chrome()
    driver.implicitly_wait(10) # 设置一个隐式等待
    return driver

def login(driver, username):
    """
    执行登录步骤。
    1. 打开登录页面
    2. 输入用户名
    3. 点击登录按钮
    """
    print("步骤 1 & 2: 正在打开登录页面并输入用户名...")
    driver.get("https://app.augmentcode.com/account")
    try:
        # 等待用户名输入框加载完成并输入
        username_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "login_input"))
        )
        username_input.clear()
        username_input.send_keys(username)

        # 找到并点击登录按钮
        login_button = driver.find_element(By.NAME, "action")
        login_button.click()
        print("登录信息已提交，等待验证页面...")
        return True
    except TimeoutException:
        print("错误：登录页面加载超时或找不到登录元素。")
        return False

def verify_email(driver, email_code):
    """
    执行邮箱验证步骤。
    1. 输入验证码
    2. 点击验证按钮
    """
    print("步骤 3 & 4: 正在输入邮箱验证码...")
    try:
        # 等待验证码输入框加载完成并输入
        code_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "code"))
        )
        code_input.clear()
        code_input.send_keys(email_code)

        # 找到并点击验证按钮
        verify_button = driver.find_element(By.NAME, "action")
        verify_button.click()
        print("验证码已提交，等待跳转...")
        return True
    except TimeoutException:
        print("错误：验证页面加载超时或找不到验证元素。")
        return False

def attempt_signup_with_retry(driver):
    """
    执行注册的最后步骤，并包含重试逻辑。
    - 勾选复选框
    - 点击注册按钮
    - 如果失败，则根据错误信息进行重试或返回错误状态
    """
    max_retries = 5
    for attempt in range(max_retries):
        print(f"\n第 {attempt + 1}/{max_retries} 次尝试注册...")
        try:
            # 步骤 5: 查找并勾选复选框，然后点击注册按钮
            print("步骤 5: 正在勾选复选框并点击注册按钮...")
            
            # 等待复选框出现并确保它没有被选中
            checkbox_mark = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.CLASS_NAME, "c-checkbox--mark"))
            )
            # 使用JS点击以防止元素被遮挡
            driver.execute_script("arguments[0].click();", checkbox_mark)
            print("复选框已勾选。")
            
            # 等待注册按钮可点击
            signup_button = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.ID, "signup-button"))
            )
            signup_button.click()
            print("注册按钮已点击，等待页面加载...")

            # 等待页面跳转后的结果
            time.sleep(5) # 等待一下，让页面有时间响应

            # 步骤 6: 检查是否出现 "Sign-up rejected"
            try:
                rejection_element = driver.find_element(By.XPATH, "/html/body/div/div/div/div")
                if "Sign-up rejected" in rejection_element.text:
                    print(f"检测到 'Sign-up rejected'。准备回退并刷新。")
                    
                    # 回退页面
                    driver.back()
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.ID, "signup-button"))
                    )
                    print("已回退到上一页。")

                    # 步骤 7: 检查是否出现 "Oops!" 错误
                    if "Oops!, something went wrong" in driver.page_source:
                        print("检测到 'Oops!, something went wrong' 错误。")
                        return "RESTART" # 返回一个特殊信号，表示需要从头开始

                    # 随机刷新
                    refresh_count = random.randint(3, 5)
                    print(f"将随机刷新 {refresh_count} 次...")
                    for i in range(refresh_count):
                        driver.refresh()
                        sleep_time = random.uniform(3, 5)
                        print(f"  第 {i+1} 次刷新后，等待 {sleep_time:.2f} 秒...")
                        time.sleep(sleep_time)
                        WebDriverWait(driver, 20).until(
                            EC.element_to_be_clickable((By.ID, "signup-button"))
                        )
                    
                    print("刷新完成，继续下一次尝试。")
                    continue # 继续 for 循环的下一次迭代

            except NoSuchElementException:
                # 如果找不到拒绝元素，说明可能成功了，跳出循环进行URL检查
                print("未检测到 'Sign-up rejected'。")
                pass

            # 步骤 8: 检查最终的 URL
            print("步骤 8: 检查当前 URL 是否为订阅页面...")
            final_url = "https://app.augmentcode.com/account/subscription"
            # 等待URL变为目标URL，最多等待10秒
            WebDriverWait(driver, 10).until(EC.url_to_be(final_url))
            
            if driver.current_url == final_url:
                print("成功！已跳转到订阅页面。")
                return "SUCCESS"
            else:
                # 如果URL不匹配，但也没有拒绝信息，可能是一个未知的状态
                print(f"警告：当前 URL 为 {driver.current_url}，与预期不符。")
                # 这种情况也视为一次失败的尝试
                driver.back() # 尝试回退以进行下一次重试
                WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.ID, "signup-button"))
                )


        except TimeoutException as e:
            print(f"在尝试注册时发生超时错误: {e}")
            # 超时也可能意味着需要重试，先回退
            try:
                driver.back()
                WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.ID, "signup-button"))
                )
            except Exception as back_e:
                print(f"回退失败: {back_e}，可能需要重启流程。")
                return "RESTART"
        except Exception as e:
            print(f"在尝试注册时发生未知错误: {e}")
            return "RESTART" # 发生其他严重错误时，最好从头开始

    print("已达到最大重试次数，注册失败。")
    return "FAILURE"


def main():
    """主函数，控制整个自动化流程"""
    # 请在这里替换为您的真实信息
    USERNAME = "<EMAIL>"
    EMAIL_CODE = "123456" # 这是一个示例验证码

    while True:
        driver = None
        try:
            driver = setup_driver()
            
            if not login(driver, USERNAME):
                # 如果登录失败，则没有必要继续
                raise Exception("登录步骤失败")

            if not verify_email(driver, EMAIL_CODE):
                # 如果验证失败，则没有必要继续
                raise Exception("邮箱验证步骤失败")

            # 进入最关键的注册和重试步骤
            result = attempt_signup_with_retry(driver)

            if result == "SUCCESS":
                print("\n自动化流程成功完成！")
                break # 成功，跳出 while 循环
            elif result == "RESTART":
                print("\n检测到严重错误，将关闭浏览器并重新开始整个流程...")
                # continue 会自动进入下一次 while 循环
            else: # result == "FAILURE"
                print("\n所有重试均告失败，流程终止。")
                break # 失败，跳出 while 循环

        except Exception as e:
            print(f"\n主流程中发生严重错误: {e}")
            print("准备重启流程...")
        finally:
            if driver:
                driver.quit()
            # 如果需要重启，在外层循环的下一次迭代开始前等待一下
            time.sleep(5)


if __name__ == "__main__":
    main()
