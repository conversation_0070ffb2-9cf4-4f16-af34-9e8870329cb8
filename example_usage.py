#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RoxyD 反检测功能使用示例
展示如何在不修改原有setup_driver函数的情况下使用反检测功能
"""

import os
import time
from dotenv import load_dotenv

def example_basic_usage():
    """基本使用示例 - 使用主函数"""
    print("=== 基本使用示例 ===")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查必要的环境变量
    required_vars = ["ROXYBRWOSER_ID", "ROXYWORK_ID", "ROXYTOKEN"]
    for var in required_vars:
        if not os.getenv(var):
            print(f"错误: 缺少环境变量 {var}")
            return False
    
    try:
        # 直接运行主函数（已内置反检测）
        from RoxyD import main
        main()
        return True
    except Exception as e:
        print(f"运行出错: {e}")
        return False

def example_custom_usage():
    """自定义使用示例 - 手动控制反检测"""
    print("=== 自定义使用示例 ===")
    
    # 加载环境变量
    load_dotenv()
    
    try:
        # 导入必要的函数
        from RoxyD import setup_driver, apply_anti_detection, simulate_human_behavior
        
        # 1. 创建driver（使用原有函数，未修改）
        print("1. 创建浏览器实例...")
        driver = setup_driver()
        
        # 2. 应用反检测机制
        print("2. 应用反检测机制...")
        if apply_anti_detection(driver):
            print("   ✅ 反检测机制应用成功")
        else:
            print("   ❌ 反检测机制应用失败")
        
        # 3. 访问测试页面
        print("3. 访问测试页面...")
        driver.get("https://bot.sannysoft.com/")
        
        # 4. 模拟人类行为
        print("4. 模拟人类行为...")
        simulate_human_behavior(driver)
        
        # 5. 检查反检测效果
        print("5. 检查反检测效果...")
        webdriver_detected = driver.execute_script("return navigator.webdriver")
        print(f"   navigator.webdriver: {webdriver_detected}")
        
        languages = driver.execute_script("return navigator.languages")
        print(f"   navigator.languages: {languages}")
        
        hardware = driver.execute_script("return navigator.hardwareConcurrency")
        print(f"   navigator.hardwareConcurrency: {hardware}")
        
        # 6. 等待用户查看
        print("\n请查看浏览器页面检查反检测效果...")
        input("按回车键继续...")
        
        # 7. 清理
        driver.quit()
        print("✅ 示例完成")
        return True
        
    except Exception as e:
        print(f"自定义使用出错: {e}")
        return False

def example_step_by_step():
    """分步骤使用示例 - 展示每个步骤"""
    print("=== 分步骤使用示例 ===")
    
    load_dotenv()
    
    try:
        from RoxyD import (
            setup_driver, 
            apply_anti_detection, 
            simulate_human_behavior,
            human_like_typing,
            human_like_mouse_move_and_click
        )
        from selenium.webdriver.common.by import By
        
        # 步骤1: 初始化
        print("步骤1: 初始化浏览器...")
        driver = setup_driver()
        
        # 步骤2: 反检测
        print("步骤2: 应用反检测...")
        apply_anti_detection(driver)
        
        # 步骤3: 访问页面
        print("步骤3: 访问Google...")
        driver.get("https://www.google.com")
        time.sleep(2)
        
        # 步骤4: 人类行为模拟
        print("步骤4: 模拟人类行为...")
        simulate_human_behavior(driver)
        
        # 步骤5: 人类化输入
        try:
            print("步骤5: 人类化输入测试...")
            search_box = driver.find_element(By.NAME, "q")
            human_like_typing(search_box, "selenium test")
            time.sleep(2)
            print("   ✅ 输入测试完成")
        except Exception as e:
            print(f"   ⚠️ 输入测试跳过: {e}")
        
        # 步骤6: 等待和清理
        print("步骤6: 等待用户确认...")
        input("按回车键关闭浏览器...")
        
        driver.quit()
        print("✅ 分步骤示例完成")
        return True
        
    except Exception as e:
        print(f"分步骤使用出错: {e}")
        return False

def main():
    """主函数 - 运行所有示例"""
    print("RoxyD 反检测功能使用示例")
    print("=" * 50)
    
    # 检查环境
    if not all(os.getenv(var) for var in ["ROXYBRWOSER_ID", "ROXYWORK_ID", "ROXYTOKEN"]):
        print("❌ 请先设置必要的环境变量:")
        print("   - ROXYBRWOSER_ID")
        print("   - ROXYWORK_ID") 
        print("   - ROXYTOKEN")
        print("\n可以创建 .env 文件或设置系统环境变量")
        return
    
    while True:
        print("\n请选择示例:")
        print("1. 基本使用示例（运行完整注册流程）")
        print("2. 自定义使用示例（手动控制反检测）")
        print("3. 分步骤使用示例（详细展示每个步骤）")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == "0":
            print("退出程序")
            break
        elif choice == "1":
            example_basic_usage()
        elif choice == "2":
            example_custom_usage()
        elif choice == "3":
            example_step_by_step()
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
