2025-08-18 16:26:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57963,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:26:40 - INFO - 127.0.0.1:57963
2025-08-18 16:26:40 - INFO - <EMAIL>
2025-08-18 16:26:40 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:27:04 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:27:09 - INFO - 找到 Turnstile...
2025-08-18 16:27:12 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:27:16 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:27:25 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:27:25 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:27:36 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:27:36 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:27:36 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:27:36 - INFO - 成功！当前在订阅页面。
2025-08-18 16:27:36 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:27:36 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:27:36 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:27:39 - INFO - 代码信息: {
  "codeVerifier": "_qcHMmssQnYj_ATMd2VN6QNcr9OzOJ7M7HOkj57mV-E",
  "code_challenge": "sP8tTdNtleEvNW3_-Iv3OREDJj86g3tr6UOgttHpIdk",
  "state": "f3cffba5-3887-4583-9c4a-1e33108ae534"
}
2025-08-18 16:27:39 - INFO - ==================================================
2025-08-18 16:27:41 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_3b1e078ee89853b926a1fb1639c3ea0e&state=f3cffba5-3887-4583-9c4a-1e33108ae534&tenant_url=https%3A%2F%2Fd11.api.augmentcode.com%2F
2025-08-18 16:27:43 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:27:43 - INFO - 添加session: <EMAIL>   b1e4a49780d774f33132eaf95ccb6cc403ccc749781d8241aae56d4ecab7bfe9   https://d11.api.augmentcode.com/  2025-08-25T08:27:28Z
2025-08-18 16:27:43 - INFO - email:<EMAIL> === cookie:.eJxNzUsOwjAMBNC7eN2i5ufGrLhJFBKDIpoU0haJ390pbGDp0czzA9yZa_aFywzbuS7cwMHnNNxc8ZlhC9DAMV25_N0pnt0ycXUprgFnn4YnWi9ZRdZKRHuQqo8og0Fe62UsYV0KoZE6LYy0lqRCktjAl_kKq3Th8V5Fb4zpjKF-N2Vf5ziGE9fNWIZUPtrvsWIrLPWhpW5PrdYoW08CW6KOpA4iBjTwegO_3kRn.aKLj_A.QC5WfOCmB07BUwMD9zmUtw33bKY
2025-08-18 16:27:43 - INFO - 
自动化流程成功完成！
2025-08-18 16:27:43 - INFO - 添加第1个
2025-08-18 16:28:00 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58187,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:28:00 - INFO - 127.0.0.1:58187
2025-08-18 16:28:00 - INFO - <EMAIL>
2025-08-18 16:28:00 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:28:25 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:28:29 - INFO - 找到 Turnstile...
2025-08-18 16:28:29 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:28:32 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:28:48 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:28:48 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:29:00 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:29:00 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:29:00 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:29:00 - INFO - 成功！当前在订阅页面。
2025-08-18 16:29:00 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:29:00 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:29:00 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:29:03 - INFO - 代码信息: {
  "codeVerifier": "QyxTD5FbAjZ1hUGD5mxiWvnHSMI4qwT_OB3oMQFvfUc",
  "code_challenge": "0Mk29QeahzvKDF9WGoPtH2eDJhA21utwAF01FOVQ2rE",
  "state": "f523e487-0ae3-4959-89e1-c0b780768ed4"
}
2025-08-18 16:29:03 - INFO - ==================================================
2025-08-18 16:29:04 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_00ad1212afa4c36304a02c36e3be1638&state=f523e487-0ae3-4959-89e1-c0b780768ed4&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-18 16:29:08 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:29:08 - INFO - 添加session: <EMAIL>   fc094cd0c829a6fa47bc975d01528cf67e98c23bc567e2ec0a6796afe74510f8   https://d5.api.augmentcode.com/  2025-08-25T08:28:50Z
2025-08-18 16:29:08 - INFO - email:<EMAIL> === cookie:.eJxNjc1uwzAMg99F52SoY8l2euqbBEIsD8ZipXDSP6x993m9bAAvJMiP3zCdpRZW0R2Oe71IB4lLXh6TchE4AnTwma-i_3yO5-mySZ1ybIEUzsvTBR4Em6yJIQ3WR-fnlKTVddW5LXGkgIjWGu8ckg0WO3hj3oRG2m8a78YT0YGcd6etcN3jOn9J_Vh1yfpL-zv2oxCyTX2ajfRIbPsRh9CPyUeSaFjwAK8f8kBFuQ.aKLkUA.e4eBbGQvcemmn5G0I7zdrgfqMhM
2025-08-18 16:29:08 - INFO - 
自动化流程成功完成！
2025-08-18 16:29:08 - INFO - 添加第2个
2025-08-18 16:29:26 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58477,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:29:26 - INFO - 127.0.0.1:58477
2025-08-18 16:29:27 - INFO - <EMAIL>
2025-08-18 16:29:27 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:29:51 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:29:55 - INFO - 找到 Turnstile...
2025-08-18 16:29:57 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:30:00 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:30:12 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:30:12 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:30:31 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:30:31 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:30:31 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:30:31 - INFO - 成功！当前在订阅页面。
2025-08-18 16:30:31 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:30:31 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:30:31 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:30:34 - INFO - 代码信息: {
  "codeVerifier": "2decHQ3NSznlb48xEEGeQco-lintMSXJliW7wLJd3fY",
  "code_challenge": "PQQ_sOfLH_xAGE21dtF6HllCitA6WCISWT143owSPIw",
  "state": "28cfc28b-b2a2-4cae-a993-4d1762572972"
}
2025-08-18 16:30:34 - INFO - ==================================================
2025-08-18 16:30:35 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_673639064ac3a2338030464d740f6911&state=28cfc28b-b2a2-4cae-a993-4d1762572972&tenant_url=https%3A%2F%2Fd17.api.augmentcode.com%2F
2025-08-18 16:30:37 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:30:37 - INFO - 添加session: <EMAIL>   819b5de0fe8adc2b01ccbfc3f958906412870296479d82f1caac16eed63aef61   https://d17.api.augmentcode.com/  2025-08-25T08:30:14Z
2025-08-18 16:30:37 - INFO - email:<EMAIL> === cookie:.eJxNjs0OgjAQhN9lz2Bol_558k1ISxfTSBctaGLUd7chHjzOZOabecFwpZI9E29w3MqdGph8TvNzYJ8JjgANnNOD-E-neB3uK5UhxWpQ9ml-a-sl9RZ7FNFOEk3UUQSNNc4Lj7WpDEqjHcoOXa8VStvATtkBFTRS4pswSqlOGS1Pa_Zli8t4oXJYeE5M8Gvsu8aKzgSt2uDRtb2Qog1TFK0Lwttgg6t34PMFSuNEVw.aKLkqw.QUyxNKZuX2JJWPp3-4e--kJ2gAU
2025-08-18 16:30:37 - INFO - 
自动化流程成功完成！
2025-08-18 16:30:37 - INFO - 添加第3个
2025-08-18 16:31:01 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58881,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:31:01 - INFO - 127.0.0.1:58881
2025-08-18 16:31:01 - INFO - <EMAIL>
2025-08-18 16:31:01 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:31:31 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:31:35 - INFO - 找到 Turnstile...
2025-08-18 16:31:37 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:31:40 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:31:52 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:31:53 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:32:14 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:32:14 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:32:14 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:32:14 - INFO - 成功！当前在订阅页面。
2025-08-18 16:32:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:32:14 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:32:14 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:32:17 - INFO - 代码信息: {
  "codeVerifier": "q5u8mlKvkhN_WdkC9SwX2ynFqIyaeT8dXpsISgg09tg",
  "code_challenge": "fR1mRkm6Inju-MRczn6PQdcn4kp0GAKRgyeqPrY-zNQ",
  "state": "6ab2da38-db4a-4742-adac-9f6113759bde"
}
2025-08-18 16:32:17 - INFO - ==================================================
2025-08-18 16:32:18 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_0ace7335f149d73f2a000c8bca2a1b7c&state=6ab2da38-db4a-4742-adac-9f6113759bde&tenant_url=https%3A%2F%2Fd15.api.augmentcode.com%2F
2025-08-18 16:32:20 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:32:20 - INFO - 添加session: <EMAIL>   2f8d644b7b633d5db4f5687854d8d77324d1eb3583afa546f9e3caa76c5d767c   https://d15.api.augmentcode.com/  2025-08-25T08:31:55Z
2025-08-18 16:32:20 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwjAQRP9lzwnCXq_tcOJPIie7QVbjDTKhKgL-nTTqoccZzXvzhP4qtSQVXeG01rs0MKWS50evqQicABq45G_Rfznztb_fpPaZt0JKyvPLx2TFSXRoOE4WAwdkJ8M210XHjbSm885HOpJx6I8meN_ArtkNvya9LD8mEG2bSHS-lVRXXsYvqYdF56wCf8R-zGQZEceWUKbWxeTbbqChReJoTYgknYH3B8bMRNY.aKLlEg.ij_iQGnqC81n7dxdY_-WnLBPzHw
2025-08-18 16:32:20 - INFO - 
自动化流程成功完成！
2025-08-18 16:32:20 - INFO - 添加第4个
2025-08-18 16:32:52 - INFO - 请求出现错误: HTTPSConnectionPool(host='es.slogo.eu.org', port=443): Max retries exceeded with url: /admin/new_address (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000024BFFEED950>, 'Connection to *********** timed out. (connect timeout=None)')))
2025-08-18 16:32:52 - INFO - 添加第5个
2025-08-18 16:33:30 - INFO - 请求出现错误: HTTPSConnectionPool(host='es.slogo.eu.org', port=443): Max retries exceeded with url: /admin/new_address (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000024BFFF68550>, 'Connection to *********** timed out. (connect timeout=None)')))
2025-08-18 16:33:30 - INFO - 添加第6个
2025-08-18 16:34:03 - INFO - 请求出现错误: HTTPSConnectionPool(host='es.slogo.eu.org', port=443): Max retries exceeded with url: /admin/new_address (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000024BFFF68690>, 'Connection to *********** timed out. (connect timeout=None)')))
2025-08-18 16:34:03 - INFO - 添加第7个
2025-08-18 16:34:36 - INFO - 请求出现错误: HTTPSConnectionPool(host='es.slogo.eu.org', port=443): Max retries exceeded with url: /admin/new_address (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000024BFFF68910>, 'Connection to *********** timed out. (connect timeout=None)')))
2025-08-18 16:34:36 - INFO - 添加第8个
2025-08-18 16:35:07 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59141,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:35:07 - INFO - 127.0.0.1:59141
2025-08-18 16:35:08 - INFO - <EMAIL>
2025-08-18 16:35:08 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:35:50 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_PROXY_CONNECTION_FAILED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:35:50 - INFO - 准备重启流程...
2025-08-18 16:36:02 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59194,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:36:02 - INFO - 127.0.0.1:59194
2025-08-18 16:36:02 - INFO - <EMAIL>
2025-08-18 16:36:02 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:36:45 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_PROXY_CONNECTION_FAILED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:36:45 - INFO - 准备重启流程...
2025-08-18 16:36:56 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59346,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:36:56 - INFO - 127.0.0.1:59346
2025-08-18 16:36:57 - INFO - <EMAIL>
2025-08-18 16:36:57 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:37:46 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:37:50 - INFO - 找到 Turnstile...
2025-08-18 16:37:54 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:38:16 - INFO - 
主流程中发生严重错误: HTTPSConnectionPool(host='es.slogo.eu.org', port=443): Max retries exceeded with url: /api/mails?limit=10&offset=0 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='es.slogo.eu.org', port=443): Read timed out. (read timeout=None)"))
2025-08-18 16:38:16 - INFO - 准备重启流程...
2025-08-18 16:38:30 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59528,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:38:30 - INFO - 127.0.0.1:59528
2025-08-18 16:38:30 - INFO - <EMAIL>
2025-08-18 16:38:30 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:39:00 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:39:04 - INFO - 找到 Turnstile...
2025-08-18 16:39:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:39:11 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:39:23 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:39:23 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:39:54 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 16:39:54 - INFO - 鼠标左键放开操作完成。
2025-08-18 16:39:54 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 16:39:55 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 16:39:58 - INFO - 验证成功，已进入目标页面。
2025-08-18 16:39:58 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:39:58 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:39:58 - INFO - 成功！当前在订阅页面。
2025-08-18 16:39:58 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:39:58 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:40:08 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 16:40:11 - INFO - 代码信息: {
  "codeVerifier": "o0N5138hXYgiRFAQnLjhmnT87L-KAw4Qrmk17I7NtO0",
  "code_challenge": "-2dFrq9hlbLic_P6YSx6p7WgjfOdv9WF-bYPCq1N-W0",
  "state": "5239d07d-5dde-419f-ace0-133aea011500"
}
2025-08-18 16:40:11 - INFO - ==================================================
2025-08-18 16:40:12 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_b2570c20b45247a64d2a42e8a76c1933&state=5239d07d-5dde-419f-ace0-133aea011500&tenant_url=https%3A%2F%2Fd15.api.augmentcode.com%2F
2025-08-18 16:40:14 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:40:14 - INFO - 添加session: <EMAIL>   cb721eeeeae2cf1e9bd287173506121453cb150a1f5b53f5a6545829a0efc26c   https://d15.api.augmentcode.com/  2025-08-25T08:39:27Z
2025-08-18 16:40:14 - INFO - email:<EMAIL> === cookie:.eJxNzUEOwiAQBdC7zLo1wAAFV96kAWZqiIUarCZGvbvVjS7n5_83DxjP3EqoXFfYr-3KHUyh5Pk-1lAY9gAdHPON69-d6TxeL9zGTFvAJeT5aV1QbK3QKMlNCgdyUWvFW70uNW1LVIN3g5NCIEqvtLOigy_zFTZpPYYc5WCMEVZ4PFxKaCst6cRtt9Q514_2e5wQUzTEPaZkeq2k7WOafB9JSfKT11YRvN6YFEU2.aKLm7A.FnBacoUdb-ZtIwcHCHZqvydvEao
2025-08-18 16:40:14 - INFO - 
自动化流程成功完成！
2025-08-18 16:40:14 - INFO - 添加第9个
2025-08-18 16:40:30 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59833,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:40:30 - INFO - 127.0.0.1:59833
2025-08-18 16:40:31 - INFO - <EMAIL>
2025-08-18 16:40:31 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:41:03 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:41:07 - INFO - 找到 Turnstile...
2025-08-18 16:41:10 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:41:14 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:41:25 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:41:25 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:41:35 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:41:35 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:41:35 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:41:35 - INFO - 成功！当前在订阅页面。
2025-08-18 16:41:35 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:41:35 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:41:35 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:41:38 - INFO - 代码信息: {
  "codeVerifier": "-XBnWn6MN87paDEy7m-Phq_Sumf9kt8flDec-Lj6VfM",
  "code_challenge": "jQiufwfDQHDO85XxDzBceAQSZzSQa3TR9cdOFXkXuBs",
  "state": "a52afd66-f935-48c8-bc9d-f24a10f6f748"
}
2025-08-18 16:41:38 - INFO - ==================================================
2025-08-18 16:41:39 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_79c1407d6d5b1143f64e2129f6aaa041&state=a52afd66-f935-48c8-bc9d-f24a10f6f748&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 16:41:41 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:41:41 - INFO - 添加session: <EMAIL>   56ac840a9935cd5c9eccaa68b89230edec77686b1d148e8381e3b798e1c13dd8   https://d9.api.augmentcode.com/  2025-08-25T08:41:28Z
2025-08-18 16:41:41 - INFO - email:<EMAIL> === cookie:.eJxNjc1uwyAQhN9lz3aFYfnLKW9ircNSoRqIsBMlTfruRVYPPc5ovm9eMF-5ZSpcdjjt7cYDRMppfc6FMsMJYIDPdOfyL6dwnW8btzmFXnCmtL6NI8lWIqopuCiVDd5GVLrPSy2XTkrntXaIk_LST8KrAQ7JwXdP-37UOlmttTAozXnL1PZQL1_cPmpZU2H4I45bXIxZhMBxsRRHdAuOLjjfI5poSGsyAn5-ARYURDQ.aKLnQw.PMtrXfMsKZfdO9I-V1KzhohjNMw
2025-08-18 16:41:41 - INFO - 
自动化流程成功完成！
2025-08-18 16:41:41 - INFO - 添加第10个
2025-08-18 16:42:03 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60249,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:42:03 - INFO - 127.0.0.1:60249
2025-08-18 16:42:03 - INFO - <EMAIL>
2025-08-18 16:42:03 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:42:28 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:42:32 - INFO - 找到 Turnstile...
2025-08-18 16:42:34 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:42:37 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:42:51 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:42:51 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:42:51 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:42:51 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:42:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:42:59 - INFO -    等待 4.30 秒...
2025-08-18 16:43:08 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 16:43:08 - INFO - 
第 2/5 次尝试注册...
2025-08-18 16:43:08 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:43:08 - INFO - 成功！当前在订阅页面。
2025-08-18 16:43:08 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:43:08 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:43:08 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:43:11 - INFO - 代码信息: {
  "codeVerifier": "0usEbdfdMrwFSD9FkniEQVNC4rNgHVwM6HeK1JKz7QA",
  "code_challenge": "AiUdjA3lrddfoknw8JccQZHTl2HfJeD6gJsvwIgoJWA",
  "state": "ab88bfb4-fff5-447a-89ac-d853e35cb5e0"
}
2025-08-18 16:43:11 - INFO - ==================================================
2025-08-18 16:43:13 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_d8b410caebe5c35ed5a3b7768a639c7f&state=ab88bfb4-fff5-447a-89ac-d853e35cb5e0&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 16:43:14 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:43:14 - INFO - 添加session: <EMAIL>   2d22e2e1ccb8917f7c2733c39fa40b19468e3812818ce5f6f2c2b4138f73c3c6   https://d9.api.augmentcode.com/  2025-08-25T08:42:53Z
2025-08-18 16:43:14 - INFO - email:<EMAIL> === cookie:.eJxNjc1uhDAQg99lzrAiISGBU98EzWYmVVQyoCxU2r933wj10IslW_bnJ8wbl4zCssO0l4MbiJjTcp8FM8ME0MB3-mX55xNt83HjMieqAWdMy2vwqNm50fSKfNS9o5Fc5FDrskqoS61tb5wfjeqUU1WUaeDEnIRK2h7LJspZa7vBKv91y1h2WsMPl8sqSxKGv8V57Ec7KOxiGwNzayJyeyXqWsIhambGPiC8P-Q3RuA.aKLnoA.j7dapVYpeIvMQ9sJYf7Ued2rWk0
2025-08-18 16:43:14 - INFO - 
自动化流程成功完成！
2025-08-18 16:43:14 - INFO - 添加第11个
2025-08-18 16:43:36 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60580,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:43:36 - INFO - 127.0.0.1:60580
2025-08-18 16:43:37 - INFO - <EMAIL>
2025-08-18 16:43:37 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:44:07 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:44:11 - INFO - 找到 Turnstile...
2025-08-18 16:44:13 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:44:17 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:44:30 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:44:30 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:44:40 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:44:40 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:44:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:44:40 - INFO - 成功！当前在订阅页面。
2025-08-18 16:44:40 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:44:40 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:44:40 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:44:43 - INFO - 代码信息: {
  "codeVerifier": "JUn40gD5pSj7c206LS7Gj6iukb-AuHoryfPLmhUFhpM",
  "code_challenge": "lu2UgnJbdokef_mnsSrwXpZ10f96g0I4FUNSxwF1En4",
  "state": "724b5428-4383-4162-85ee-ea66f7cce655"
}
2025-08-18 16:44:43 - INFO - ==================================================
2025-08-18 16:44:44 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_921e39c73c43a2cb8ec65df2f9266063&state=724b5428-4383-4162-85ee-ea66f7cce655&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-18 16:44:46 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:44:46 - INFO - 添加session: <EMAIL>   77a0228ec2c270562cc948f6de7ac460140042aca511af67c40e1873e1cec7fb   https://d10.api.augmentcode.com/  2025-08-25T08:44:33Z
2025-08-18 16:44:46 - INFO - email:<EMAIL> === cookie:.eJxNjsuKwzAMRf9F66TYll_pqn8S1EjpmImd4qYDYWb-vSZ0UbgbCZ2j-wvjXWqmImWD81af0sFMOS37WCgLnAE6uKUfKR9z4vv4fEgdE7eFZErLn49kJPBkUXOcDQYm5KConZe1TI1Er-yAGiNG62yLVx0cmsPQTCl_7aKDc055r83lkaluvE7fUk9rWVIReBPH40FbMcGpfubZ9TaK768auTfRK9XaEKOD_xfT8UUM.aKLn_A.04G0izvBvFQCLVVJyAOXYdw5WCo
2025-08-18 16:44:46 - INFO - 
自动化流程成功完成！
2025-08-18 16:44:46 - INFO - 添加第12个
2025-08-18 16:45:11 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60852,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:45:11 - INFO - 127.0.0.1:60852
2025-08-18 16:45:11 - INFO - <EMAIL>
2025-08-18 16:45:11 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:45:40 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:45:44 - INFO - 找到 Turnstile...
2025-08-18 16:45:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:45:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:46:01 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:46:01 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:46:12 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:46:12 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:46:12 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:46:12 - INFO - 成功！当前在订阅页面。
2025-08-18 16:46:12 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:46:12 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:46:19 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:46:22 - INFO - 代码信息: {
  "codeVerifier": "Woy6cS3-Cnjb6qgXBpomAlmgfwMJq2vCVbmMxDT25Qk",
  "code_challenge": "UKOcw5hLjbHnee90XZn-D4nJQOeatckZuncBFUvxAds",
  "state": "61171e62-fcc9-4b4c-9fa0-04438745fd58"
}
2025-08-18 16:46:22 - INFO - ==================================================
2025-08-18 16:46:23 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_8b4f61e0821286b997b699f17c8e60e9&state=61171e62-fcc9-4b4c-9fa0-04438745fd58&tenant_url=https%3A%2F%2Fd14.api.augmentcode.com%2F
2025-08-18 16:46:25 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:46:25 - INFO - 添加session: <EMAIL>   7ee4a6d687e00465b7d2dc06420366e788fb3921fc15f1299bca814581ae268a   https://d14.api.augmentcode.com/  2025-08-25T08:46:04Z
2025-08-18 16:46:25 - INFO - email:<EMAIL> === cookie:.eJxNjU1uwyAQhe8yazsCY2Amq9zEmoZxhWrGEXaiRE3vXmR10eX7-943TDephVV0h_Ne79LBzCUvr0m5CJwBOvjMD9F_OqfbdN-kTjk1Qwrn5R2QB0FHo7MJ58HFxJTIhFbXVa9t2RKLcTSIhpAG23QHB-YgNBIvz6I2eu9NiCZctsJ1T-v1S-pp1SWrwN_iOCYf0DdOj9Z99GOQsacZh94RGTRe2NoIP7973EOP.aKLoXw.WLfHljEVgmLXbgdIZhHAqT2GjdI
2025-08-18 16:46:25 - INFO - 
自动化流程成功完成！
2025-08-18 16:46:25 - INFO - 添加第13个
2025-08-18 16:46:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61035,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:46:40 - INFO - 127.0.0.1:61035
2025-08-18 16:46:40 - INFO - <EMAIL>
2025-08-18 16:46:40 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:47:11 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:47:15 - INFO - 找到 Turnstile...
2025-08-18 16:47:17 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:47:21 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:47:33 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:47:33 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:47:34 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:47:34 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:47:34 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:47:42 - INFO -    等待 3.19 秒...
2025-08-18 16:47:51 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 16:47:51 - INFO - 
第 2/5 次尝试注册...
2025-08-18 16:47:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:47:51 - INFO - 成功！当前在订阅页面。
2025-08-18 16:47:51 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:47:51 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:47:51 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:47:54 - INFO - 代码信息: {
  "codeVerifier": "OxMuqEMF6jIUGBxrMqJEfqCrJ8W2C7VSgaHFKxL_KTs",
  "code_challenge": "RUF6Mv9o9zcu69w_I97COo6So_iQpsyP6Q8r7YsN93Y",
  "state": "053c506a-caa0-4046-b580-ccadea5dcc26"
}
2025-08-18 16:47:54 - INFO - ==================================================
2025-08-18 16:47:55 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_99d8b7d31ca5c5cedd44b4e077a5ef42&state=053c506a-caa0-4046-b580-ccadea5dcc26&tenant_url=https%3A%2F%2Fd14.api.augmentcode.com%2F
2025-08-18 16:47:57 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:47:57 - INFO - 添加session: <EMAIL>   01b5bd4056ba76228adec2754245cd9ebb03b28316bbd754cf5726701dd8c2f2   https://d14.api.augmentcode.com/  2025-08-25T08:47:35Z
2025-08-18 16:47:57 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgyAQhN9lz9oILLD21DcxG1kaUsEGbZP-vXuN6aHHmcz3zQuGq9TMRcoKx7XepIHIOU2PoXAWOAI0cE53KX85hetwW6QOKWyFZE7T2xFroR7RqEBRGx84Mira5mUu40Z6VN440qQVmp46craBXbMbNtP0rLwob63tnO_taclc1zCPF6mHuUypCPyI_ThGb2Inqh2jYIvO2JbCaFr0xkdt-8Co4fMFwUxFIg.aKLouw.x5fYEFvsecHOjfdvuVyEx_RWzno
2025-08-18 16:47:57 - INFO - 
自动化流程成功完成！
2025-08-18 16:47:57 - INFO - 添加第14个
2025-08-18 16:48:13 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61323,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:48:13 - INFO - 127.0.0.1:61323
2025-08-18 16:48:14 - INFO - <EMAIL>
2025-08-18 16:48:14 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:48:45 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:48:49 - INFO - 找到 Turnstile...
2025-08-18 16:48:51 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:48:54 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:49:05 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:49:05 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:49:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:49:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:49:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:49:21 - INFO - 成功！当前在订阅页面。
2025-08-18 16:49:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:49:21 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:49:21 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:49:24 - INFO - 代码信息: {
  "codeVerifier": "w8gUkhAckkCGRYLKYxkWbXd12SdUpLMX8voFG34GNms",
  "code_challenge": "hAAZlU5SoBT0xvjqIxx1EEIDHyed9use_m_-2ivKnfo",
  "state": "93f28186-ae57-4e9c-a101-81750cc10320"
}
2025-08-18 16:49:24 - INFO - ==================================================
2025-08-18 16:49:26 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_9883eb78be52e795138a3083794ac241&state=93f28186-ae57-4e9c-a101-81750cc10320&tenant_url=https%3A%2F%2Fd7.api.augmentcode.com%2F
2025-08-18 16:49:27 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:49:27 - INFO - 添加session: <EMAIL>   a423f588c01fbfa6fa170cbb23138f26f5a5d132016f145785e16b6eed09f404   https://d7.api.augmentcode.com/  2025-08-25T08:49:08Z
2025-08-18 16:49:27 - INFO - email:<EMAIL> === cookie:.eJxNjd0OgjAMRt-l12AG--nmlW9CiitkygYZYGLUd3chXnjV9st3Tl_QLZwjJU4bnLe8cwUDxTA9u0SR4QxQwRgenP7u4JduXzl3wZeAI4XpbSy1bIdWycaXIdH32g0tlnqa07WQaNGgM1ZapVwj0QlRwaE5DMWUx-k2Nqi1FsZad1kj5c3P1zvn05ymkBh-xPHYkZeSvaiV9FwrhVQ7I8rWYE9SOG36Fj5fv91E4A.aKLpFA.ldl_UuJwO_czaHjF7pHeXD4bf4U
2025-08-18 16:49:27 - INFO - 
自动化流程成功完成！
2025-08-18 16:49:27 - INFO - 添加第15个
2025-08-18 16:49:42 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61589,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:49:42 - INFO - 127.0.0.1:61589
2025-08-18 16:49:42 - INFO - <EMAIL>
2025-08-18 16:49:42 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:50:26 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:50:29 - INFO - 找到 Turnstile...
2025-08-18 16:50:31 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:50:35 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:50:46 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:50:46 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:50:57 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:50:57 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:50:57 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:50:57 - INFO - 成功！当前在订阅页面。
2025-08-18 16:50:57 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:50:57 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:50:58 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:51:01 - INFO - 代码信息: {
  "codeVerifier": "-IbwUZ4GOoOMcDHJ48JpdU43yrCtH45_ds1e6_WyvMI",
  "code_challenge": "SvxTPlu8NE8bSYtZbgOwJYrkvvOJM0cE4qkGUnDGd6c",
  "state": "91869008-36fc-4e57-896a-3bf617bd7b58"
}
2025-08-18 16:51:01 - INFO - ==================================================
2025-08-18 16:51:01 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_e3c497d6142afc7caf50a2eb6355b967&state=91869008-36fc-4e57-896a-3bf617bd7b58&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 16:51:03 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:51:03 - INFO - 添加session: <EMAIL>   6982f29da954f4edb54602717f515bee814aa239e5c02e22f62908085dbf6130   https://d9.api.augmentcode.com/  2025-08-25T08:50:50Z
2025-08-18 16:51:03 - INFO - email:<EMAIL> === cookie:.eJxNjcEOwiAQRP9lz61hgS3gyT9p1rI1xEINrSZG_XebxoPHmcx784L-JjVzkbLCca13aWDknKZnXzgLHAEauKSHlL-c4q2_L1L7FLdCMqfp3XnWEqizBqMftXHxPBgKapuXuQwb6Sig1cY6hUhaK6dMA7tmN2ymha_PER0RqS44f1oy1zXOw1XqYS5TKgI_Yj_2ZK33GFpnULUWmVtvutjGMVoKHlExw-cLd6JD_g.aKLpdQ.h0eK-4_wQdSh8Vny7KdkbrkOSnY
2025-08-18 16:51:03 - INFO - 
自动化流程成功完成！
2025-08-18 16:51:03 - INFO - 添加第16个
2025-08-18 16:51:25 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61781,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:51:25 - INFO - 127.0.0.1:61781
2025-08-18 16:51:25 - INFO - <EMAIL>
2025-08-18 16:51:25 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:51:56 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:52:00 - INFO - 找到 Turnstile...
2025-08-18 16:52:02 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:52:05 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:52:18 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:52:18 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:52:42 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:52:42 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:52:42 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:52:42 - INFO - 成功！当前在订阅页面。
2025-08-18 16:52:42 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:52:42 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:52:42 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:52:45 - INFO - 代码信息: {
  "codeVerifier": "TIgeudeUGuQjo_DET9ccxK68IBryoIhKw5PaDEz1xnk",
  "code_challenge": "M5jeKUfg3btt4pJMIy2QEc8bg6EpzNBBrrIIbBa-Ajc",
  "state": "e6baf2e8-d77f-443e-b708-66c37de12916"
}
2025-08-18 16:52:45 - INFO - ==================================================
2025-08-18 16:52:46 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_2663e31caa5e1e8a8635891ee0c6c9fd&state=e6baf2e8-d77f-443e-b708-66c37de12916&tenant_url=https%3A%2F%2Fd18.api.augmentcode.com%2F
2025-08-18 16:52:49 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:52:49 - INFO - 添加session: <EMAIL>   9b497f42a166ca3e48f779882b8669523a96f86664fa4badd2be44da71cf1a21   https://d18.api.augmentcode.com/  2025-08-25T08:52:20Z
2025-08-18 16:52:49 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2DKbv_w5JuQ0i6kkRZSwMSo7y4hHjzOZL5vXtAtXJLLnDe4bmXnCgaX4vTssksMV4AKxvjg_JdjWLp95dLFcBScXJze2jrktheSmmAHJBM8CmHomOc5-4OUpjFWKaullgatJazgtJyCQzROnlNjlFLCCCtua3JlC7O_c7nMeYqZ4Uecv0hoqZe6JiF9LYPD2oUWa-1bjQKZBib4fAFERkQM.aKLp3g.8Bf4LvTQJKnWH8ohD22wQ5zl0Y4
2025-08-18 16:52:49 - INFO - 
自动化流程成功完成！
2025-08-18 16:52:49 - INFO - 添加第17个
2025-08-18 16:53:06 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62047,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:53:06 - INFO - 127.0.0.1:62047
2025-08-18 16:53:07 - INFO - <EMAIL>
2025-08-18 16:53:07 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:53:39 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:53:43 - INFO - 找到 Turnstile...
2025-08-18 16:53:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:53:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:54:01 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:54:01 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:54:19 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:54:19 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:54:19 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:54:19 - INFO - 成功！当前在订阅页面。
2025-08-18 16:54:19 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:54:19 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:54:19 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:54:22 - INFO - 代码信息: {
  "codeVerifier": "aXb4FJQldgmZtrTuHw5HqMiMEfVp0UA5muImfwyNRxk",
  "code_challenge": "rIsNpnfOjiHdDimiEaEHe9l9rGtrDlfNY3aamKuD5tI",
  "state": "9e8a9326-89db-4cde-9c26-bc08d17494b5"
}
2025-08-18 16:54:22 - INFO - ==================================================
2025-08-18 16:54:23 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_3b7b06d346869581ee1ae2e9e3dc7a0b&state=9e8a9326-89db-4cde-9c26-bc08d17494b5&tenant_url=https%3A%2F%2Fd1.api.augmentcode.com%2F
2025-08-18 16:54:25 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:54:25 - INFO - 添加session: <EMAIL>   3e36ab31456911523862d19ae09902486a7f4a21cdab1492ee3037606c5217ac   https://d1.api.augmentcode.com/  2025-08-25T08:54:03Z
2025-08-18 16:54:25 - INFO - email:<EMAIL> === cookie:.eJxNzUGOwjAMBdC7eN2iukkThxU3qULioIjGLWlBQszcncJmZumv_59fMC5cixeWDY5bvXMDyZc8PUfxheEI0MAlP1j-3Tku433lOua4B1x8nn4M-Z49klYYKfXKxkBWW97rMkvYl1orZ9EqdNqQUz1Z08CX-Qq75CUvN7TDMHQWCU9r8XWLc7hyPcwyZflof4-jVtFwCq0zMbTaEbWEOLTnLnTGJZdMSvD7Br0IRVI.aKLqPw.A84RsiTd9NhYMisNBttgtHRMAUE
2025-08-18 16:54:25 - INFO - 
自动化流程成功完成！
2025-08-18 16:54:25 - INFO - 添加第18个
2025-08-18 16:54:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62385,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:54:40 - INFO - 127.0.0.1:62385
2025-08-18 16:54:41 - INFO - <EMAIL>
2025-08-18 16:54:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:55:09 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:55:13 - INFO - 找到 Turnstile...
2025-08-18 16:55:16 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:55:19 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:55:31 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:55:31 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:55:57 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:55:57 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:55:57 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:55:57 - INFO - 成功！当前在订阅页面。
2025-08-18 16:55:57 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:55:57 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:56:08 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 16:56:11 - INFO - 代码信息: {
  "codeVerifier": "GkbTu6dGhaA7zii1q1gKrmBXVVDJCygJNbMyWei0c80",
  "code_challenge": "f48B0tbIh4osm4fYs3r1LQ7CXk5CjlC-AbEnFTZxnPU",
  "state": "a6d776dd-8e1d-4020-b5b7-fe2c7d7a8f64"
}
2025-08-18 16:56:11 - INFO - ==================================================
2025-08-18 16:56:12 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_f7f50f2ec872610edb00bbac1cf4d24f&state=a6d776dd-8e1d-4020-b5b7-fe2c7d7a8f64&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-18 16:56:14 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:56:14 - INFO - 添加session: <EMAIL>   db08e856a2fbc9fc7069f8fbb143ba1061a44d712092e03a42f11d197c163945   https://d8.api.augmentcode.com/  2025-08-25T08:55:34Z
2025-08-18 16:56:14 - INFO - email:<EMAIL> === cookie:.eJxNjkEOgyAQRe8ya2lwAEFXvYlBGBpSQYPapGl79xLTRZfz89_784JxpZJsprzDsJeDGgg2xfk5ZpsIBoAGbvFB-e-Ofh2PjcoYfQ0o2Ti_O2ORrBZStN4EFNo7EhP1tZ6X7CqJsuctRyU49iixb41q4NSchmpaj7iEViuluEbdXbdky-4Xd6dyWfIcM8GPOIe99aicRKaFCky6yTFTQRaw43wiXp-Z4PMF35VFOg.aKLqrA.nPHzN6fVqdsak_AE65wqeRM6jxQ
2025-08-18 16:56:14 - INFO - 
自动化流程成功完成！
2025-08-18 16:56:14 - INFO - 添加第19个
2025-08-18 16:56:37 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62708,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:56:37 - INFO - 127.0.0.1:62708
2025-08-18 16:56:38 - INFO - <EMAIL>
2025-08-18 16:56:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:57:02 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:57:06 - INFO - 找到 Turnstile...
2025-08-18 16:57:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:57:11 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:57:11 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:57:11 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:57:11 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:57:27 - INFO -    等待 3.58 秒...
2025-08-18 16:57:35 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 16:57:35 - INFO - 
第 2/5 次尝试注册...
2025-08-18 16:57:35 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:57:35 - INFO - 成功！当前在订阅页面。
2025-08-18 16:57:35 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:57:35 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:57:46 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 16:57:49 - INFO - 代码信息: {
  "codeVerifier": "WHZMTOIUUUhB3QwQRnTscNGkFRwdgRz-leFohUCyBj0",
  "code_challenge": "8i3Z6gl9EMkvoH62ExT8khKoCMwbZWSauOqFzKquf28",
  "state": "baac0b80-a175-4cc7-bf88-654afdc28c9f"
}
2025-08-18 16:57:49 - INFO - ==================================================
2025-08-18 16:57:50 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_d1babe32bd2519f83ee60f8bfb677cb3&state=baac0b80-a175-4cc7-bf88-654afdc28c9f&tenant_url=https%3A%2F%2Fd16.api.augmentcode.com%2F
2025-08-18 16:57:52 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:57:52 - INFO - 添加session: <EMAIL>   b22d63ee3a6d14b32cc09871cb875bad18c7a07d281d5b88ac440ef3e1e17950   https://d16.api.augmentcode.com/  2025-08-25T08:57:29Z
2025-08-18 16:57:52 - INFO - email:<EMAIL> === cookie:.eJxNjktuwzAMBe_CtR1Y1pdZ5SYGY1KtEks2FCdA0PbuFYwuunkACc7wfcG0Sc1UpOxw3utTOoiU0_KeCmWBM0AHH-kl5d-ceJueD6lT4raQTGn5doFGIRmNVhziqD2zRUWqnZe1zI0MGCxq77zRLdWotevg0ByGZsqft3lV3lo7eI368shUd17nu9TTWpZUBP6I4zEG4sjx2tvBxN64wfaISL1tTdiRQQ4D_PwC0-dFYg.aKLrDg.6w5cb2SoAHtesJ8Q4MXPWjbkXxc
2025-08-18 16:57:52 - INFO - 
自动化流程成功完成！
2025-08-18 16:57:52 - INFO - 添加第20个
2025-08-18 16:58:12 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63115,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 16:58:12 - INFO - 127.0.0.1:63115
2025-08-18 16:58:12 - INFO - <EMAIL>
2025-08-18 16:58:12 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 16:58:36 - INFO - 获取 cap_value 验证码成功...
2025-08-18 16:58:40 - INFO - 找到 Turnstile...
2025-08-18 16:58:42 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 16:58:45 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 16:58:56 - INFO - 验证码已提交，等待跳转...
2025-08-18 16:58:56 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 16:59:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 16:59:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 16:59:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 16:59:21 - INFO - 成功！当前在订阅页面。
2025-08-18 16:59:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 16:59:21 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 16:59:32 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 16:59:35 - INFO - 代码信息: {
  "codeVerifier": "qvkPQ-_eOMGcVMEiSCxn5fDLNCwYmfJZhHdtiiuilbs",
  "code_challenge": "kEYI79DepN-7KQTnW_sZtCwSI-WGMZRyXOqDL_7zamM",
  "state": "ad8189c3-02f8-4403-aa0d-302b0c3105d1"
}
2025-08-18 16:59:35 - INFO - ==================================================
2025-08-18 16:59:36 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_312cd677ffe813ab25ef3063c09c32b0&state=ad8189c3-02f8-4403-aa0d-302b0c3105d1&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-18 16:59:38 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 16:59:38 - INFO - 添加session: <EMAIL>   80bbe89f5232608daa55dac1ece8865daaab443776ee99e816397d0b22d420cb   https://d6.api.augmentcode.com/  2025-08-25T08:58:59Z
2025-08-18 16:59:38 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtV1I1odSVrmJQUtUIdSiA8UJULS9ew0jiyxnMO_ND8w37o2EZYfL3h88QKFW1-9ZqDFcAAb4rE-Wt1zzbX7cuc81HwU3quuvDzTxYpU1OocyGcx5CS66Yy6bpIP0IXqjHeoJMVqtgpoGODWn4TAlobVqdM4ptAGv90Z9z1v64v6xyVqF4UWcx4vXjJbyWDD60UbN4-JTHAsltoZNScrD3z-1X0WD.aKLrdw.H-idWnnaOe1UA97GycUjCKKrn_s
2025-08-18 16:59:38 - INFO - 
自动化流程成功完成！
2025-08-18 16:59:38 - INFO - 添加第21个
2025-08-18 17:00:02 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63417,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:00:02 - INFO - 127.0.0.1:63417
2025-08-18 17:00:02 - INFO - <EMAIL>
2025-08-18 17:00:02 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:00:34 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:00:38 - INFO - 找到 Turnstile...
2025-08-18 17:00:40 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:00:43 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:00:55 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:00:55 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:01:14 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:01:14 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:01:14 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:01:14 - INFO - 成功！当前在订阅页面。
2025-08-18 17:01:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:01:14 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:01:16 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:01:19 - INFO - 代码信息: {
  "codeVerifier": "ieYixctwhHiUUlRZNZ4s7pQh6UF7HyFv-t9zuryotag",
  "code_challenge": "nJ6wzQDGGb9NamdKz1AabQ1hfmoMhGCXW2OXo7LokOw",
  "state": "cb61f404-a5db-4e60-915c-53a313bcd6ae"
}
2025-08-18 17:01:19 - INFO - ==================================================
2025-08-18 17:01:19 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_6aae55c8e4ab77e2242b0f76fe7096f3&state=cb61f404-a5db-4e60-915c-53a313bcd6ae&tenant_url=https%3A%2F%2Fd4.api.augmentcode.com%2F
2025-08-18 17:01:22 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:01:22 - INFO - 添加session: <EMAIL>   f4ed99c216e7f21e3ea63c35eed75b7b409ddde362517af3c78c0c9dc532b0e5   https://d4.api.augmentcode.com/  2025-08-25T09:00:57Z
2025-08-18 17:01:22 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mBa-pjiyj8hhU61kRZSwMSo_24hLlxNZu49Z17QzZSjTZRWOK95owq8jWF8dslGgjNABdfwoPS3Bzd320K5C64cKNowvrWxDfU9SsGd8Y1AR6JF05Z6mtJQSGk4F5oZ0TChlGJlVnBoDkMx3fLSZ457iKrFyxJtXt003CmfpjSGRPAjjsdaIGpBplYSeS3Z4GozuL5mu8K3xLj08PkCoKdEbg.aKLr3w.27EtN9DYDfz11n9UxMHHq5QXB3o
2025-08-18 17:01:22 - INFO - 
自动化流程成功完成！
2025-08-18 17:01:22 - INFO - 添加第22个
2025-08-18 17:01:46 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63666,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:01:46 - INFO - 127.0.0.1:63666
2025-08-18 17:01:47 - INFO - <EMAIL>
2025-08-18 17:01:47 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:02:15 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:02:19 - INFO - 找到 Turnstile...
2025-08-18 17:02:22 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:02:25 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:02:37 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:02:37 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:02:49 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:02:49 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:02:49 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:02:49 - INFO - 成功！当前在订阅页面。
2025-08-18 17:02:49 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:02:49 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:02:49 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:02:52 - INFO - 代码信息: {
  "codeVerifier": "RgvB0alG4v2DTO34yW31BqN3lOvG1gX9Hgc2z35XglU",
  "code_challenge": "ty5N2cVrAeaIfnQ72ffXiN2dIaI1imuS3DiiUwEodU0",
  "state": "38f915a9-8cf2-46f1-a06d-7c1a2429919c"
}
2025-08-18 17:02:52 - INFO - ==================================================
2025-08-18 17:02:53 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_bdb2c7b2b38ec0e897ef64c06dc44b92&state=38f915a9-8cf2-46f1-a06d-7c1a2429919c&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-18 17:02:55 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:02:55 - INFO - 添加session: <EMAIL>   335db1eeb74fcbfe9d408c04e34116dd8b3a49d8816a6faf2cfb97cc5968cb2d   https://d5.api.augmentcode.com/  2025-08-25T09:02:39Z
2025-08-18 17:02:55 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgyAQRP9lz9rAAoKe-idmlaUhFTSoTZq2_15jeuhxJvPevKBfuCTKnDfotrJzBYFSnJ59psTQAVRwiw_Ofzn6pd9XLn30R8GJ4vRuHCGP0mslvQuorGcaUOljnuc8HqTRRqJzQqC1jXKyEVjBqTkNh4kCbV5aY4ywVuB1TVQ2P493Lpc5TzEz_IjzGIMPw8BcE2lXayltTVbqmgW6llscHWn4fAHg9UV7.aKLsPQ.5m3Ffuw-DAruOdXBw5aRAr3QZgM
2025-08-18 17:02:55 - INFO - 
自动化流程成功完成！
2025-08-18 17:02:55 - INFO - 添加第23个
2025-08-18 17:03:14 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63945,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:03:14 - INFO - 127.0.0.1:63945
2025-08-18 17:03:15 - INFO - <EMAIL>
2025-08-18 17:03:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:03:40 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:03:44 - INFO - 找到 Turnstile...
2025-08-18 17:03:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:03:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:04:02 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:04:02 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:04:02 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:04:02 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:04:02 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:04:14 - INFO -    等待 3.06 秒...
2025-08-18 17:04:23 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 17:04:23 - INFO - 
第 2/5 次尝试注册...
2025-08-18 17:04:23 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:04:23 - INFO - 成功！当前在订阅页面。
2025-08-18 17:04:23 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:04:23 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:04:23 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:04:26 - INFO - 代码信息: {
  "codeVerifier": "NZwReIb_shfMK0TaEEeuBQRdeKrfHSjjPG1Zo_iqd3Y",
  "code_challenge": "rJp_XXKZBYWncjiHFthLW6FCOgNk7lzR9dmshiPAIE4",
  "state": "b8f0bd3b-bacd-443b-86f3-fccd397450ec"
}
2025-08-18 17:04:26 - INFO - ==================================================
2025-08-18 17:04:27 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_d798e9203a030347d34c36d1bef3b32f&state=b8f0bd3b-bacd-443b-86f3-fccd397450ec&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 17:04:29 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:04:29 - INFO - 添加session: <EMAIL>   52f0a15666825959a0f1c78f036494cd852995bcc301d7c32943c7ccacc8f60c   https://d9.api.augmentcode.com/  2025-08-25T09:04:04Z
2025-08-18 17:04:29 - INFO - email:<EMAIL> === cookie:.eJxNzU1OxDAMBeC7eN2iJI7zMytuUoXYRRGNO8p0kBBwd8psYOmn9z5_wnKV0YuKHnA5xl0mWEtv28eipQtcACZ4be-i_-7G1-V-k7E0PgPppW1fIRUnNVqPltPqMPJqgmc567prPZc-oQ_BWjKZMFBKCSd4MA_hlGqvm9pIRCbGbJ5vvYyD9_om42nXremv9vc4yYvk6sxMtvLsGXHOwdmZkKqgo8KZ4PsHxhxE7Q.aKLsmw.l_4V-cdPAoZynZN33XgqcZZeNw8
2025-08-18 17:04:29 - INFO - 
自动化流程成功完成！
2025-08-18 17:04:29 - INFO - 添加第24个
2025-08-18 17:04:46 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64327,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:04:46 - INFO - 127.0.0.1:64327
2025-08-18 17:04:47 - INFO - <EMAIL>
2025-08-18 17:04:47 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:05:09 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:05:13 - INFO - 找到 Turnstile...
2025-08-18 17:05:18 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:05:21 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:05:21 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:05:33 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:05:45 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:05:45 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:05:45 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:05:45 - INFO - 成功！当前在订阅页面。
2025-08-18 17:05:45 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:05:45 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:05:45 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:05:48 - INFO - 代码信息: {
  "codeVerifier": "xJfO1Q14Nm_Y1UvV16LqWinfZcpOoFYmuUCYVvR724E",
  "code_challenge": "sTgLoc6N5XTHbGJRqaGXn_1u5UpP0I6brmBUUrPHpcQ",
  "state": "94b61b67-7f51-46b2-9425-374c0f4a8b4a"
}
2025-08-18 17:05:48 - INFO - ==================================================
2025-08-18 17:05:49 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_9ae5d9b28138623c32e690aae2a0a19a&state=94b61b67-7f51-46b2-9425-374c0f4a8b4a&tenant_url=https%3A%2F%2Fd11.api.augmentcode.com%2F
2025-08-18 17:05:52 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:05:52 - INFO - 添加session: <EMAIL>   fdbed3ce43ef1c5ea41a4be650e0a109d47a30bdb07ec4717f47684e0a012f2b   https://d11.api.augmentcode.com/  2025-08-25T09:05:35Z
2025-08-18 17:05:52 - INFO - email:<EMAIL> === cookie:.eJxNjs0OgjAQhN9lz2BKS3_g5JuQpV1MI11IAROjvrsN8eBtdjLz7bxgWCknZOId-j0fVMGEKc7PgTER9AAV3OKD-O-OYR2OjfIQQzEoYZzfxqEk732rmuAmqWyYDBZZ4rywL02pnRKiU8IJaWTjOt1WcGJOQiEdfETfWK21sM7J65Yw72Hxd8qXhefIBL_G-RilMyMpVZvW2rq1aGoURWEYy45RohAIny_4eUWg.aKLs7Q.Hyxdht5lKXj1hJ2h4J6sE2iEL7M
2025-08-18 17:05:52 - INFO - 
自动化流程成功完成！
2025-08-18 17:05:52 - INFO - 添加第25个
2025-08-18 17:06:09 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65076,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:06:09 - INFO - 127.0.0.1:65076
2025-08-18 17:06:09 - INFO - <EMAIL>
2025-08-18 17:06:09 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:06:35 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:06:39 - INFO - 找到 Turnstile...
2025-08-18 17:06:41 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:06:44 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:06:56 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:06:56 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:07:14 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:07:14 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:07:14 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:07:14 - INFO - 成功！当前在订阅页面。
2025-08-18 17:07:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:07:14 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:07:14 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:07:17 - INFO - 代码信息: {
  "codeVerifier": "vSisIdsV4cJq4hnvDsVenR2DdPqgBIDdAKSMEwcHo14",
  "code_challenge": "4feAFzLF-ccKtiENGF6GGAfB3mmyQWMu_PV76LZoaG8",
  "state": "137eaf5c-67c0-4a2c-8190-0df8f8bbb36e"
}
2025-08-18 17:07:17 - INFO - ==================================================
2025-08-18 17:07:18 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_3e83547df37c48754ddebcc38441bbf4&state=137eaf5c-67c0-4a2c-8190-0df8f8bbb36e&tenant_url=https%3A%2F%2Fd11.api.augmentcode.com%2F
2025-08-18 17:07:20 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:07:20 - INFO - 添加session: <EMAIL>   cc4005592c7b1689e49c70f451893500a9e03783cc206da14a3814a31f9c003c   https://d11.api.augmentcode.com/  2025-08-25T09:06:58Z
2025-08-18 17:07:20 - INFO - email:<EMAIL> === cookie:.eJxNjUkOwjAMRe_idYuaqW674iaRSRwU0aRVKEhMdyeqWLCwJX_99_wCu3JJlDlvMG3lxg0ESnF-2EyJYQJo4BzvnP_u6Fd7u3Kx0deAE8X53Q8k2ctOK-GHIBX64FBJqvW8ZFdJNKMSdQx2WqBENLKBXbMbqmnxz3MQaIzpcOz18ZqobH5xFy6HJc8xM_yI_XEXTug0ja1zQbWaB9mO4lRX35OkoMMgPHy-z_xFhg.aKLtRg.zSQWmV_fKL5psEES0XCvSHlSq3I
2025-08-18 17:07:20 - INFO - 
自动化流程成功完成！
2025-08-18 17:07:20 - INFO - 添加第26个
2025-08-18 17:07:41 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65271,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:07:41 - INFO - 127.0.0.1:65271
2025-08-18 17:07:41 - INFO - <EMAIL>
2025-08-18 17:07:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:07:59 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:08:03 - INFO - 找到 Turnstile...
2025-08-18 17:08:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:08:12 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:08:12 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:08:25 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:08:37 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:08:37 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:08:37 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:08:37 - INFO - 成功！当前在订阅页面。
2025-08-18 17:08:37 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:08:37 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:08:37 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:08:40 - INFO - 代码信息: {
  "codeVerifier": "8TeI1T5CUaXPus__tzclvq36jSzn_HuZywfqRhFmM4A",
  "code_challenge": "HwAefgiLD_Np5njXKOYaUj3al0EzyN9YfcNqveC8lQs",
  "state": "7a914917-cfbe-48e7-aa9e-bbf8f6e61d30"
}
2025-08-18 17:08:40 - INFO - ==================================================
2025-08-18 17:08:41 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_2b0d3ccde3f509827111251064cb5001&state=7a914917-cfbe-48e7-aa9e-bbf8f6e61d30&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-18 17:08:43 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:08:43 - INFO - 添加session: <EMAIL>   2428fa1b7317406129e7fbad6f44669873b40425658d15a8a1763025b738b301   https://d6.api.augmentcode.com/  2025-08-25T09:08:29Z
2025-08-18 17:08:43 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgjAQRP9lz2Cg0O7Wk39ClnYxjbSYghqj_rsN8WDmNJOZNy8YrpIjJ0kbHLd8kwomjmF-DomjwBGggnO4S_rzwV-H2yp5CL4EEjnMb0OsxKPpu9bTpDr0jaKmp1JPS3JlqTtD1hKiRq0UWUVYwY7ZCYU0Pi6cW9RaN9Roc1oj580v7iL5sKQ5JIHfYj8m5HGUyda2Y6z7VnxNHl1tyRQ5tOx6-HwBlQFFBQ.aKLtmQ.08xI_LfsgbAj5P8Ng5h-1Rqyvj8
2025-08-18 17:08:43 - INFO - 
自动化流程成功完成！
2025-08-18 17:08:43 - INFO - 添加第27个
2025-08-18 17:08:58 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:49179,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:08:58 - INFO - 127.0.0.1:49179
2025-08-18 17:08:59 - INFO - <EMAIL>
2025-08-18 17:08:59 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:09:46 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:09:50 - INFO - 找到 Turnstile...
2025-08-18 17:09:53 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:09:56 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:10:08 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:10:08 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:10:19 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:10:19 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:10:19 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:10:19 - INFO - 成功！当前在订阅页面。
2025-08-18 17:10:19 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:10:19 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:10:19 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:10:22 - INFO - 代码信息: {
  "codeVerifier": "60-2cP8H-Kwl5rvRrYszF-sHAlLouYSQbfjosT5zLBk",
  "code_challenge": "qguL9r16kodPFG6BvnBQDpLMDxHcj5IwNcoIY1lp9FA",
  "state": "c4d6722b-102d-4295-98e9-7b2ae009264b"
}
2025-08-18 17:10:22 - INFO - ==================================================
2025-08-18 17:10:23 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_291e3007ae1fb159f596baf5e1b5abaf&state=c4d6722b-102d-4295-98e9-7b2ae009264b&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 17:10:26 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:10:26 - INFO - 添加session: <EMAIL>   f1b724be9dd8215aa771bfa2b438d72ad7d44e47e763f12055052a7128007459   https://d9.api.augmentcode.com/  2025-08-25T09:10:11Z
2025-08-18 17:10:26 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAURP_lrsHQ0icr_4SU3lttpEULkvj6dwlx4XImc868oL9SSS5TXqBbyp0qCC7F8dFnlwg6gApOcaX8lyNe-_tMpY-4FZRcHN_KOE5IjWgZmsBbjY0l7s02z1P2G8mFVUoKrpU0VrZMMFXBrtkNm-l2fs4r01LKxrBWHOfkyoKTv1A5THmMmeBH7MfopRu0DbX2KtTCYVMbNpja8OCVGJAxaeHzBeZNRa4.aKLt_w.WB7tevD3nZgNxhTVeJGKGRJNN4A
2025-08-18 17:10:26 - INFO - 
自动化流程成功完成！
2025-08-18 17:10:26 - INFO - 添加第28个
2025-08-18 17:10:49 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:49997,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:10:49 - INFO - 127.0.0.1:49997
2025-08-18 17:10:50 - INFO - <EMAIL>
2025-08-18 17:10:50 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:11:14 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:11:18 - INFO - 找到 Turnstile...
2025-08-18 17:11:22 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:11:25 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:11:39 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:11:39 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:11:46 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:11:46 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:11:46 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:11:46 - INFO - 成功！当前在订阅页面。
2025-08-18 17:11:46 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:11:46 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:11:48 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:11:51 - INFO - 代码信息: {
  "codeVerifier": "N5GgAjSH4WwbIN_rq4mu_yeMz-5pmb4ExAQFJSOiBjM",
  "code_challenge": "kj2NADtVMo_96ofr4a07HY6H4jqmYGJUZBMsxXQzD_o",
  "state": "d5a483bc-b2fb-4494-b68e-385cc85b18d2"
}
2025-08-18 17:11:51 - INFO - ==================================================
2025-08-18 17:11:53 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_9e849cf503e2429e5d575d9a3ea731e2&state=d5a483bc-b2fb-4494-b68e-385cc85b18d2&tenant_url=https%3A%2F%2Fd4.api.augmentcode.com%2F
2025-08-18 17:11:54 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:11:54 - INFO - 添加session: <EMAIL>   1bf182286f3c86a8e924e25af0599b1b70fa542eab6c8468ed155a391b9bb6c1   https://d4.api.augmentcode.com/  2025-08-25T09:11:41Z
2025-08-18 17:11:54 - INFO - email:<EMAIL> === cookie:.eJxNjksOwjAQQ-8y6xblM2kDK25SpckEBZppFQoCAXcnqliwtGU_-wXDQiU7Jl7hsJYbNRBdTtNzYJcJDgANnNKd-E-nsAy3K5UhhWpQdml6d9YpIm1Ry2Cj0n2QokMha5xn9rVplcHeKqm0MtYItPuugQ2zESrpPvL5IXtjjLAK8XjNrqxh9hcqu5mnxAS_xjYcA469k771xo8t7iO1I2rfBo31RoyCooDPF5R3RYQ.aKLuWA.KyHBzDxlJ5U6LU5p0yzUmNN2kIY
2025-08-18 17:11:54 - INFO - 
自动化流程成功完成！
2025-08-18 17:11:54 - INFO - 添加第29个
2025-08-18 17:12:09 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:50335,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:12:09 - INFO - 127.0.0.1:50335
2025-08-18 17:12:10 - INFO - <EMAIL>
2025-08-18 17:12:10 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:12:40 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:12:44 - INFO - 找到 Turnstile...
2025-08-18 17:12:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:12:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:12:49 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:13:03 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:13:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:13:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:13:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:13:21 - INFO - 成功！当前在订阅页面。
2025-08-18 17:13:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:13:21 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:13:21 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:13:24 - INFO - 代码信息: {
  "codeVerifier": "Th_vL8FZU6G7n1nfMX9dLQMIfFd-v3LChgEHwyQJtOw",
  "code_challenge": "cmQYT7YOEYIRZRIeFlYnBo1_SPhWovRLpSp2mV9UtFU",
  "state": "7030ae9d-bae2-472f-9bd0-cf5895de5352"
}
2025-08-18 17:13:24 - INFO - ==================================================
2025-08-18 17:13:27 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_19b1e73892fe614d857325d5c6afb43f&state=7030ae9d-bae2-472f-9bd0-cf5895de5352&tenant_url=https%3A%2F%2Fd16.api.augmentcode.com%2F
2025-08-18 17:13:29 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:13:29 - INFO - 添加session: <EMAIL>   ba699b49131b0d52b1b04432a9de57bfdf3c16d2ee506e8198d9224b9678bcca   https://d16.api.augmentcode.com/  2025-08-25T09:13:06Z
2025-08-18 17:13:29 - INFO - email:<EMAIL> === cookie:.eJxNjcFugzAQRP9lzxBhe9deOPVPkLGXyio21CGRoiT_HoR66HFG8948YdykZl-k7DDs9SYNzD6n5TEWnwUGgAa-013Kv5ziNt6uUscUj0KyT8vLstciHNGoyLM2LirrGOmYl7WEg0QibZ1B5t4ojWS7Bk7LKThEv49tqsoRUcdG09c1-7rHNfxIvaxlSUXgjzh_TXR9mAK2HLhrcUZqvXVzKxq91aFTFHt4fwBs5USw.aKLutQ.V60JGiAKLWDl8ppAGyyzjFg0PpQ
2025-08-18 17:13:29 - INFO - 
自动化流程成功完成！
2025-08-18 17:13:29 - INFO - 添加第30个
2025-08-18 17:13:46 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:50613,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:13:46 - INFO - 127.0.0.1:50613
2025-08-18 17:13:46 - INFO - <EMAIL>
2025-08-18 17:13:46 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:14:13 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:14:17 - INFO - 找到 Turnstile...
2025-08-18 17:14:21 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:14:25 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:14:25 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:14:37 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:15:08 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 17:15:08 - INFO - 鼠标左键放开操作完成。
2025-08-18 17:15:08 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 17:15:09 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 17:15:12 - INFO - 验证成功，已进入目标页面。
2025-08-18 17:15:12 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:15:12 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:15:12 - INFO - 成功！当前在订阅页面。
2025-08-18 17:15:12 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:15:12 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:15:12 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:15:15 - INFO - 代码信息: {
  "codeVerifier": "QD-oBwmLOs8rEOHwxXFEqQDrpepcs3Z2neAWDOtBtRY",
  "code_challenge": "GMmYmF-DL_fXgHS0BjGEo707zk0Vk5Xoqg3sa3AL2O0",
  "state": "d3413f7e-58ff-4dab-b594-3e4f29b69a34"
}
2025-08-18 17:15:15 - INFO - ==================================================
2025-08-18 17:15:15 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_93260637049202e89b0cdfbd06a3126d&state=d3413f7e-58ff-4dab-b594-3e4f29b69a34&tenant_url=https%3A%2F%2Fd18.api.augmentcode.com%2F
2025-08-18 17:15:17 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:15:17 - INFO - 添加session: <EMAIL>   56b0594ecb353e856098da529d05b56a04b84f4235f2c4c7c7386e6087433ea6   https://d18.api.augmentcode.com/  2025-08-25T09:14:39Z
2025-08-18 17:15:17 - INFO - email:<EMAIL> === cookie:.eJxNjUtuhDAQRO_Saxi5_cGGVW6CGrpJrGAzMhBpNDN3j4WyyLJK9V49YbxLSZQlHzAc5ZQGFkpxfYyZksAA0MBn_JH8L0e-j-cuZYxcC0kU11cXSIvIZA1yWLTxjGyUV3WetzxXEq22XXC-w16h8s6GroFLcxmq6fx67Am9c04Fq_FjT1QO3uZvKbctrzEL_BHXMRtj5wl1yxP71pLTbb1fWu6VJ4vKsCN4_wLSYkVE.aKLvIw.0whQyxMOyZATxohfO4RW8YjdWrU
2025-08-18 17:15:17 - INFO - 
自动化流程成功完成！
2025-08-18 17:15:17 - INFO - 添加第31个
2025-08-18 17:15:39 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:50911,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:15:39 - INFO - 127.0.0.1:50911
2025-08-18 17:15:40 - INFO - <EMAIL>
2025-08-18 17:15:40 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:16:04 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:16:08 - INFO - 找到 Turnstile...
2025-08-18 17:16:10 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:16:13 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:16:13 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:16:25 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:16:45 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:16:45 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:16:45 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:16:45 - INFO - 成功！当前在订阅页面。
2025-08-18 17:16:45 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:16:45 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:16:45 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:16:48 - INFO - 代码信息: {
  "codeVerifier": "6HXrLmcdVNF_ahDicygQbQHEQ0bE3NYdFBcr5_pPeZg",
  "code_challenge": "aONG1E7JjHQxgBMc2NK4jHgizdgC4EoM-BQ2J9CnY68",
  "state": "160e093e-d8ed-4c21-9dcc-ae081af0aaf8"
}
2025-08-18 17:16:48 - INFO - ==================================================
2025-08-18 17:16:49 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_82420a4787a0f14a2c5d2504088a80a2&state=160e093e-d8ed-4c21-9dcc-ae081af0aaf8&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-18 17:16:51 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:16:51 - INFO - 添加session: <EMAIL>   6777729f7fec92729d1046abf25043b910373196d9507fd7cb1f56c0f97d68be   https://d6.api.augmentcode.com/  2025-08-25T09:16:27Z
2025-08-18 17:16:51 - INFO - email:<EMAIL> === cookie:.eJxNjUuOgzAQRO_Sa4j8a9tklZugBjeRFWwiA5mJZnL3WCiLLKtU79Uf9HcuiTLnDc5b2bmBiVKcn32mxHAGaOAaH5y_cgz3fl-59DHUghPF-d96UjyhN1oGPyntgjKDpa7O85LHShqNQnptVaeVltg5Zxs4NIehmtafhX6lQ0ThUeNlTVS2sIw3LqclzzEzfIjj2PmBRqNEG9DL1hg5tkP9bQUbK1zoUBqE1xuw_ER4.aKLvgQ.6F5TaJkfbeasB50qgloBFQJuZpE
2025-08-18 17:16:51 - INFO - 
自动化流程成功完成！
2025-08-18 17:16:51 - INFO - 添加第32个
2025-08-18 17:17:14 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51186,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:17:14 - INFO - 127.0.0.1:51186
2025-08-18 17:17:15 - INFO - <EMAIL>
2025-08-18 17:17:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:17:47 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:17:52 - INFO - 找到 Turnstile...
2025-08-18 17:17:54 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:17:57 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:17:57 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:18:10 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:18:18 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:18:18 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:18:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:18:18 - INFO - 成功！当前在订阅页面。
2025-08-18 17:18:18 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:18:18 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:18:27 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:18:30 - INFO - 代码信息: {
  "codeVerifier": "aPCm1tWCjT-IqaojZNGUi96I4NoyYNoIgfdVLVZuMYA",
  "code_challenge": "pHP8gXGGmnhwnpcJ6XsJF_yXujKSzYvJEPzhdQDmx6w",
  "state": "1f23183e-9f02-4e13-9548-76a3ce3278e4"
}
2025-08-18 17:18:30 - INFO - ==================================================
2025-08-18 17:18:31 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_d1cc4320ef6be5ab72e754e3e82f26f2&state=1f23183e-9f02-4e13-9548-76a3ce3278e4&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 17:18:33 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:18:33 - INFO - 添加session: <EMAIL>   07d0929c4573dd0259d9a8ebd7ed5775260c7c2260684627c445ab67dda85a36   https://d9.api.augmentcode.com/  2025-08-25T09:18:12Z
2025-08-18 17:18:33 - INFO - email:<EMAIL> === cookie:.eJxNjctuwyAQRf9l1nY1wIBxVv0TawLjCAVwRJw-1Obfg6wuuryvc39guUkrXKXucNrbQwZYuaT8vVQuAieAAS7pQ-o_neJtedylLSl2Qwqn_Os8a1kDklHRr9pMUZ8DSez1utXQl5r87K2xDs2MzpO1doADcxA66esz511NPUDvDL7fC7c9buEq7W2rOVWBv8VxTL0UiaZxxUAjKXUemRWOMxEZzREtOni-APQpRMk.aKLv5w._U61ex1T0K7QBnmC9TspbKdgja4
2025-08-18 17:18:33 - INFO - 
自动化流程成功完成！
2025-08-18 17:18:33 - INFO - 添加第33个
2025-08-18 17:18:50 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51511,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:18:50 - INFO - 127.0.0.1:51511
2025-08-18 17:18:50 - INFO - <EMAIL>
2025-08-18 17:18:50 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:19:15 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:19:18 - INFO - 找到 Turnstile...
2025-08-18 17:19:23 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:19:26 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:19:38 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:19:38 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:19:55 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:19:55 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:19:55 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:19:55 - INFO - 成功！当前在订阅页面。
2025-08-18 17:19:56 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:19:56 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:20:06 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 17:20:09 - INFO - 代码信息: {
  "codeVerifier": "jvNwMHk4nwUZghVeUU2NQ_5-OI-HXBUxj_rj3fXTemU",
  "code_challenge": "t8pSZGWF-Q6qN2EWHbrCqN8n_WyrdEgJc7WtU89fl5U",
  "state": "9d6ef57d-001b-43a2-ae25-512b68acdfe0"
}
2025-08-18 17:20:09 - INFO - ==================================================
2025-08-18 17:20:10 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_e558cf869da452d933181af29d573d93&state=9d6ef57d-001b-43a2-ae25-512b68acdfe0&tenant_url=https%3A%2F%2Fd7.api.augmentcode.com%2F
2025-08-18 17:20:12 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:20:12 - INFO - 添加session: <EMAIL>   a0b3ac73c8d9a9090aa08c2f214340cc9fa71a9272be7c472d80612b8fd56cfb   https://d7.api.augmentcode.com/  2025-08-25T09:19:41Z
2025-08-18 17:20:12 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgyAQhN9lz9rwIwie-iZmlaUhFTSoTUzbdy81PfQ2M5n55gn9QjliorRBt-WdKvAYw3T0CSNBB1DBLTwo_fngln5fKffBlYAihumlDQrPuG0kd8YL2TrJh4GxUk9zGsuyVUpZIxQzVmhhdVEVnJiTUEjrmI-Vf3vMtEJf14h5c_N4p3yZ0xQSwW9xHnPNB4e2qS1pVTej97VRRRk3oEcuURKH9weaKEU4.aKLwSg.ctw1iE3NBmuOJCkd0UZFaOVctIA
2025-08-18 17:20:12 - INFO - 
自动化流程成功完成！
2025-08-18 17:20:12 - INFO - 添加第34个
2025-08-18 17:20:28 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51761,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:20:28 - INFO - 127.0.0.1:51761
2025-08-18 17:20:29 - INFO - <EMAIL>
2025-08-18 17:20:29 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:20:58 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:21:02 - INFO - 找到 Turnstile...
2025-08-18 17:21:02 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:21:05 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:21:18 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:21:18 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:21:18 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:21:18 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:21:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:21:26 - INFO -    等待 4.79 秒...
2025-08-18 17:21:34 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 17:21:34 - INFO - 
第 2/5 次尝试注册...
2025-08-18 17:21:34 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:21:34 - INFO - 成功！当前在订阅页面。
2025-08-18 17:21:34 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:21:34 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:21:34 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:21:37 - INFO - 代码信息: {
  "codeVerifier": "keX5LtEdasDdR3Zjv6gdzpQrMmxGL5gyDCr_x9YcAcE",
  "code_challenge": "wOoK4r3GnWh4O9NLkde2I4TKIjIXDUnBoNQIe3Myqwo",
  "state": "ba94a345-92e8-446f-9da6-8cd841b0a20d"
}
2025-08-18 17:21:37 - INFO - ==================================================
2025-08-18 17:21:38 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_dc14fc1e28a182e2726d8663ba3b6aaa&state=ba94a345-92e8-446f-9da6-8cd841b0a20d&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-18 17:21:40 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:21:40 - INFO - 添加session: <EMAIL>   efa98439a4135c5dc1dae2a97f304a7d245cbf9a77027db331c8615ae2744e22   https://d10.api.augmentcode.com/  2025-08-25T09:21:20Z
2025-08-18 17:21:40 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtR0opH7OqjcxFJFKhFiyoTgFiqZ3r2B00eUM5r35hnmTVkKVusNlby8ZIIWSl6-5hiJwARjglj-l_suZt_n1lDZn7oWUkJe39QGTcknTmX1Cckz-qkn1eV1r7KR1RDhpT6ScstOkrRrg0ByGbrpvy207O2OM8h71x7OEtvMaH9JOa11yFfgjjmNkmozGOKKyPOp4lTGgxpEjsfGpfwQLP7-Rr0S9.aKLwog.U5wg3mrR0csXsIJhlSHeLv3Nz_o
2025-08-18 17:21:40 - INFO - 
自动化流程成功完成！
2025-08-18 17:21:40 - INFO - 添加第35个
2025-08-18 17:21:57 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52028,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:21:57 - INFO - 127.0.0.1:52028
2025-08-18 17:21:57 - INFO - <EMAIL>
2025-08-18 17:21:57 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:22:30 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:22:35 - INFO - 找到 Turnstile...
2025-08-18 17:22:40 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:22:43 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:22:58 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:22:59 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:23:29 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 17:23:29 - INFO - 鼠标左键放开操作完成。
2025-08-18 17:23:29 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 17:23:30 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 17:23:33 - INFO - 验证成功，已进入目标页面。
2025-08-18 17:23:33 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:23:33 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:23:33 - INFO - 成功！当前在订阅页面。
2025-08-18 17:23:33 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:23:33 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:23:33 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:23:36 - INFO - 代码信息: {
  "codeVerifier": "tMjFJ626kPXS2GbZ_x-vYd18Hi9OUIsV_ZEZGbFOrz8",
  "code_challenge": "BZPenzf8Wv2rlG1JwW2IqdbTHSNfbq61CRnqR3qv_os",
  "state": "725b79f3-3e94-4c59-8372-098dee172711"
}
2025-08-18 17:23:36 - INFO - ==================================================
2025-08-18 17:23:38 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_a3cc49bd4b8305d8287dd091c3ad7d7a&state=725b79f3-3e94-4c59-8372-098dee172711&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-18 17:23:40 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:23:40 - INFO - 添加session: <EMAIL>   4d9d2f15fd579efd66786ef331fdf477540332efa80ae1290abf22be3b1c3720   https://d12.api.augmentcode.com/  2025-08-25T09:23:00Z
2025-08-18 17:23:40 - INFO - email:<EMAIL> === cookie:.eJxNjc0OwiAQhN9lz61pu7CAJ9-kwbIYtNAGW41_7y5RD14mmcnMNw_oZ87RJk4LbJe8cgXexjDe-mQjwxaggkO4cPrzwc39eubcB1cCjjaMT9K2840bBLZO-w6VQ98aRaWepjSUpZCSkEyniZQRaEg1FXwwH0IhXffH9d4qKWWjTdvtztHmxU3DifNmSmNIDL_F97jBckdcM7qhFrKIRidrgR79XmttiOH1Bsh6RWI.aKLxGg.lkPUdqsuxhbYj2kRpaUiB9RJt48
2025-08-18 17:23:40 - INFO - 
自动化流程成功完成！
2025-08-18 17:23:40 - INFO - 添加第36个
2025-08-18 17:24:03 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52320,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:24:03 - INFO - 127.0.0.1:52320
2025-08-18 17:24:03 - INFO - <EMAIL>
2025-08-18 17:24:03 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:24:32 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:24:36 - INFO - 找到 Turnstile...
2025-08-18 17:24:38 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:24:41 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:24:41 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:24:52 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:24:52 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:24:52 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:24:52 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:25:03 - INFO -    等待 3.85 秒...
2025-08-18 17:25:11 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 17:25:11 - INFO - 
第 2/5 次尝试注册...
2025-08-18 17:25:11 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:25:11 - INFO - 成功！当前在订阅页面。
2025-08-18 17:25:11 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:25:11 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:25:11 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:25:14 - INFO - 代码信息: {
  "codeVerifier": "tKVUZ_aE8D-s8OSHhSX5KaK9quYgh24Y08v29YlTsFo",
  "code_challenge": "vJ2oD28J5eBO618cJDu6GaEcZLxN53zYcOOn2nTu9XI",
  "state": "12d09ef9-4b03-4903-aad1-0f4c721d30e6"
}
2025-08-18 17:25:14 - INFO - ==================================================
2025-08-18 17:25:14 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_169550765be678d4ebb7dc1c0941c073&state=12d09ef9-4b03-4903-aad1-0f4c721d30e6&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-18 17:25:16 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:25:16 - INFO - 添加session: <EMAIL>   c03e4c29cbb69a33761b6805fa174f70c1ae93546ed215ca1a0f127ec8b2d66c   https://d10.api.augmentcode.com/  2025-08-25T09:24:54Z
2025-08-18 17:25:16 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3blZcFATvkTaw1LhWpwgp1IVdt_r5P00OOM5r35gukirXCVusNpbzfpIHHJy-dUuQicADp4z3ep_3KOl-m2SZtyPAopnJfv0bFKaLQmjC4pslFbIgzHvK41HKQyylszOhpxUGjIat3BU_M0PEzXeWO0xpjBD-TOW-G2xzV8SHtb65KrwB_xOk40myCuT9H7XqPHnlnmXkfyHGaFPjn4-QWDnUVE.aKLxeg.ltjN9V6HMza3aLdg5KtGKA-CDuA
2025-08-18 17:25:16 - INFO - 
自动化流程成功完成！
2025-08-18 17:25:16 - INFO - 添加第37个
2025-08-18 17:25:36 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52522,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:25:36 - INFO - 127.0.0.1:52522
2025-08-18 17:25:36 - INFO - <EMAIL>
2025-08-18 17:25:36 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:26:18 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:26:22 - INFO - 找到 Turnstile...
2025-08-18 17:26:25 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:26:28 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:26:41 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:26:41 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:26:50 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:26:50 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:26:50 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:26:50 - INFO - 成功！当前在订阅页面。
2025-08-18 17:26:50 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:26:50 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:26:50 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:26:53 - INFO - 代码信息: {
  "codeVerifier": "fB-RZFB0G7o8GhgSUChRXCmsjDVfDCJg4Lxdd7MD5XQ",
  "code_challenge": "NBaS8FPvbdLeYFDwnOWen3IEFk9MOAILafP5_mHIfko",
  "state": "3d038782-3533-42b1-9b7b-f917bfbba1be"
}
2025-08-18 17:26:53 - INFO - ==================================================
2025-08-18 17:26:54 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_3fbc53a1f5dfc3a4c152e45025a1175c&state=3d038782-3533-42b1-9b7b-f917bfbba1be&tenant_url=https%3A%2F%2Fd14.api.augmentcode.com%2F
2025-08-18 17:26:56 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:26:56 - INFO - 添加session: <EMAIL>   160cb0579b55a9de0227414e8230c7f9d299a0b980aaccc3d5948d2de7bc9bec   https://d14.api.augmentcode.com/  2025-08-25T09:26:43Z
2025-08-18 17:26:56 - INFO - email:<EMAIL> === cookie:.eJxNjUEOgyAURO_y19qACB-66k0IwqchChrUJk3bu9eYLrqcybw3L7AL1ewKlQ2uW92pgehymp62uExwBWjgnh5U_nIKi91XqjaFo6Ds0vRW2nWRe9YLHnTsBIaetEJ_zMtc_EFqNCiV6oRCztBwpUQDp-Y0HKZxykviKKVkhgt-W7OrW5j9SPUylykVgh9xHhtloqPAWvImtv2gTTuwoW8jyo5LDNEjwucLwSVFdQ.aKLx3g.QTGXG0mqYNHeLte4CLGxfLluPes
2025-08-18 17:26:56 - INFO - 
自动化流程成功完成！
2025-08-18 17:26:56 - INFO - 添加第38个
2025-08-18 17:27:15 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52815,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:27:15 - INFO - 127.0.0.1:52815
2025-08-18 17:27:16 - INFO - <EMAIL>
2025-08-18 17:27:16 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:27:43 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:27:47 - INFO - 找到 Turnstile...
2025-08-18 17:27:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:27:51 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:28:03 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:28:03 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:28:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:28:22 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:28:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:28:22 - INFO - 成功！当前在订阅页面。
2025-08-18 17:28:22 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:28:22 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:28:22 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:28:25 - INFO - 代码信息: {
  "codeVerifier": "3LctdhtJiFF3_kyfz1RhFnCiN3G3gYE_CpffS43L0B8",
  "code_challenge": "0fWAMHd0R9fea1Fuj5rqWyAeVQxmx3rkdP5B2I9j3FQ",
  "state": "961cd721-42a8-445d-9ed3-e17d49e1f716"
}
2025-08-18 17:28:25 - INFO - ==================================================
2025-08-18 17:28:26 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_80013eb6066e4d19ea18abb7d5eed08e&state=961cd721-42a8-445d-9ed3-e17d49e1f716&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-18 17:28:29 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:28:29 - INFO - 添加session: <EMAIL>   9f33ee014d613413d39e0da4ed31d7b2ce00ee4a87780e44598326e9b19491be   https://d8.api.augmentcode.com/  2025-08-25T09:28:06Z
2025-08-18 17:28:29 - INFO - email:<EMAIL> === cookie:.eJxNjcFugzAQRP9lzxDZXq_BnPInyMZL5RYvyCGRUNp_L0I99DijeW_eMG5cSxCWHYa9PrmBOZS8HKOEwjAANPCRXyz_ck7b-HxwHXM6Cy4hL9-uD2Y2mizq1M8Gu0TWEuI5l1Wmk9RIBr13HXXOKCJjfAOX5jKcpv0zyqE7IlLeoLo_Sqh7WqcvrrdVliwMf8R1PHmKqCm2M_aptdFjG71PLWtllY2OaXLw8wti_ERr.aKLyOg.6U9jj_JOkrFSUZY-LZgFtQU0L5I
2025-08-18 17:28:29 - INFO - 
自动化流程成功完成！
2025-08-18 17:28:29 - INFO - 添加第39个
2025-08-18 17:28:53 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53460,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:28:53 - INFO - 127.0.0.1:53460
2025-08-18 17:28:54 - INFO - <EMAIL>
2025-08-18 17:28:54 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:29:23 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:29:27 - INFO - 找到 Turnstile...
2025-08-18 17:29:27 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:29:31 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:29:31 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:29:42 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:29:51 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:29:51 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:29:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:29:51 - INFO - 成功！当前在订阅页面。
2025-08-18 17:29:51 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:29:51 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:29:51 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:29:54 - INFO - 代码信息: {
  "codeVerifier": "QLVo7POZCMhehMu0gaoCjdyvXwTiPF7l-RCyjhrM4TU",
  "code_challenge": "4j1IxE_Irw6Ii4u71C3AZ-orPX4zm_Ch-Col2QApI5Y",
  "state": "bec65ab6-ea79-4c41-97d0-083f6c1d155d"
}
2025-08-18 17:29:54 - INFO - ==================================================
2025-08-18 17:29:55 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_d0b360407cf58a47c35065514fc38ca5&state=bec65ab6-ea79-4c41-97d0-083f6c1d155d&tenant_url=https%3A%2F%2Fd15.api.augmentcode.com%2F
2025-08-18 17:29:57 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:29:57 - INFO - 添加session: <EMAIL>   66a4a6052283e3edd044f7e0ec75c9917b19a0d052f5e17da669d45ce9be949e   https://d15.api.augmentcode.com/  2025-08-25T09:29:44Z
2025-08-18 17:29:57 - INFO - email:<EMAIL> === cookie:.eJxNjs1uwyAQhN9lz3YEi9dgn_omFj9Lg2JwSpxIUdp3D7Jy6HFGM9_MC5Yr12wLlx3mvd65g2hzWp9LsZlhBujgOz24_NMpXJf7jeuSQjM427T-jsZiRG0GJYOJqHQgy2x9i5et-NbUKEiPBlFMQkoaJxo7ODAHoZHO0acfqYlITAqnr1u2dQ-bv3A9bWVNheHTOIYxeuO8U72KxP1AjnonOPZKOlS-XVBs4O8N5P1Flw.aKLykw.8HQC9D_3YlWnDE_TeIWsKu52moE
2025-08-18 17:29:57 - INFO - 
自动化流程成功完成！
2025-08-18 17:29:57 - INFO - 添加第40个
2025-08-18 17:30:18 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53730,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:30:18 - INFO - 127.0.0.1:53730
2025-08-18 17:30:19 - INFO - <EMAIL>
2025-08-18 17:30:19 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:30:40 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:30:44 - INFO - 找到 Turnstile...
2025-08-18 17:30:44 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:30:48 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:30:48 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:30:59 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:31:15 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:31:15 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:31:15 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:31:15 - INFO - 成功！当前在订阅页面。
2025-08-18 17:31:15 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:31:15 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:31:15 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:31:18 - INFO - 代码信息: {
  "codeVerifier": "oGAiG-wvjrW9AdS_A67sPFFN5KCgc5IV0Auy-PMsRZ8",
  "code_challenge": "0sxYgXw1LAS1rjL1l45zDLmy3QsIYAJ1qdZAPaqERhE",
  "state": "21cb3444-ba99-4bc5-8d71-09f3ddb0ff98"
}
2025-08-18 17:31:18 - INFO - ==================================================
2025-08-18 17:31:19 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_64d3aafbb550347d98d62260ae25d0ec&state=21cb3444-ba99-4bc5-8d71-09f3ddb0ff98&tenant_url=https%3A%2F%2Fd11.api.augmentcode.com%2F
2025-08-18 17:31:21 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:31:21 - INFO - 添加session: <EMAIL>   00116d469a308e4b712ef75fcb83129db00053ae6ad255b0740974878b341ab2   https://d11.api.augmentcode.com/  2025-08-25T09:31:01Z
2025-08-18 17:31:21 - INFO - email:<EMAIL> === cookie:.eJxNjc1uwyAQhN9lz3ZlMMtPTnkTa8suFarBESGVqqTvXmT10NvMaOabJ2w3aYWq1A6X3h4yQaKS9--tUhG4AEzwkb-k_vOZb9vjLm3LPAIplPeX9aSTjmhWxT7p1bFd7GrCqNejxrF0y6q80xiUNcqiCjjBSTkBA5R62rtyiLgEo8z1Xqh1PuKntLej7rkK_C3O36h9MJHfZxs5zkaMn8kOxeLIO3YYCeHnF1boRV8.aKLy5w.rfhmOUy1MjCspqn9WX1fbLhaAgc
2025-08-18 17:31:21 - INFO - 
自动化流程成功完成！
2025-08-18 17:31:21 - INFO - 添加第41个
2025-08-18 17:31:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54000,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:31:40 - INFO - 127.0.0.1:54000
2025-08-18 17:31:41 - INFO - <EMAIL>
2025-08-18 17:31:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:32:01 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:32:05 - INFO - 找到 Turnstile...
2025-08-18 17:32:07 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:32:10 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:32:21 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:32:21 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:32:27 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:32:27 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:32:27 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:32:27 - INFO - 成功！当前在订阅页面。
2025-08-18 17:32:27 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:32:27 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:32:27 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:32:30 - INFO - 代码信息: {
  "codeVerifier": "2eqFcK7hnfK3K173TUoZTGvT_I_WbRV273S6mGGpvCM",
  "code_challenge": "7S8y0M89l56bSrmuG0NV69xG6-cAAkgj43K8w9t2zzE",
  "state": "d8a1bea3-a33a-4c1a-8fac-1db499b3ccad"
}
2025-08-18 17:32:30 - INFO - ==================================================
2025-08-18 17:32:31 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_5928efa557277d5c5fa59094320dc5d3&state=d8a1bea3-a33a-4c1a-8fac-1db499b3ccad&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-18 17:37:32 - INFO - 
主流程中发生严重错误: 'WebDriver' object has no attribute 'decode'
2025-08-18 17:37:32 - INFO - 准备重启流程...
2025-08-18 17:37:37 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54429,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:37:37 - INFO - 127.0.0.1:54429
2025-08-18 17:37:37 - INFO - <EMAIL>
2025-08-18 17:37:37 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:37:57 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:38:01 - INFO - 找到 Turnstile...
2025-08-18 17:38:01 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:38:05 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:38:11 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:38:11 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:38:11 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:38:11 - INFO - 成功！当前在订阅页面。
2025-08-18 17:38:11 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 17:38:11 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 17:38:21 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 17:38:24 - INFO - 代码信息: {
  "codeVerifier": "hfbI0_KcZowWI3dNG4ZeeEXjObhELiyHvnihpqt96Tc",
  "code_challenge": "QLkriH1Y_35hzSqsoX__ueLltJ4CvG4cOT9TP8ggTyw",
  "state": "ab145e4d-e85a-43d2-9cba-4a19e91e38a4"
}
2025-08-18 17:38:24 - INFO - ==================================================
2025-08-18 17:38:26 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_85a709191c60c111d28d6a045f774f79&state=ab145e4d-e85a-43d2-9cba-4a19e91e38a4&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-18 17:38:29 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 17:38:29 - INFO - 添加session: <EMAIL>   b1f917746671dbddf0c218c1b2de900d419757ee84d68bac26652774b0012013   https://d13.api.augmentcode.com/  2025-08-25T09:32:23Z
2025-08-18 17:38:29 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAURP_lrsHQ2yeu_BNS2lvTQAspqDHqv9sQFy5nMufMC4aVSrKZ8g7nvdyogWBTnJ9DtongDNDANd4p_-Xo1-G2URmirwUlG-e3MhYDZ0pw5k1Arr2SAXGs87xkV0mF2DGhe86M6JVhXDdwWA5BFU3b_TEyLaXs-rq4bMmW3S9uonJa8hwzwY84fnE0jpTsWjUaaoUMoTXoeFvvhbPcoHYKPl9jJUTj.aKL0kg.wAj2KmqykUvQAIQfwCk2MLW4NQ4
2025-08-18 17:38:29 - INFO - 
自动化流程成功完成！
2025-08-18 17:38:29 - INFO - 添加第42个
2025-08-18 17:38:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54653,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:38:51 - INFO - 127.0.0.1:54653
2025-08-18 17:38:51 - INFO - <EMAIL>
2025-08-18 17:38:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:39:17 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:39:21 - INFO - 找到 Turnstile...
2025-08-18 17:39:23 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:39:26 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:39:26 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:39:36 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:39:36 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:39:39 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:39:39 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:39:43 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54786,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:39:43 - INFO - 127.0.0.1:54786
2025-08-18 17:39:43 - INFO - <EMAIL>
2025-08-18 17:39:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:40:09 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:40:13 - INFO - 找到 Turnstile...
2025-08-18 17:40:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:40:18 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:40:28 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:40:28 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:40:28 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:40:28 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:40:28 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:40:31 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:40:31 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:40:35 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54940,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:40:35 - INFO - 127.0.0.1:54940
2025-08-18 17:40:36 - INFO - <EMAIL>
2025-08-18 17:40:36 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:41:01 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:41:06 - INFO - 找到 Turnstile...
2025-08-18 17:41:07 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:41:10 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:41:10 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:41:21 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:41:31 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:41:31 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:41:31 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:41:34 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:41:34 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:41:38 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55161,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:41:38 - INFO - 127.0.0.1:55161
2025-08-18 17:41:38 - INFO - <EMAIL>
2025-08-18 17:41:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:42:00 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:42:04 - INFO - 找到 Turnstile...
2025-08-18 17:42:04 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:42:07 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:42:07 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:42:19 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:42:48 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:42:48 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:42:48 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:42:51 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:42:51 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:42:55 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55318,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:42:55 - INFO - 127.0.0.1:55318
2025-08-18 17:42:55 - INFO - <EMAIL>
2025-08-18 17:42:55 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:43:22 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:43:26 - INFO - 找到 Turnstile...
2025-08-18 17:43:27 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:43:31 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:43:42 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:43:42 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:43:49 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:43:49 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:43:49 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:43:52 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:43:52 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:43:56 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55509,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:43:56 - INFO - 127.0.0.1:55509
2025-08-18 17:44:15 - INFO - <EMAIL>
2025-08-18 17:44:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:44:42 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:44:46 - INFO - 找到 Turnstile...
2025-08-18 17:44:49 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:44:52 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:45:02 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:45:02 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:45:12 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:45:12 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:45:12 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:45:15 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:45:15 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:45:18 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55745,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:45:18 - INFO - 127.0.0.1:55745
2025-08-18 17:45:19 - INFO - <EMAIL>
2025-08-18 17:45:19 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:45:42 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:45:46 - INFO - 找到 Turnstile...
2025-08-18 17:45:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:45:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:45:49 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:45:49 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:45:49 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:46:04 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:46:04 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:46:08 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55908,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:46:08 - INFO - 127.0.0.1:55908
2025-08-18 17:46:08 - INFO - <EMAIL>
2025-08-18 17:46:08 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:46:44 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:46:48 - INFO - 找到 Turnstile...
2025-08-18 17:46:53 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:46:56 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:47:12 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:47:12 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:47:43 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 17:47:43 - INFO - 鼠标左键放开操作完成。
2025-08-18 17:47:43 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 17:47:44 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 17:47:47 - INFO - 验证成功，已进入目标页面。
2025-08-18 17:47:47 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:47:47 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:47:50 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:47:50 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:47:54 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56198,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:47:54 - INFO - 127.0.0.1:56198
2025-08-18 17:47:54 - INFO - <EMAIL>
2025-08-18 17:47:54 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:48:33 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:48:37 - INFO - 找到 Turnstile...
2025-08-18 17:48:37 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:48:40 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:48:40 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:48:40 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:48:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:49:00 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:49:00 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:49:04 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56419,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:49:04 - INFO - 127.0.0.1:56419
2025-08-18 17:49:05 - INFO - <EMAIL>
2025-08-18 17:49:05 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:49:46 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:49:50 - INFO - 找到 Turnstile...
2025-08-18 17:49:53 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:49:56 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:49:56 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:50:12 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:50:15 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:50:15 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:50:15 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:50:18 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:50:18 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:50:22 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56579,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:50:22 - INFO - 127.0.0.1:56579
2025-08-18 17:50:23 - INFO - <EMAIL>
2025-08-18 17:50:23 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:51:01 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:51:05 - INFO - 找到 Turnstile...
2025-08-18 17:51:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:51:11 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:51:11 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:51:11 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:51:11 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:51:31 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:51:31 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:51:34 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56740,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:51:34 - INFO - 127.0.0.1:56740
2025-08-18 17:51:35 - INFO - <EMAIL>
2025-08-18 17:51:35 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:52:05 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:52:05 - INFO - 准备重启流程...
2025-08-18 17:52:12 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56877,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:52:12 - INFO - 127.0.0.1:56877
2025-08-18 17:52:13 - INFO - <EMAIL>
2025-08-18 17:52:13 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:52:58 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:53:02 - INFO - 找到 Turnstile...
2025-08-18 17:53:03 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:53:06 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:53:20 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:53:20 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:53:28 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:53:28 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:53:28 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:53:31 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:53:31 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:53:36 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57046,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:53:36 - INFO - 127.0.0.1:57046
2025-08-18 17:53:36 - INFO - <EMAIL>
2025-08-18 17:53:36 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:54:06 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:54:11 - INFO - 找到 Turnstile...
2025-08-18 17:54:11 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:54:14 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:54:29 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:54:29 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:54:31 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:54:31 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:54:31 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:54:34 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:54:34 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:54:38 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57256,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:54:38 - INFO - 127.0.0.1:57256
2025-08-18 17:54:38 - INFO - <EMAIL>
2025-08-18 17:54:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:55:15 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:55:19 - INFO - 找到 Turnstile...
2025-08-18 17:55:23 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:55:27 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:55:37 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:55:37 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:55:51 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:55:51 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:55:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:55:54 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:55:54 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:55:59 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57432,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:55:59 - INFO - 127.0.0.1:57432
2025-08-18 17:55:59 - INFO - <EMAIL>
2025-08-18 17:55:59 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:56:44 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:56:48 - INFO - 找到 Turnstile...
2025-08-18 17:56:50 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:56:53 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:56:53 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:57:03 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:57:34 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 17:57:34 - INFO - 鼠标左键放开操作完成。
2025-08-18 17:57:34 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 17:57:34 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 17:57:37 - INFO - 验证成功，已进入目标页面。
2025-08-18 17:57:37 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:57:37 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:57:40 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:57:40 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:57:45 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57889,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:57:45 - INFO - 127.0.0.1:57889
2025-08-18 17:57:45 - INFO - <EMAIL>
2025-08-18 17:57:45 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:58:18 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:58:22 - INFO - 找到 Turnstile...
2025-08-18 17:58:25 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:58:28 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:58:45 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:58:45 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 17:58:56 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 17:58:56 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:58:56 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 17:58:59 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 17:58:59 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 17:59:03 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58142,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 17:59:03 - INFO - 127.0.0.1:58142
2025-08-18 17:59:04 - INFO - <EMAIL>
2025-08-18 17:59:04 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 17:59:36 - INFO - 获取 cap_value 验证码成功...
2025-08-18 17:59:40 - INFO - 找到 Turnstile...
2025-08-18 17:59:43 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 17:59:46 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 17:59:46 - INFO - 验证码已提交，等待跳转...
2025-08-18 17:59:46 - INFO - 
第 1/5 次尝试注册...
2025-08-18 17:59:46 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:00:05 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 18:00:05 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 18:00:10 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58311,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:00:10 - INFO - 127.0.0.1:58311
2025-08-18 18:00:11 - INFO - <EMAIL>
2025-08-18 18:00:11 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:00:44 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:00:48 - INFO - 找到 Turnstile...
2025-08-18 18:00:48 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:00:52 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:01:03 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:01:03 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:01:07 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:01:07 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:01:07 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:01:10 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 18:01:10 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 18:01:15 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58443,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:01:15 - INFO - 127.0.0.1:58443
2025-08-18 18:01:15 - INFO - <EMAIL>
2025-08-18 18:01:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:01:43 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:01:47 - INFO - 找到 Turnstile...
2025-08-18 18:01:48 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:01:52 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:02:02 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:02:02 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:02:29 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:02:29 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:02:29 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:02:32 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 18:02:32 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 18:02:36 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58700,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:02:36 - INFO - 127.0.0.1:58700
2025-08-18 18:02:37 - INFO - <EMAIL>
2025-08-18 18:02:37 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:03:03 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:03:06 - INFO - 找到 Turnstile...
2025-08-18 18:03:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:03:11 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:03:22 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:03:22 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:03:24 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:03:24 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:03:24 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:03:27 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 18:03:27 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 18:03:32 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58830,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:03:32 - INFO - 127.0.0.1:58830
2025-08-18 18:03:33 - INFO - <EMAIL>
2025-08-18 18:03:33 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:03:59 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:04:03 - INFO - 找到 Turnstile...
2025-08-18 18:04:13 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:04:16 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:04:22 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:04:22 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:04:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:04:32 - INFO -    等待 3.38 秒...
2025-08-18 18:04:40 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:04:40 - INFO - 
第 2/5 次尝试注册...
2025-08-18 18:04:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:04:49 - INFO -    等待 5.00 秒...
2025-08-18 18:04:57 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:04:57 - INFO - 
第 3/5 次尝试注册...
2025-08-18 18:04:57 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:05:06 - INFO -    等待 4.80 秒...
2025-08-18 18:05:15 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:05:15 - INFO - 
第 4/5 次尝试注册...
2025-08-18 18:05:15 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:05:24 - INFO -    等待 3.20 秒...
2025-08-18 18:05:33 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:05:33 - INFO - 
第 5/5 次尝试注册...
2025-08-18 18:05:33 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:05:42 - INFO -    等待 4.06 秒...
2025-08-18 18:05:51 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:05:51 - INFO - 已达到最大重试次数，注册失败。
2025-08-18 18:05:51 - INFO - 
所有重试均告失败，流程终止。
2025-08-18 18:05:51 - INFO - 添加第43个
2025-08-18 18:06:15 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59214,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:06:15 - INFO - 127.0.0.1:59214
2025-08-18 18:06:15 - INFO - <EMAIL>
2025-08-18 18:06:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:06:59 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:06:59 - INFO - 准备重启流程...
2025-08-18 18:07:02 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59267,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:07:02 - INFO - 127.0.0.1:59267
2025-08-18 18:07:03 - INFO - <EMAIL>
2025-08-18 18:07:03 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:07:46 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:07:46 - INFO - 准备重启流程...
2025-08-18 18:07:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59330,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:07:51 - INFO - 127.0.0.1:59330
2025-08-18 18:07:52 - INFO - <EMAIL>
2025-08-18 18:07:52 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:08:27 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:08:31 - INFO - 找到 Turnstile...
2025-08-18 18:09:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:09:18 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:09:19 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:09:32 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:09:52 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:09:52 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:09:52 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:09:52 - INFO - 成功！当前在订阅页面。
2025-08-18 18:09:52 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:09:52 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:09:52 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:09:55 - INFO - 代码信息: {
  "codeVerifier": "ogHFTFmV50WIjjDf-UaY_CLoLtU9wBdMFCZiIc7ZcSE",
  "code_challenge": "Hba3Wihe1AUWeXn-0Bp7AZtq2zwkZjUwBJa5i9LTauM",
  "state": "09bca707-0bf8-4337-8e4a-7370ec9a1716"
}
2025-08-18 18:09:55 - INFO - ==================================================
2025-08-18 18:09:56 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_11da9c74026466a3a208edc5703aaffd&state=09bca707-0bf8-4337-8e4a-7370ec9a1716&tenant_url=https%3A%2F%2Fd2.api.augmentcode.com%2F
2025-08-18 18:09:58 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:09:58 - INFO - 添加session: <EMAIL>   5ba9dd0e9d701f9fb838cd4533c77efca93e30883bdc95364c4b84c407bfc022   https://d2.api.augmentcode.com/  2025-08-25T10:09:36Z
2025-08-18 18:09:58 - INFO - email:<EMAIL> === cookie:.eJxNjcsKgzAURP_lrrXkaRJX_RO5mpsSNFGiFqTtv1ekiy5nmHPmBd1CJWGmvEG7lZ0qCJjidHQZE0ELUMEjPin_5eiXbl-pdNGfBSWM07uxKEKPTEnubRDS-OCNouac5zkPJ6mtsJZxw611jimjFKvg0lyG0xRGHA9utNac68bd14Rl8_MwUrnNeYqZ4Edcx2rwhJr5mqRmteod1U6LpraGS-ECVzggfL7n70VT.aKL78w.S4TE5xbgSx98lSxdhBtCJ5B4XyU
2025-08-18 18:09:58 - INFO - 
自动化流程成功完成！
2025-08-18 18:09:58 - INFO - 添加第44个
2025-08-18 18:10:19 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59753,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:10:19 - INFO - 127.0.0.1:59753
2025-08-18 18:10:20 - INFO - <EMAIL>
2025-08-18 18:10:20 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:11:25 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:11:29 - INFO - 找到 Turnstile...
2025-08-18 18:11:34 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:11:38 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:11:52 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:11:52 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:12:14 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:12:14 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:12:14 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:12:14 - INFO - 成功！当前在订阅页面。
2025-08-18 18:12:14 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:12:14 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:12:14 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:12:17 - INFO - 代码信息: {
  "codeVerifier": "pO3ksx7nWQryChO_pAKBMHMYpvbo7JuFKjT5TGI38iQ",
  "code_challenge": "HZJGzq2DzjLOMwvNj_MASz5QalmGwC5Zk4DdNOc5Ts8",
  "state": "c8348fac-b9ca-4177-9424-6f982ade73b0"
}
2025-08-18 18:12:17 - INFO - ==================================================
2025-08-18 18:12:18 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_ee64c2aaa67fa5e7cab541e6dbca41de&state=c8348fac-b9ca-4177-9424-6f982ade73b0&tenant_url=https%3A%2F%2Fd7.api.augmentcode.com%2F
2025-08-18 18:12:20 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:12:20 - INFO - 添加session: <EMAIL>   3836ebceec9610ac10d9a136d0e6a5966702d1658d6ec9288d9d2807ed1172c1   https://d7.api.augmentcode.com/  2025-08-25T10:11:55Z
2025-08-18 18:12:20 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2Doz9LCyTchS7vVRlpIBROjvruEePA4k_m-ecGwcEmUOa_Qr2XjCgKlOD2HTImhB6jgEh-c_3L0y7DduQzR7wUnitO7tSSDQ9RKeBukMr4h5DHs8zxnt5MSm9YqbaTVtlFGNworODSHYTddt5mfwiCiEFbo8z1RWf3sblxOc55iZvgRx3Enpe2QRU3CtbXuvKxJGlEbx9q6jsagDXy-0s5FLQ.aKL8gg.Neo4hIc0fW9FeZSCRpS2_XZKfn4
2025-08-18 18:12:20 - INFO - 
自动化流程成功完成！
2025-08-18 18:12:20 - INFO - 添加第45个
2025-08-18 18:12:37 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60067,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:12:37 - INFO - 127.0.0.1:60067
2025-08-18 18:12:38 - INFO - <EMAIL>
2025-08-18 18:12:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:13:21 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:13:26 - INFO - 找到 Turnstile...
2025-08-18 18:13:29 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:13:32 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:14:18 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:14:18 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:14:48 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 18:14:48 - INFO - 鼠标左键放开操作完成。
2025-08-18 18:14:48 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 18:14:49 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 18:14:52 - INFO - 验证成功，已进入目标页面。
2025-08-18 18:14:52 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:14:52 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:14:55 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 18:14:55 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 18:14:58 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60309,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:14:58 - INFO - 127.0.0.1:60309
2025-08-18 18:14:59 - INFO - <EMAIL>
2025-08-18 18:14:59 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:17:01 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:17:05 - INFO - 找到 Turnstile...
2025-08-18 18:17:11 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:17:14 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:17:14 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:17:14 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:17:14 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:17:49 - INFO -    等待 4.27 秒...
2025-08-18 18:17:57 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:17:57 - INFO - 
第 2/5 次尝试注册...
2025-08-18 18:17:57 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:17:57 - INFO - 成功！当前在订阅页面。
2025-08-18 18:17:57 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:17:57 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:18:08 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 18:18:11 - INFO - 代码信息: {
  "codeVerifier": "Inr09MX0DzPLln0x93Xn4FMN26cBf4NpySU6_92Abfk",
  "code_challenge": "vamcmFXTEgXKJMobVUI2bHaYCzFe2B6TxGEPagR7w5A",
  "state": "37800531-ac66-4d45-874a-655e0bf5fe58"
}
2025-08-18 18:18:11 - INFO - ==================================================
2025-08-18 18:18:12 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_d55abb95e582563165170b631510a001&state=37800531-ac66-4d45-874a-655e0bf5fe58&tenant_url=https%3A%2F%2Fd4.api.augmentcode.com%2F
2025-08-18 18:18:14 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:18:14 - INFO - 添加session: <EMAIL>   45cf7cbc00bf045477748126a601ab0dc2f4dc6ee80520f60b887c759312728c   https://d4.api.augmentcode.com/  2025-08-25T10:17:44Z
2025-08-18 18:18:14 - INFO - email:<EMAIL> === cookie:.eJxNjksOwjAMBe_idYuapG4CK25S5eNARONWoYAQcHeiigXLZ3vG7wXjQiVbJl7hsJYbNRBtTtNzZJsJDgANnNKd-C-nsIy3K5UxhTqgbNP0HoyV0XvdKxFMlEoHIQcRVD3nmX0lUQthOqO10ohGolKqgU2zGaopZH6cRd2iEHuUx2u2ZQ2zv1DZzTwlJvgR2-OhN86hcq3zFNu-M9Ra2bk2aom4d7VGj_D5ArknRVM.aKL95A.8UvXyOg_Nrd27HUr6-K85SRdqKU
2025-08-18 18:18:14 - INFO - 
自动化流程成功完成！
2025-08-18 18:18:14 - INFO - 添加第46个
2025-08-18 18:18:32 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60628,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:18:32 - INFO - 127.0.0.1:60628
2025-08-18 18:18:32 - INFO - <EMAIL>
2025-08-18 18:18:32 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:19:43 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:19:47 - INFO - 找到 Turnstile...
2025-08-18 18:20:27 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:24:48 - INFO - 
主流程中发生严重错误: maximum recursion depth exceeded
2025-08-18 18:24:48 - INFO - 准备重启流程...
2025-08-18 18:24:52 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61180,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:24:52 - INFO - 127.0.0.1:61180
2025-08-18 18:24:52 - INFO - <EMAIL>
2025-08-18 18:24:52 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:25:37 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:25:37 - INFO - 准备重启流程...
2025-08-18 18:25:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61220,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:25:40 - INFO - 127.0.0.1:61220
2025-08-18 18:25:41 - INFO - <EMAIL>
2025-08-18 18:25:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:26:21 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:26:25 - INFO - 找到 Turnstile...
2025-08-18 18:26:28 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:26:32 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:26:43 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:26:43 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:27:14 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 18:27:14 - INFO - 鼠标左键放开操作完成。
2025-08-18 18:27:15 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 18:27:15 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 18:27:18 - INFO - 验证成功，已进入目标页面。
2025-08-18 18:27:18 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:27:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:27:18 - INFO - 成功！当前在订阅页面。
2025-08-18 18:27:18 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:27:18 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:27:18 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:27:21 - INFO - 代码信息: {
  "codeVerifier": "m_gSoLKy-N5rahz67fWmo1WA3ePnqdhDtCsd466D68c",
  "code_challenge": "evlB8WlVBk5rH161ozANjGjoEJbkXq-un2mRMDJwprA",
  "state": "2caaa273-6e60-4499-8a0b-a9b772c095ee"
}
2025-08-18 18:27:21 - INFO - ==================================================
2025-08-18 18:27:22 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_8abedfa38f39b680b5b8b6f961cd5b1b&state=2caaa273-6e60-4499-8a0b-a9b772c095ee&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-18 18:27:24 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:27:24 - INFO - 添加session: <EMAIL>   e57ed28f9f9529b6f008eb2ac405445445a3d81b80b5f5cc645f9cc3f5311e7e   https://d10.api.augmentcode.com/  2025-08-25T10:26:54Z
2025-08-18 18:27:24 - INFO - email:<EMAIL> === cookie:.eJxNjUFuwzAMBP_Csx1YlGTJOfUnBiNSgRCLDhQnQNH27zWMHHLcxc7sD8x3aZVUdIPz1p7SQaZalu9ZqQqcATq4lpfoRy58n58PaXPhvZBKZfkdI2HOjM4ajhltYDfJZPI-11XTTloTBm88Tg4xjGEYzdjBoTkMu-nVNJMJ3nuDdohfj0pt4zXdpJ1WXYoKvInj2NkpJsOhjz753mXinpJNPUW8oI-WXYrw9w_J1kVS.aKMACg.kB2XKN6TErIZMM7FJZGZb4yK7Lo
2025-08-18 18:27:24 - INFO - 
自动化流程成功完成！
2025-08-18 18:27:24 - INFO - 添加第47个
2025-08-18 18:27:47 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61706,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:27:47 - INFO - 127.0.0.1:61706
2025-08-18 18:27:48 - INFO - <EMAIL>
2025-08-18 18:27:48 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:28:47 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:28:51 - INFO - 找到 Turnstile...
2025-08-18 18:28:51 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:29:14 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:30:37 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:30:37 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:30:42 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:30:42 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:30:42 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:31:25 - INFO -    等待 4.87 秒...
2025-08-18 18:31:34 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:31:34 - INFO - 
第 2/5 次尝试注册...
2025-08-18 18:31:34 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:31:37 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 18:31:37 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 18:31:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62003,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:31:40 - INFO - 127.0.0.1:62003
2025-08-18 18:31:41 - INFO - <EMAIL>
2025-08-18 18:31:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:32:23 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:32:23 - INFO - 准备重启流程...
2025-08-18 18:32:25 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62119,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:32:25 - INFO - 127.0.0.1:62119
2025-08-18 18:32:26 - INFO - <EMAIL>
2025-08-18 18:32:26 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:32:55 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:32:59 - INFO - 找到 Turnstile...
2025-08-18 18:32:59 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:33:03 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:33:48 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:33:48 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:33:59 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:33:59 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:33:59 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:33:59 - INFO - 成功！当前在订阅页面。
2025-08-18 18:33:59 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:33:59 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:33:59 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:34:02 - INFO - 代码信息: {
  "codeVerifier": "FPdzebR9ouKRVLA7WL0PUKpexvNJ5qvAnlUYlVdRTkE",
  "code_challenge": "RT3eQLOlBofHJtkqltSerhmiNZSK6tWudPrKjZ4etxg",
  "state": "e1fc449e-f759-42d7-8f0a-7e91fdf6d4f9"
}
2025-08-18 18:34:02 - INFO - ==================================================
2025-08-18 18:34:02 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_e5ed9dca69c5529510c007c0e1ea5150&state=e1fc449e-f759-42d7-8f0a-7e91fdf6d4f9&tenant_url=https%3A%2F%2Fd3.api.augmentcode.com%2F
2025-08-18 18:34:04 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:34:04 - INFO - 添加session: <EMAIL>   4ec3a683dc9c97d7517b31c28b7c076a12537a96fadaf586ddcf3bccaed7a43f   https://d3.api.augmentcode.com/  2025-08-25T10:34:04.067388017Z
2025-08-18 18:34:04 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3YELIshp_yJhWGdohocYadVlPbfg6wccpzRvDdPGG9csy9cdjjv9c4dzD6n5TEWnxnOAB1c0w-Xj5zibbxvXMcUW8HZp-XPWI9CDE6jjHZWOEQiZ8LU5mUtoZHaDYoUWiERnVDaSdfBoTkMzbR98eNXDkQklTV42bKve1zDN9fTWpZUGN7EcSxd0FNz9gaj7rWMvncCbS8nCmhIEs4E_y9uT0Pj.aKMBmg.V2nQYSgAuH-aJRa5gLpx9ueVDiw
2025-08-18 18:34:04 - INFO - 
自动化流程成功完成！
2025-08-18 18:34:04 - INFO - 添加第48个
2025-08-18 18:34:20 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62338,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:34:20 - INFO - 127.0.0.1:62338
2025-08-18 18:34:20 - INFO - <EMAIL>
2025-08-18 18:34:20 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:35:17 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:35:21 - INFO - 找到 Turnstile...
2025-08-18 18:35:48 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:35:51 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:35:57 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:35:57 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:36:27 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 18:36:27 - INFO - 鼠标左键放开操作完成。
2025-08-18 18:36:28 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 18:36:28 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 18:36:31 - INFO - 验证成功，已进入目标页面。
2025-08-18 18:36:31 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:36:31 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:36:31 - INFO - 成功！当前在订阅页面。
2025-08-18 18:36:31 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:36:31 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:36:31 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:36:34 - INFO - 代码信息: {
  "codeVerifier": "U0JNUubIA77zokOHKO7MaX4cO2rQhv6v2Dv1oglifjo",
  "code_challenge": "ZTgF6uao4alRYO0GNwMcOD1z4BjtEjw4rCrjUiJCZE8",
  "state": "598f5f8e-38f9-426d-bae1-883e623cd849"
}
2025-08-18 18:36:34 - INFO - ==================================================
2025-08-18 18:36:35 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_eaa85217eebace841d8c418e495e06dc&state=598f5f8e-38f9-426d-bae1-883e623cd849&tenant_url=https%3A%2F%2Fd7.api.augmentcode.com%2F
2025-08-18 18:36:37 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:36:37 - INFO - 添加session: <EMAIL>   c9c79e30a816cbca252ff0e083aa0a9da40cd58ca3a27ae97f22f154a1a553d2   https://d7.api.augmentcode.com/  2025-08-25T10:36:01Z
2025-08-18 18:36:37 - INFO - email:<EMAIL> === cookie:.eJxNjcsOwiAURP_lrlvD5Y0r_6SBcjHEAgariVH_3aZx4XImc868YLpSL75SXeG49jsNkHzJy3OqvhAcAQY45wfVv5zjdbrfqE85bgUVn5e3tl4wzpgUGG3iwkTDJaLZ5rXVeSNRMq2ctVIwKTUqN8Au2fnNM7dLi2iUUii40qdb8X2Nbb5QP7S65ErwI_bb5IJwPOgxsRRGiSGMTgQ1SsO0RW747D18vsX1Q6Q.aKMCMw.GCJ_zFnnh-FqBzoAv4G4phh_C0Q
2025-08-18 18:36:37 - INFO - 
自动化流程成功完成！
2025-08-18 18:36:37 - INFO - 添加第49个
2025-08-18 18:36:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62651,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:36:51 - INFO - 127.0.0.1:62651
2025-08-18 18:36:52 - INFO - <EMAIL>
2025-08-18 18:36:52 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:37:18 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:37:22 - INFO - 找到 Turnstile...
2025-08-18 18:37:22 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:37:25 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:38:09 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:38:09 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:38:36 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:38:36 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:38:36 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:38:39 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 18:38:39 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 18:38:42 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62888,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:38:42 - INFO - 127.0.0.1:62888
2025-08-18 18:38:43 - INFO - <EMAIL>
2025-08-18 18:38:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:39:44 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:39:48 - INFO - 找到 Turnstile...
2025-08-18 18:40:16 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:40:19 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:41:02 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:41:02 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:41:08 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:41:08 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:41:08 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:41:35 - INFO -    等待 3.48 秒...
2025-08-18 18:41:44 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:41:44 - INFO - 
第 2/5 次尝试注册...
2025-08-18 18:41:44 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:41:44 - INFO - 成功！当前在订阅页面。
2025-08-18 18:41:44 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:41:44 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:41:55 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 18:41:58 - INFO - 代码信息: {
  "codeVerifier": "QPgpIP3yYZbIgyTNNh1Y28vd13BWeFMXKoCk2xNenow",
  "code_challenge": "NDP5ULnQDpYHNAYLRHmCpNwcAVLArjvhOouXIWGLikY",
  "state": "ff80567c-0824-4799-a496-bd8345649b97"
}
2025-08-18 18:41:58 - INFO - ==================================================
2025-08-18 18:41:59 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_9cc81bb5dc504ad371dbdaab5e8b033b&state=ff80567c-0824-4799-a496-bd8345649b97&tenant_url=https%3A%2F%2Fd14.api.augmentcode.com%2F
2025-08-18 18:42:01 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:42:01 - INFO - 添加session: <EMAIL>   64a162b445c97d42eec72bfbe00d3f6614bea495f37dcc4756a51b45998958f2   https://d14.api.augmentcode.com/  2025-08-25T10:41:38Z
2025-08-18 18:42:01 - INFO - email:<EMAIL> === cookie:.eJxNjUtuxCAQRO_SazvCNND0rOYmFoGeCMW0LeyJlN_dY02yyLJK9V59wrxJb0lFD7gc_S4D3FKry_usqQlcAAZ4qW-i_3It23zfpc-1nIW0VJevEBMaG9DhVOLNIhVilmjPua6aT9I6gxYjE6MNLjhHOMBD8zCcJv3I-zaR935CZ-i6t9SPsuZX6U-rLlUF_ojf44RRxOPIE9HojEkjM8cxF7I-P-eQ2cH3D2PJRF8.aKMDdw.1mOdBsdPLHCyTnP0fq0ALL8xTp4
2025-08-18 18:42:01 - INFO - 
自动化流程成功完成！
2025-08-18 18:42:01 - INFO - 添加第50个
2025-08-18 18:42:15 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63224,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:42:15 - INFO - 127.0.0.1:63224
2025-08-18 18:42:15 - INFO - <EMAIL>
2025-08-18 18:42:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:43:25 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:43:29 - INFO - 找到 Turnstile...
2025-08-18 18:43:29 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:43:33 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:43:33 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:43:42 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:43:53 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:43:53 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:43:53 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:43:53 - INFO - 成功！当前在订阅页面。
2025-08-18 18:43:53 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:43:53 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:43:53 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:43:56 - INFO - 代码信息: {
  "codeVerifier": "fQhaKcfbG0kIJcVmuinzR1NFHOK-TXmim43IyXZo8Ek",
  "code_challenge": "BE2Kz-FiFWJKHcNiFVaaWYstLdD8Vcr5GsafExt_Aw4",
  "state": "767e2cb8-97ac-4598-a525-bee7e8d4d97d"
}
2025-08-18 18:43:56 - INFO - ==================================================
2025-08-18 18:43:57 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_e0f488aeabfd3064ddef5294d4b39d94&state=767e2cb8-97ac-4598-a525-bee7e8d4d97d&tenant_url=https%3A%2F%2Fd3.api.augmentcode.com%2F
2025-08-18 18:43:59 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:43:59 - INFO - 添加session: <EMAIL>   dd34b20e8c28732e73f497a414b2bf765cddd461df3d23669e457974dbe2337f   https://d3.api.augmentcode.com/  2025-08-25T10:43:45Z
2025-08-18 18:43:59 - INFO - email:<EMAIL> === cookie:.eJxNjsFuwyAQRP9lz3bEghdITv0TC8M6QjXrCDtVoiT_XmT10OOMZt7MC8Yb1xKEZYfLXu_cwRxKXp6jhMJwAejgmn9Y_umcbuN94zrm1AwuIS9v64NRJunBYPKzNi6dyUWPLS6rxNa0Dq22lixp5TQpb3UHB-YgNNJju06CjojQOINfWwl1T2v85npaZcnC8Nc4hhVxmFCHPimM_WAG13ucU09etx-DnUw8w-cXdnxEUg.aKMD7Q.-gjY_6QP-fVqMf4PWHw54GKbRxw
2025-08-18 18:43:59 - INFO - 
自动化流程成功完成！
2025-08-18 18:43:59 - INFO - 添加第51个
2025-08-18 18:44:16 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63506,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:44:16 - INFO - 127.0.0.1:63506
2025-08-18 18:44:17 - INFO - <EMAIL>
2025-08-18 18:44:17 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:45:14 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:45:18 - INFO - 找到 Turnstile...
2025-08-18 18:46:00 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:46:04 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:46:04 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:46:50 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:46:50 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:46:56 - INFO -    等待 4.87 秒...
2025-08-18 18:47:04 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:47:04 - INFO - 
第 2/5 次尝试注册...
2025-08-18 18:47:04 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:47:04 - INFO - 成功！当前在订阅页面。
2025-08-18 18:47:04 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:47:04 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:47:14 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 18:47:17 - INFO - 代码信息: {
  "codeVerifier": "_9a_m-wQqV-DRR3Y3fWgdR_ttOmfAW_r6cAnGIe49VY",
  "code_challenge": "O2mNQ5-4fC6CmH0PdaMiZ-tkx7rLz20q0WhCVJ_p_P8",
  "state": "a1f46455-7abc-4298-9ab2-db3363f7e286"
}
2025-08-18 18:47:17 - INFO - ==================================================
2025-08-18 18:47:18 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_dd394b0b8e11f56e4b8a8600c1b1b393&state=a1f46455-7abc-4298-9ab2-db3363f7e286&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-18 18:47:20 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:47:20 - INFO - 添加session: <EMAIL>   f3f33007f9d3e7cae74d7f8eecbe26343798a181af93e75771475902c8c75d87   https://d12.api.augmentcode.com/  2025-08-25T10:46:53Z
2025-08-18 18:47:20 - INFO - email:<EMAIL> === cookie:.eJxNjUuOgzAQRO_Sa4hs2t-s5ibI0M3IGtxEDoyCktw9CM1illWq9-oJ_Y1rScKywnWtGzcwpZLnvZdUGK4ADXznX5Z_OdOt3-5c-0xHwSXl-eVCQmWMMqgpTB16ikQx0jGXRcaDtLbTykejtdLGO_Q6NHBqTsNhqjI9du2ttRqDxa97SXWlZfzhellkzsLwR5zHXik3oMMWQxdbY93QDtFxS8HzNFIaGRO8P4YaRQ8.aKMEtg.-QfOrxpqcMhcMPnfQdktLDgFGU0
2025-08-18 18:47:20 - INFO - 
自动化流程成功完成！
2025-08-18 18:47:20 - INFO - 添加第52个
2025-08-18 18:47:37 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64057,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:47:37 - INFO - 127.0.0.1:64057
2025-08-18 18:47:38 - INFO - <EMAIL>
2025-08-18 18:47:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:48:20 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:48:20 - INFO - 准备重启流程...
2025-08-18 18:48:22 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64187,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:48:22 - INFO - 127.0.0.1:64187
2025-08-18 18:48:23 - INFO - <EMAIL>
2025-08-18 18:48:23 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:48:57 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:49:01 - INFO - 找到 Turnstile...
2025-08-18 18:49:42 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:49:45 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:49:45 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:49:45 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:49:45 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:50:54 - INFO -    等待 3.93 秒...
2025-08-18 18:51:01 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:51:01 - INFO - 
第 2/5 次尝试注册...
2025-08-18 18:51:01 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:51:01 - INFO - 成功！当前在订阅页面。
2025-08-18 18:51:01 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:51:01 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:51:01 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:51:04 - INFO - 代码信息: {
  "codeVerifier": "4igJKpynZxB6ykue4xdiFJ9-qvyzF2M5BLXe-0hulaQ",
  "code_challenge": "ueUjg058gJ3dp89gbnuxudDPVvpaHJiuamGrs7yHWN4",
  "state": "42a08d4d-ab7d-4ea9-aaf7-9bbf0324d84f"
}
2025-08-18 18:51:04 - INFO - ==================================================
2025-08-18 18:51:05 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_b3c6094524b76180012abc5eea626b39&state=42a08d4d-ab7d-4ea9-aaf7-9bbf0324d84f&tenant_url=https%3A%2F%2Fd14.api.augmentcode.com%2F
2025-08-18 18:51:07 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:51:07 - INFO - 添加session: <EMAIL>   8ea2ea97f047d1df5356f3bc697c55092afdad1d2e116e30e32288849d1db3ba   https://d14.api.augmentcode.com/  2025-08-25T10:51:06.760148346Z
2025-08-18 18:51:07 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgyAURP_lrrXh8hDoqn9iQC6GVNCgNmna_nuN6aLLmcw584J-oZpdobLBdas7NRBdTtOzLy4TXAEaGNODyl9OYen3lWqfwlFQdml6d8YJppCkwGAiFzo4CtHxY17mMhyk4KitQhRotbCGYdfAaTkFhyjncVtQK6VQMiVva3Z1C_Nwp3qZy5QKwY84fz3TXYzE2oDSt9Jz23oheSsVGW0NcvQSPl-bU0Sv.aKMFmg.AWQ5KiUao_r3NeeslkW-VWZOaTc
2025-08-18 18:51:07 - INFO - 
自动化流程成功完成！
2025-08-18 18:51:07 - INFO - 添加第53个
2025-08-18 18:51:28 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64493,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:51:28 - INFO - 127.0.0.1:64493
2025-08-18 18:51:29 - INFO - <EMAIL>
2025-08-18 18:51:29 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:52:26 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:52:31 - INFO - 找到 Turnstile...
2025-08-18 18:53:14 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:53:17 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:53:17 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:53:23 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:53:48 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:53:48 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:53:48 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:53:48 - INFO - 成功！当前在订阅页面。
2025-08-18 18:53:48 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:53:48 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:53:48 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:53:51 - INFO - 代码信息: {
  "codeVerifier": "hB_V2LRSlayWTLvdfPFAngUrdONrgDVGqP_yr_f0geo",
  "code_challenge": "Fyo2eBppOGZ9BfWXQkn940EPTeX4N73Hh2W4IDnuDPA",
  "state": "165a802a-1eaf-448e-9376-b0ca4ecafaca"
}
2025-08-18 18:53:51 - INFO - ==================================================
2025-08-18 18:53:52 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_00394340d8732ad52e1cd4e86df33a55&state=165a802a-1eaf-448e-9376-b0ca4ecafaca&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-18 18:53:53 - INFO - 添加会话出错
2025-08-18 18:53:53 - INFO - 
主流程中发生严重错误: 'WebDriver' object has no attribute 'decode'
2025-08-18 18:53:53 - INFO - 准备重启流程...
2025-08-18 18:53:56 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65213,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:53:56 - INFO - 127.0.0.1:65213
2025-08-18 18:53:56 - INFO - <EMAIL>
2025-08-18 18:53:56 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:54:19 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:54:23 - INFO - 找到 Turnstile...
2025-08-18 18:54:25 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:54:28 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:54:29 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:54:33 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:54:33 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:54:33 - INFO - 成功！当前在订阅页面。
2025-08-18 18:54:33 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:54:33 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:54:44 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 18:54:47 - INFO - 代码信息: {
  "codeVerifier": "lt_tchI7WsSHsxREO4gcU5WzEvq2JtZGf7WfQsJYdkQ",
  "code_challenge": "PdhQy4k2qDrSvgsIYknMgzE1Tjhy1Hydqp0KheeBaR0",
  "state": "63b660ac-1937-4039-b585-ea759b11b288"
}
2025-08-18 18:54:47 - INFO - ==================================================
2025-08-18 18:54:48 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_e82f4a2868094d433d70c0dbc24e6381&state=63b660ac-1937-4039-b585-ea759b11b288&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-18 18:54:50 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:54:50 - INFO - 添加session: <EMAIL>   527a15ab6a69fdb88344b7496041339bd314530eac5c8bbc0fe07dcb0da69238   https://d5.api.augmentcode.com/  2025-08-25T10:53:26Z
2025-08-18 18:54:50 - INFO - email:<EMAIL> === cookie:.eJxNjUEOgyAURO_y19oA8gFd9SYG5NNQBQ21TWrbu9eYLrqcybw3L-gXKslmyit0a7lTBcGmOD37bBNBB1DBJT4o_-Xol_5-o9JHvxeUbJzeytiGYRCy4d4E0WjvgnWc7fM852EntRaMISo0Bo1UqsW2gkNzGHbTdt3GjWtE5FIYPN-SLaufh5HKac5TzAQ_4jh2WkjW-FAHPmAth5Zqo7yuNUrujGilCxo-X8_qRS4.aKMGeA.27Zh0AIebmARW3lgopoCkNt6TsM
2025-08-18 18:54:50 - INFO - 
自动化流程成功完成！
2025-08-18 18:54:50 - INFO - 添加第54个
2025-08-18 18:55:13 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65384,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:55:13 - INFO - 127.0.0.1:65384
2025-08-18 18:55:13 - INFO - <EMAIL>
2025-08-18 18:55:13 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:56:09 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:56:13 - INFO - 找到 Turnstile...
2025-08-18 18:56:13 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:56:16 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:56:16 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:57:00 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:57:00 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:57:44 - INFO -    等待 3.36 秒...
2025-08-18 18:57:51 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 18:57:51 - INFO - 
第 2/5 次尝试注册...
2025-08-18 18:57:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:57:54 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 18:57:54 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 18:57:57 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:49949,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:57:57 - INFO - 127.0.0.1:49949
2025-08-18 18:57:58 - INFO - <EMAIL>
2025-08-18 18:57:58 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 18:58:51 - INFO - 获取 cap_value 验证码成功...
2025-08-18 18:58:54 - INFO - 找到 Turnstile...
2025-08-18 18:58:58 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 18:59:01 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 18:59:13 - INFO - 验证码已提交，等待跳转...
2025-08-18 18:59:13 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 18:59:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:59:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 18:59:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 18:59:21 - INFO - 成功！当前在订阅页面。
2025-08-18 18:59:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 18:59:21 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 18:59:21 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 18:59:24 - INFO - 代码信息: {
  "codeVerifier": "0iES6I0xAPNyr2DkVcnGVpnAztVt3Uvx5EguLgu5qo0",
  "code_challenge": "bkqDfP-EZy9rHOxb5JCuthx_gURlcBH8Slta7ZyBLIQ",
  "state": "893a4ade-9905-4b89-ad7e-afe9be674f84"
}
2025-08-18 18:59:24 - INFO - ==================================================
2025-08-18 18:59:25 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_aa10bf1edb3a0af55be8d9aa6d3239a6&state=893a4ade-9905-4b89-ad7e-afe9be674f84&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-18 18:59:27 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 18:59:27 - INFO - 添加session: <EMAIL>   9456fc0678ad3a6be10e41804096bd1ceee34dc3ec9f465b5a53a9c8b2efb3ac   https://d13.api.augmentcode.com/  2025-08-25T10:59:16Z
2025-08-18 18:59:27 - INFO - email:<EMAIL> === cookie:.eJxNjc0KwjAQhN9lz60kNr-efJOyzW5LsElLbAVR391QPHgZmGHmmxf0K5eEmfMGl63s3MCIKc7PPmNiuAA0MMUH5z8fae33O5c-Ug04YZzfxmEnTGDVSXLjubNEYtRB1npecqhLbY0UqkonpZBOWWEbODAHoZL2By2TtFprqbTw13vCstESblxOS55jZvgtjmPHTAOiaom8bpUyvnWDw9aIQH4wgoPx8PkC0YlFtA.aKMHjQ.23H7j4KhjGtEA7N3o2NKBcNUHwg
2025-08-18 18:59:27 - INFO - 
自动化流程成功完成！
2025-08-18 18:59:27 - INFO - 添加第55个
2025-08-18 18:59:50 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:50299,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 18:59:50 - INFO - 127.0.0.1:50299
2025-08-18 18:59:51 - INFO - <EMAIL>
2025-08-18 18:59:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:01:20 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:01:24 - INFO - 找到 Turnstile...
2025-08-18 19:01:26 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:01:29 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:01:35 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:01:35 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:02:03 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:02:03 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:02:03 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:02:03 - INFO - 成功！当前在订阅页面。
2025-08-18 19:02:03 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:02:03 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:02:03 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:02:06 - INFO - 代码信息: {
  "codeVerifier": "yw_msa18Zq7ICF1_CaJq8iTf94Mk7CWDWtLgySN4jKQ",
  "code_challenge": "F_8WHDQac_NrDLHgJktsAm4VK_VY8nist1fxLcV_QWo",
  "state": "7d7c4e14-c981-4b06-b711-c2345a962dad"
}
2025-08-18 19:02:06 - INFO - ==================================================
2025-08-18 19:02:07 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_64e067fa9c4c8c898fcfd43b951461ce&state=7d7c4e14-c981-4b06-b711-c2345a962dad&tenant_url=https%3A%2F%2Fd17.api.augmentcode.com%2F
2025-08-18 19:02:09 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:02:09 - INFO - 添加session: <EMAIL>   fade219c16f638c54b1efd5cf9763af71b2b4481611018f886d3da7919c6af0d   https://d17.api.augmentcode.com/  2025-08-25T11:01:38Z
2025-08-18 19:02:09 - INFO - email:<EMAIL> === cookie:.eJxNjcsOwiAQRf9l1q2B8u7KP2mgDIZYaEVqYtR_lzQu3Exy78w584Jpw5JsxlxhrGXHDoJNcXlO2SaEEaCDS3xg_svRb9N-xzJF3wpMNi5vqS0jmgjOqNdhYMqj40r7dp7XPDeStZUymhAzcG2oNEp0cGgOQzPtj1utVAkhaEPl-Z5sqX6dr1hOa15iRvgRx2MmgwzzoHonqes5477Xug1mglPcMUsGAZ8voYxE0A.aKMILw.BoSYBjEwbS0IaNtPVdaj-9GOP_k
2025-08-18 19:02:09 - INFO - 
自动化流程成功完成！
2025-08-18 19:02:09 - INFO - 添加第56个
2025-08-18 19:02:27 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:50866,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:02:27 - INFO - 127.0.0.1:50866
2025-08-18 19:02:27 - INFO - <EMAIL>
2025-08-18 19:02:27 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:03:02 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:03:06 - INFO - 找到 Turnstile...
2025-08-18 19:03:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:03:12 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:03:12 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:03:56 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:03:56 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:04:39 - INFO -    等待 3.70 秒...
2025-08-18 19:04:49 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:04:49 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:04:49 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:04:52 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 19:04:52 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 19:04:55 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51262,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:04:55 - INFO - 127.0.0.1:51262
2025-08-18 19:04:55 - INFO - <EMAIL>
2025-08-18 19:04:55 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:05:54 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:05:58 - INFO - 找到 Turnstile...
2025-08-18 19:06:39 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:06:43 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:07:20 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:07:20 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:07:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:07:22 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:07:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:07:27 - INFO -    等待 3.92 秒...
2025-08-18 19:07:35 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:07:35 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:07:35 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:07:35 - INFO - 成功！当前在订阅页面。
2025-08-18 19:07:35 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:07:35 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:07:35 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:07:38 - INFO - 代码信息: {
  "codeVerifier": "-i-fvff0Z2dVEdjGxaowq5pNzI11xV0WjIqoCgYVHSY",
  "code_challenge": "xbVoMiCZZ0GioVncUKA1QHhqvIKgkXo45FIRXrT04jg",
  "state": "4a1f7d1e-43f6-43db-b0fc-5e2692d7cfc3"
}
2025-08-18 19:07:38 - INFO - ==================================================
2025-08-18 19:07:39 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_88414f4615c480e869f847aef6c14b0a&state=4a1f7d1e-43f6-43db-b0fc-5e2692d7cfc3&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 19:07:41 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:07:41 - INFO - 添加session: <EMAIL>   32e62f61561ecdc1ca212d24342e91555116171952c314c9d1bc74afca95403e   https://d9.api.augmentcode.com/  2025-08-25T11:07:24Z
2025-08-18 19:07:41 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3aEYRdwTv0TC5slRTXrBDuVorb_XmT10OOM5r35gunOtQRhOeB61Cd3kELJ62uSUBiuAB3c8ifLv5zjfXruXKccW8El5PXb-mCUtzOaIfqkjYsJaVSxzWWTpZFW4YjkDbrRKT9opTs4LaegifabPN4HR0RDW5q3vYR6xG354HrZZM3C8Eecv3ZJNqSA_WyV6pGM7mfSutezdiM6QlIafn4BVxNDxw.aKMJew.YZtbXRrYdpTuoE1f-3QnRbXryRM
2025-08-18 19:07:41 - INFO - 
自动化流程成功完成！
2025-08-18 19:07:41 - INFO - 添加第57个
2025-08-18 19:07:56 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:51709,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:07:56 - INFO - 127.0.0.1:51709
2025-08-18 19:07:56 - INFO - <EMAIL>
2025-08-18 19:07:56 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:08:43 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:08:47 - INFO - 找到 Turnstile...
2025-08-18 19:08:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:08:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:08:56 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:08:56 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:09:05 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:09:05 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:09:05 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:09:05 - INFO - 成功！当前在订阅页面。
2025-08-18 19:09:05 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:09:05 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:09:05 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:09:08 - INFO - 代码信息: {
  "codeVerifier": "RN3Cgvt9jqe0TrD8dKvAXDL3fp2hZJVJ1hlRakylxyQ",
  "code_challenge": "Qc2Hwm-FUueQbVwoa-DySWXjMUlr6DfvY69hJ5pMzfQ",
  "state": "679f6a8f-387b-421e-a9c7-ee0b7fc5f8ef"
}
2025-08-18 19:09:08 - INFO - ==================================================
2025-08-18 19:09:09 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_4b255e901374b43b049d3de85a859102&state=679f6a8f-387b-421e-a9c7-ee0b7fc5f8ef&tenant_url=https%3A%2F%2Fd18.api.augmentcode.com%2F
2025-08-18 19:09:11 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:09:11 - INFO - 添加session: <EMAIL>   4e7a536ae25407ad4787b50ac128e1bbdc0d760da76425f1cf44ae9c50bd13f7   https://d18.api.augmentcode.com/  2025-08-25T11:08:59Z
2025-08-18 19:09:11 - INFO - email:<EMAIL> === cookie:.eJxNjctuwjAQRf9l1gmKZ_wKq_5JNI4nyGrsUBOQEPDvtaIuuryvc18wXaVmLlJ2OO_1Lh0snNP6nApngTNAB5f0kPJPp3id7jepU4rNkMxpfVvPNIxh0aSiX5BcVGhncq1etjK3pdVae2o5krU4Gqc6OCgHoIHWy08JyhljlEGHX7fMdY_b_C31tJU1FYG_xfEbCZUJYexZI_Y6kOu9iq5HHkgPmr2aNXx-AUlMQ9U.aKMJ1Q.NPXXzGXU4zyW1WeVnNAJgDuKYsQ
2025-08-18 19:09:11 - INFO - 
自动化流程成功完成！
2025-08-18 19:09:11 - INFO - 添加第58个
2025-08-18 19:09:25 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52004,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:09:25 - INFO - 127.0.0.1:52004
2025-08-18 19:09:25 - INFO - <EMAIL>
2025-08-18 19:09:25 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:10:07 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:10:11 - INFO - 找到 Turnstile...
2025-08-18 19:10:11 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:10:14 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:10:21 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:10:21 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:10:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:10:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:10:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:10:29 - INFO -    等待 3.94 秒...
2025-08-18 19:10:38 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:10:38 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:10:38 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:10:38 - INFO - 成功！当前在订阅页面。
2025-08-18 19:10:38 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:10:38 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:10:38 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:10:41 - INFO - 代码信息: {
  "codeVerifier": "uhYpybBh4Us1fQwXU5dVgVmvDcvlOIUudr2Hk_jEUtA",
  "code_challenge": "SGqKtqb5rgEQ8WfcYsLKQ2frszRGRs-jVMllO3f3Uco",
  "state": "fc6de6bd-dae6-4b8e-8ca8-cc019b851727"
}
2025-08-18 19:10:41 - INFO - ==================================================
2025-08-18 19:10:42 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_873ceafc34cdeffbbfef9e7edb996593&state=fc6de6bd-dae6-4b8e-8ca8-cc019b851727&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-18 19:10:44 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:10:44 - INFO - 添加session: <EMAIL>   df04d824c3cdc1463a5948de1a4630bae83f96c8d40a94383379ed0808407778   https://d8.api.augmentcode.com/  2025-08-25T11:10:24Z
2025-08-18 19:10:44 - INFO - email:<EMAIL> === cookie:.eJxNjUtuhDAQRO_Sa4hof9owq7kJ6tDtyBpskIGRRpPcPQhlkWWV6r16w7hqzVy07HDb66ENRM5pfo2Fs8INoIGv9NTyLydZx2PTOiY5C82c5m_q2XaMzlmUPhobBIfISOe8LGU6SSLbo_U-ILmOcEDjGrg0l-E05eOxRgzee_SW8L5lrrss00Prx1LmVBT-iOuYP6cgQbk1naHWBcKWNUirYnDi2JlBDPz8ApcyRTQ.aKMKMg.NHmONxWR-M1RVtDnJh7ukjYmbYs
2025-08-18 19:10:44 - INFO - 
自动化流程成功完成！
2025-08-18 19:10:44 - INFO - 添加第59个
2025-08-18 19:11:06 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52385,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:11:06 - INFO - 127.0.0.1:52385
2025-08-18 19:11:07 - INFO - <EMAIL>
2025-08-18 19:11:07 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:11:55 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:11:59 - INFO - 找到 Turnstile...
2025-08-18 19:12:01 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:12:04 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:12:10 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:12:10 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:12:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:12:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:12:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:12:21 - INFO - 成功！当前在订阅页面。
2025-08-18 19:12:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:12:21 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:12:21 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:12:24 - INFO - 代码信息: {
  "codeVerifier": "aCV7ki1sC80BABqMBVDL4Uc4yhKofhFOia5iB5bHYp8",
  "code_challenge": "8RXyYKCXtQ4kWsPo06hz0FI3WqQmCn8IqJve7dG_T2c",
  "state": "297ad105-c837-47e9-bdd6-82e5743bc2e9"
}
2025-08-18 19:12:24 - INFO - ==================================================
2025-08-18 19:12:25 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_33a22fe7300cd746c536a98303cb5ec5&state=297ad105-c837-47e9-bdd6-82e5743bc2e9&tenant_url=https%3A%2F%2Fd14.api.augmentcode.com%2F
2025-08-18 19:12:29 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:12:29 - INFO - 添加session: <EMAIL>   d41721c20a0b909784120b64808ad0a77b2cd6b7b95d917450b9987cc76d1245   https://d14.api.augmentcode.com/  2025-08-25T11:12:13Z
2025-08-18 19:12:29 - INFO - email:<EMAIL> === cookie:.eJxNjc1ugzAQhN9lz1Dh9c-anPomaIvXlRVsEgOVorTvHgvl0NvMaOabJ0w3qZmLlB0uez2kg8g5LY-pcBa4AHTwnX6k_PMp3KZjkzql0ALJnJZf51kP7AejVfARNQXUgSO2elnL3JbWK0uIjpQjIo16cB2cmJPQSOW43qMia62yxuHnlrnuYZ2vUj_WsqQi8F6cx5GDzI3Ui9PUG9OUFzf2RGP4Gg2aUSP8vQCo9UR_.aKMKmQ.3SB51SlY_oYp1bN-D-qwQiqPTw8
2025-08-18 19:12:29 - INFO - 
自动化流程成功完成！
2025-08-18 19:12:29 - INFO - 添加第60个
2025-08-18 19:12:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52662,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:12:44 - INFO - 127.0.0.1:52662
2025-08-18 19:12:45 - INFO - <EMAIL>
2025-08-18 19:12:45 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:13:25 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:13:29 - INFO - 找到 Turnstile...
2025-08-18 19:13:31 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:13:34 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:13:40 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:13:41 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:13:54 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:13:54 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:13:54 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:13:54 - INFO - 成功！当前在订阅页面。
2025-08-18 19:13:54 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:13:54 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:13:54 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:13:57 - INFO - 代码信息: {
  "codeVerifier": "Yc_KjfRxIGvqwrcYGIl_bEERXEWVHgKP7_I-c5jFjBU",
  "code_challenge": "PcVJtSvWMHKo__8JCXuRzhDLDD8GhuYhguZVZd2WGmE",
  "state": "80af866b-fec6-496d-859f-3b2ee57417ee"
}
2025-08-18 19:13:57 - INFO - ==================================================
2025-08-18 19:13:58 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_e5dfcf417cd80c91aa9a80a593ae20ed&state=80af866b-fec6-496d-859f-3b2ee57417ee&tenant_url=https%3A%2F%2Fd3.api.augmentcode.com%2F
2025-08-18 19:14:00 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:14:00 - INFO - 添加session: <EMAIL>   187e0e445c98afa530cf7509b5552987a563d3572b8702a66acf8ed861184879   https://d3.api.augmentcode.com/  2025-08-25T11:13:44Z
2025-08-18 19:14:00 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mCYlj5w5Z-QsTOYRlpMBRNf_y4SFy7vzT3nPqG_SEmUJc-wn8siFQyU4njvMyWBPUAFp3iT_JcjX_rlKqWPvBaSKI4v60k3xNRqZD8o7VgdORCv8zzlsJJOo7JoGte1aBGtbrCCTbMZvqblcY_ojDFojG0O10Rl5imcpeymPMYs8CO2Y-OVo04da7GW6razqvatp3pw7DXqgK4L8P4A-9dFJw.aKMK9w.mAyaP6IQJsFxiRMH76uZMPpU_vQ
2025-08-18 19:14:00 - INFO - 
自动化流程成功完成！
2025-08-18 19:14:00 - INFO - 添加第61个
2025-08-18 19:14:16 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52942,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:14:16 - INFO - 127.0.0.1:52942
2025-08-18 19:14:17 - INFO - <EMAIL>
2025-08-18 19:14:17 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:15:50 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:15:53 - INFO - 找到 Turnstile...
2025-08-18 19:15:53 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:15:57 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:17:12 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:17:12 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:17:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:17:22 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:17:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:17:22 - INFO - 成功！当前在订阅页面。
2025-08-18 19:17:22 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:17:22 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:17:22 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:17:25 - INFO - 代码信息: {
  "codeVerifier": "vGVrpDCMSTMWBAe2Hsv0-FerNDjiK-Qx1yxAWp3Dwvo",
  "code_challenge": "rZuf1lJPPAo7ok6btGx-9aJp3RJtxdyjpJylllqxZUA",
  "state": "40af0faf-41c8-4493-bd24-cf3071693759"
}
2025-08-18 19:17:25 - INFO - ==================================================
2025-08-18 19:17:26 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_aa7e0fe872db4f8eb4191e66243fd0b2&state=40af0faf-41c8-4493-bd24-cf3071693759&tenant_url=https%3A%2F%2Fd2.api.augmentcode.com%2F
2025-08-18 19:17:28 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:17:28 - INFO - 添加session: <EMAIL>   5f88fe1f0985af65b59da8e03462fed9fa22b8dfbc5c78b3e0737428b46b81a7   https://d2.api.augmentcode.com/  2025-08-25T11:17:15Z
2025-08-18 19:17:28 - INFO - email:<EMAIL> === cookie:.eJztU9tu4jAQ_ZdI5aWlxLliJLTbQgmgJS0pScAvkWObYuJcNpcW6Pbf16RdqdL2cR9XsuV45pwzI8_JqxIVrExxxrJaGdRlw66UqKpxzSLc1Ds1EvkTzyKSBifiwIZowZZOxQtagX2seWKeuhUOg4ZOYIHGYE_XrhprQBAOAH5ERxR6heTuZR6QNIF46qlkuhAL9U4sko3caEemN81mHWTxWcdxn5HjNxsN1vPV3JFxFY_8o5e6u9g58E3olvPMVTdrTzysKfJGIKfjGxUHO-AnxYZOZ2ApbuePCTqsguRI02JP7iZS3x1jcXf0U3CP7vxU6icogHvsTKrYCRqk-RBN6HgRTu5jf3Ivz3oD4ClMAKCJu0MjEHoplDg6CoLbLfNREUwDfzXZYXpE7VusNTffhIcKhWYmtWu09vJYW0L_NAMuR-Ah3JlI86o49PZUJ6oyeFUorvH5zPKMMGWgVP6ybMTz089ZhZ2Ddbs2nEy5UkpGeclIHTUll6hdXRfVoNc7T-gaN0-pHB7JKbsmedrLz1GtR7AQMSaJZDel-ERqB_oX60zKS35i30pWFXlWsag-Fmx4BnSI4BIbcTp8ET9WQeiMt7M6hNMTn-Uvjx5nS9c7isXDCnc-dzpsS17oNxfaRK6vupXh937lx5-OOxXJZWW5M04vWYq5uCzKfMsF67TOHP534793Y0c-cVrUw9YendaPwy_d-HalsEOhDIBtmiaAmgmvVcO2bEuXmS1OuThGGU7Pbpbee-LPLPt057SImoqV0kwy0A73l9XHuhpb2NAB7W813aZ6H0OGJfzjv7Bs1bTsvmnYumXaNuwbprT1WaZVkEoN4Zy9t2Rapv69SnFZ05wkrLzOM8Ezpnww2sIG7BMIrH5XMwHrGoxoXQx10tX6hm7FhswYW-XtN91TuQA.aKMLxg.JTFllTeaEWFLSLfZixSKWdO8OuA
2025-08-18 19:17:28 - INFO - 
自动化流程成功完成！
2025-08-18 19:17:28 - INFO - 添加第62个
2025-08-18 19:17:43 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53283,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:17:43 - INFO - 127.0.0.1:53283
2025-08-18 19:17:43 - INFO - <EMAIL>
2025-08-18 19:17:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:18:25 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:18:25 - INFO - 准备重启流程...
2025-08-18 19:18:28 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53337,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:18:28 - INFO - 127.0.0.1:53337
2025-08-18 19:18:28 - INFO - <EMAIL>
2025-08-18 19:18:28 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:19:26 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:19:30 - INFO - 找到 Turnstile...
2025-08-18 19:19:30 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:19:33 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:20:00 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:20:00 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:20:01 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:20:01 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:20:01 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:20:10 - INFO -    等待 4.72 秒...
2025-08-18 19:20:19 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:20:19 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:20:19 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:20:19 - INFO - 成功！当前在订阅页面。
2025-08-18 19:20:19 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:20:19 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:20:19 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:20:22 - INFO - 代码信息: {
  "codeVerifier": "3HyEhw0RBmm8ycBcYuTQNdrkauq3Ha2_Ef664rLGV4Q",
  "code_challenge": "YZxlTK5-U9_UwBx85G6Br8Nmpyi65LQxYgATd02spkw",
  "state": "6bef2e3f-3ca6-4d9e-a08c-96914f19f188"
}
2025-08-18 19:20:22 - INFO - ==================================================
2025-08-18 19:20:23 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_4af80c1c350f269b54c0ea46b6f0e3ec&state=6bef2e3f-3ca6-4d9e-a08c-96914f19f188&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-18 19:20:25 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:20:25 - INFO - 添加session: <EMAIL>   1bb88b5a2cc04146ace9f39539a913d3a73efda70e66376596193b5303519fca   https://d5.api.augmentcode.com/  2025-08-25T11:20:04Z
2025-08-18 19:20:25 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgjAQRP9lz2Ao7Xa3nPwTUuliGmkhBU2M-u8S9eBxJvPePKBfpCSfJW_QbeUqFYw-xeneZ58EOoAKzvEm-S_HsPTXVUofw15I8nF6Wva6GYw2WgUeW03BnKxlu8_znIedROuc0aSZtGNquWVVwUfzMeymUWROihBRIaM7rsmXLczDRcphzlPMAj_ie-yaIExYWx5sbdDYmluFtRfFZBozkiN4vQF1QUPS.aKMMdw.qAWn4MdAUbyAOuM6-0e6NLdbAEA
2025-08-18 19:20:25 - INFO - 
自动化流程成功完成！
2025-08-18 19:20:25 - INFO - 添加第63个
2025-08-18 19:20:41 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53640,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:20:41 - INFO - 127.0.0.1:53640
2025-08-18 19:20:41 - INFO - <EMAIL>
2025-08-18 19:20:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:21:28 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:21:32 - INFO - 找到 Turnstile...
2025-08-18 19:21:32 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:21:36 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:22:14 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:22:20 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:22:29 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:22:29 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:22:29 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:22:29 - INFO - 成功！当前在订阅页面。
2025-08-18 19:22:29 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:22:29 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:22:29 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:22:32 - INFO - 代码信息: {
  "codeVerifier": "mXJDlLsj8_p-DrV_Avg0wGa9jhLZkB_kx4ti_dlFavk",
  "code_challenge": "MvSQErUBRY0MSjbMLwoZm33-1a0scftLUO3U9J6OydQ",
  "state": "6e3e94f5-9340-4bba-9a2b-8370d971eeb8"
}
2025-08-18 19:22:32 - INFO - ==================================================
2025-08-18 19:22:33 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_796682d1ce9504ef8a0937f1aa43bfa2&state=6e3e94f5-9340-4bba-9a2b-8370d971eeb8&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-18 19:22:35 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:22:35 - INFO - 添加session: <EMAIL>   776b70d56b497b0a3dbbf047215c16008d33c8ec9f8a9f6912423ee923afca00   https://d6.api.augmentcode.com/  2025-08-25T11:22:34.400801838Z
2025-08-18 19:22:35 - INFO - email:<EMAIL> === cookie:.eJxNjUuOgzAQRO_Sa4ho7PYnq9wENXYzsoINckiUKDN3D0JZzLJK9V69YVilZi5SNjhv9S4NTJzT_BoKZ4EzQAM_6SHlX05xHe43qUOKeyGZ0_xrHKsujFErjG7qlY1kjGW1z8tSwk4iWaN7VJq8p44catPAoTkMu-l1fT5XtESEplP2cstct7iEq9TTUuZUBL7EcTzi5NmhtCMZbrUjbv3EfWt1QC9OOdcH-PsAv7xFIg.aKMM-Q.Z3oDcX-uqFagSC3m5ZOynRH0QrQ
2025-08-18 19:22:35 - INFO - 
自动化流程成功完成！
2025-08-18 19:22:35 - INFO - 添加第64个
2025-08-18 19:22:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53944,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:22:51 - INFO - 127.0.0.1:53944
2025-08-18 19:22:51 - INFO - <EMAIL>
2025-08-18 19:22:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:23:33 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:23:33 - INFO - 准备重启流程...
2025-08-18 19:23:36 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53996,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:23:36 - INFO - 127.0.0.1:53996
2025-08-18 19:23:36 - INFO - <EMAIL>
2025-08-18 19:23:36 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:24:14 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:24:18 - INFO - 找到 Turnstile...
2025-08-18 19:24:20 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:24:23 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:24:54 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:24:54 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:25:02 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:25:02 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:25:02 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:25:02 - INFO - 成功！当前在订阅页面。
2025-08-18 19:25:02 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:25:02 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:25:02 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:25:05 - INFO - 代码信息: {
  "codeVerifier": "Neh0lE2IW7I8fHLaCm-mjcHF27TvX6gr_5XzjqTjdfY",
  "code_challenge": "QIMYFPh2o3sGgD_94hF4Aa2Mo-qxt-okyCTbOfc1UaQ",
  "state": "d5bcd2c7-d5a8-4377-9da4-e5a59b0aea2c"
}
2025-08-18 19:25:05 - INFO - ==================================================
2025-08-18 19:25:06 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_cdec3d8fec980ecea4561974d6e21990&state=d5bcd2c7-d5a8-4377-9da4-e5a59b0aea2c&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-18 19:25:09 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:25:09 - INFO - 添加session: <EMAIL>   66b42572f6d27c3baa5b187b02dee1291dfa53658e4ec57e0ce22b053c4c8b34   https://d12.api.augmentcode.com/  2025-08-25T11:25:08.270831035Z
2025-08-18 19:25:09 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgyAQhN9lz9qAwAI99U0MLmtDKtigbdK_d68xPfQ4k_m-eUF_5ZpD4bLCca03bmAMOU2PvoTMcARo4JzuXP5yitf-tnDtU9wKziFNb3RBiYhKKxnd2CkbUY-aaZuXudBGaqU6YaTrvEdl0SK6BnbNbthMz-VMJK0xRqJEe1pyqGuc6cL1MJcpFYYfsR97xSJaT60Vo2i1ENQOejAtBeGJ0Tk_ePh8AavmRQI.aKMNkg.82X2wNMuote8mXb1HOY91ACjetI
2025-08-18 19:25:09 - INFO - 
自动化流程成功完成！
2025-08-18 19:25:09 - INFO - 添加第65个
2025-08-18 19:25:29 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54295,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:25:29 - INFO - 127.0.0.1:54295
2025-08-18 19:25:30 - INFO - <EMAIL>
2025-08-18 19:25:30 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:26:11 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:26:11 - INFO - 准备重启流程...
2025-08-18 19:26:14 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54363,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:26:14 - INFO - 127.0.0.1:54363
2025-08-18 19:26:15 - INFO - <EMAIL>
2025-08-18 19:26:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:26:55 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:26:59 - INFO - 找到 Turnstile...
2025-08-18 19:26:59 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:27:02 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:27:02 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:27:02 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:27:02 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:27:15 - INFO -    等待 3.69 秒...
2025-08-18 19:27:23 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:27:23 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:27:23 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:27:58 - INFO - 成功！当前在订阅页面。
2025-08-18 19:27:58 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:27:58 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:28:09 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 19:28:12 - INFO - 代码信息: {
  "codeVerifier": "a-Rz6aQEhOaxPaOC7Ylq2PfV9kXgl-jKumyLhwQ2bLE",
  "code_challenge": "KE8mgZPMN8Op1uqaa8LcRcvJi0EO_mpv4CA-IybnIKY",
  "state": "44994fba-7636-4843-bdbd-c2b3c71722a1"
}
2025-08-18 19:28:12 - INFO - ==================================================
2025-08-18 19:28:13 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_9922e4309d5ab19f8c48db0786ae3264&state=44994fba-7636-4843-bdbd-c2b3c71722a1&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-18 19:28:15 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:28:15 - INFO - 添加session: <EMAIL>   3bff782df45e43d6095d86ea10283c53acd24e40ffeda82b74b313b964dc3ba0   https://d13.api.augmentcode.com/  2025-08-25T11:27:17Z
2025-08-18 19:28:15 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2Batr-efJOm0MU00oIFTIz67jbEg7eZycw3L3ALleQz5Q3OW9mpgdGnOD1d9ongDNDANT4o__kYFrevVFwMNaDk4_RWxiMjJgTyYMYOddAohAi1nuc81KVBhZxzY0xnpUJmUTRwYA5CJY3TvY9cSym5wk5e1uTLFubhRuU05ylmgt_iOJao6oE3bS-6oRUaqbVWsap8zywbVGAaPl9k6UPx.aKMOTQ.24HPkAPrlwNzOFJUh0NBpQoqgOc
2025-08-18 19:28:15 - INFO - 
自动化流程成功完成！
2025-08-18 19:28:15 - INFO - 添加第66个
2025-08-18 19:28:37 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54660,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:28:37 - INFO - 127.0.0.1:54660
2025-08-18 19:28:37 - INFO - <EMAIL>
2025-08-18 19:28:37 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:29:55 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:29:59 - INFO - 找到 Turnstile...
2025-08-18 19:30:00 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:30:04 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:30:12 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:30:12 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:30:24 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:30:24 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:30:24 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:30:24 - INFO - 成功！当前在订阅页面。
2025-08-18 19:30:24 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:30:24 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:30:24 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:30:27 - INFO - 代码信息: {
  "codeVerifier": "OeLZjwHrD9sS6ak3gO-ANLl0LnklNjjW5AZkhE4bb8M",
  "code_challenge": "Z8QZTr7dDdBDz2m0eTFeVkjR1VgqxoZOKwjG0mKn4Fw",
  "state": "8a66c42f-c725-4909-a6d5-d16d9b9d239e"
}
2025-08-18 19:30:27 - INFO - ==================================================
2025-08-18 19:30:28 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_8b62e968bb209db7bece90761f3a3cfb&state=8a66c42f-c725-4909-a6d5-d16d9b9d239e&tenant_url=https%3A%2F%2Fd19.api.augmentcode.com%2F
2025-08-18 19:30:30 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:30:30 - INFO - 添加session: <EMAIL>   7bf9ae8c516836ee92035786731d7f4b431329824141f7645fdddbf336c64946   https://d19.api.augmentcode.com/  2025-08-25T11:30:21Z
2025-08-18 19:30:30 - INFO - email:<EMAIL> === cookie:.eJxNjUuOwjAQRO_S6wTF3e0PrLhJ5E8HGWIHmYA0GubuE0UsWFap3qtfGO_Siq9SVzit7SkdTL7k-WesvgicADq45JfUr5zTfXw-pI05bYUUn-e3cZ4GCY5JJTch2eTIoeZtXpcaN5IMDmwcG1IWiRiRO9g1u2Ez5Wt53ZTVWiujFZ4fxbc1LfEm7bDUOVeBD7EfRx60nSz3ylHoOUrsj0ce-hgoBVQYmCf4-wdzY0SA.aKMO1Q.k2iLjc-KBBXjDdzEVe9yFeJKisM
2025-08-18 19:30:30 - INFO - 
自动化流程成功完成！
2025-08-18 19:30:30 - INFO - 添加第67个
2025-08-18 19:30:52 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:54955,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:30:52 - INFO - 127.0.0.1:54955
2025-08-18 19:30:52 - INFO - <EMAIL>
2025-08-18 19:30:52 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:32:27 - INFO - 获取验证码失败，重试次数: 1
2025-08-18 19:32:37 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:32:41 - INFO - 找到 Turnstile...
2025-08-18 19:32:43 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:32:47 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:32:47 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:32:54 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:32:54 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:32:59 - INFO -    等待 4.65 秒...
2025-08-18 19:33:07 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:33:07 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:33:07 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:33:07 - INFO - 成功！当前在订阅页面。
2025-08-18 19:33:07 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:33:07 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:33:17 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 19:33:20 - INFO - 代码信息: {
  "codeVerifier": "DM55UIRBQ1pkxrLcbcSWx8P007vLA_KJAg2WC8nCW58",
  "code_challenge": "FHOPdRGzWUDnsaoJJzrbdlTv8KkPhYHD9horpzFSc8c",
  "state": "39c49ca1-985f-479f-851c-b6b3ae1ae6c4"
}
2025-08-18 19:33:20 - INFO - ==================================================
2025-08-18 19:33:21 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_97c613f5b758310107f2aea07acbbb15&state=39c49ca1-985f-479f-851c-b6b3ae1ae6c4&tenant_url=https%3A%2F%2Fd16.api.augmentcode.com%2F
2025-08-18 19:33:23 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:33:23 - INFO - 添加session: <EMAIL>   47d5fbfab66196b0784bbb33ae0523c1639a7987b53b91337bedb0ee43a7d98c   https://d16.api.augmentcode.com/  2025-08-25T11:32:57Z
2025-08-18 19:33:23 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mDoY8rgyj8hpZ2aRlpIeSRG_XcJceHy3txz7gv6mUuymfMK17VsXEGwKY7PPtvEcAWo4B53zn85-rnfFi599EfBycbxbciqJqDVSngKUrW-U9Jbd8zzlN1BtsoYhSRIS9NoTZJUBafmNBymNc47ixYRhTGabkuyZfWTe3C5THmMmeFHnMfIqNEHrAfhQq25C_XAjmpk2QwkBqmcgM8X1sxFvQ.aKMPgQ.SNjLHuBE0vD217ptYey2-aaOTG8
2025-08-18 19:33:23 - INFO - 
自动化流程成功完成！
2025-08-18 19:33:23 - INFO - 添加第68个
2025-08-18 19:33:43 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55304,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:33:43 - INFO - 127.0.0.1:55304
2025-08-18 19:33:43 - INFO - <EMAIL>
2025-08-18 19:33:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:34:14 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:34:18 - INFO - 找到 Turnstile...
2025-08-18 19:34:18 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:34:21 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:34:21 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:34:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:34:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:34:31 - INFO -    等待 4.70 秒...
2025-08-18 19:34:41 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:34:41 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:34:41 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:34:41 - INFO - 成功！当前在订阅页面。
2025-08-18 19:34:41 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:34:41 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:34:41 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:34:44 - INFO - 代码信息: {
  "codeVerifier": "yrH23ZZ0lSUWYi9OiZVen--FyplVW9yJJc3-ypIf7co",
  "code_challenge": "Oe3-6DBAG9AMj9f5cP1u8l7TT-FsXWVwxc6SIGYWdz0",
  "state": "ea5d5cbd-c42a-4cc5-902e-9c4d23167fd8"
}
2025-08-18 19:34:44 - INFO - ==================================================
2025-08-18 19:34:45 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_47eb3083d119ef5268ee31900cd299e5&state=ea5d5cbd-c42a-4cc5-902e-9c4d23167fd8&tenant_url=https%3A%2F%2Fd4.api.augmentcode.com%2F
2025-08-18 19:34:48 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:34:48 - INFO - 添加session: <EMAIL>   9b7611a1c654e4dbbf8463be200eeca64eb6741855c2805466ec91b7060c7634   https://d4.api.augmentcode.com/  2025-08-25T11:34:30Z
2025-08-18 19:34:48 - INFO - email:<EMAIL> === cookie:.eJxNjUkOwjAQBP8y5wTZ8c6Jn0RexsginiAnIBDwd6KIA8dudVW_YLxiq56QVjiu7YYdZF_L9BzJV4QjQAfnckf6yyVdx9uCbSxpK7D6Mr219YLlEKTgyeZBmOSidIJtc5opbiTXAzNaS2MU004oa00Hu2Y3bKaSKT64UUpxbbk7LdW3Nc3xgu0w01QI4Ufsx15JyQYV-4jW9TIo04cQRS94HlBImxlm-HwBsaJFKQ.aKMP1Q.C5Bx9-ylFawynC2bUHXV8MG8af8
2025-08-18 19:34:48 - INFO - 
自动化流程成功完成！
2025-08-18 19:34:48 - INFO - 添加第69个
2025-08-18 19:35:10 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55572,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:35:10 - INFO - 127.0.0.1:55572
2025-08-18 19:35:11 - INFO - <EMAIL>
2025-08-18 19:35:11 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:35:52 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:35:52 - INFO - 准备重启流程...
2025-08-18 19:35:55 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55774,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:35:55 - INFO - 127.0.0.1:55774
2025-08-18 19:35:55 - INFO - <EMAIL>
2025-08-18 19:35:55 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:36:20 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:36:24 - INFO - 找到 Turnstile...
2025-08-18 19:36:25 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:36:29 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:36:36 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:36:36 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:36:51 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:36:51 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:36:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:36:51 - INFO - 成功！当前在订阅页面。
2025-08-18 19:36:51 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:36:51 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:36:51 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:36:54 - INFO - 代码信息: {
  "codeVerifier": "1zgep_9LyOHSNhgWLSGGcvzMMD6NQs5K-Bg39Bm3q9w",
  "code_challenge": "wWdQQR3PKOSNwMsi2hLDpkTVTHPYfGi6EDWA_24a4nE",
  "state": "948ea967-1040-4651-a305-6529b6cc9481"
}
2025-08-18 19:36:54 - INFO - ==================================================
2025-08-18 19:36:55 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_03932888692c579fb5b7746b8610e712&state=948ea967-1040-4651-a305-6529b6cc9481&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-18 19:36:57 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:36:57 - INFO - 添加session: <EMAIL>   4a9dc846ff94ffb9f09732e89e14dafbadb092ca6abdf7e5c4e5ece65268df98   https://d8.api.augmentcode.com/  2025-08-25T11:36:38Z
2025-08-18 19:36:57 - INFO - email:<EMAIL> === cookie:.eJxNjUFuwzAMBP_Csx2YliVROeUnBiNSrRBLDhSnQNP27zWMHnrcxc7sF8x3bYWr1g3OW3tqB4lLXj7nykXhDNDBW_7Q-i9nuc_Ph7Y5y15o4bx8O2KDgwmTQaE0Gi9MJg60z-ta406i926wox09BhfQeWs6ODSHYTe9btf2jt5aiy4M_vIo3DZZ403baa1Lrgp_xHE8RmIiST1fkftJEvbMwfU-KonGZCYh-PkFjnhFwQ.aKMQVw.Yq-RmGLR3dh4JPZJVDL8pxeKpdQ
2025-08-18 19:36:57 - INFO - 
自动化流程成功完成！
2025-08-18 19:36:57 - INFO - 添加第70个
2025-08-18 19:37:16 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:55984,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:37:16 - INFO - 127.0.0.1:55984
2025-08-18 19:37:17 - INFO - <EMAIL>
2025-08-18 19:37:17 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:37:41 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:37:45 - INFO - 找到 Turnstile...
2025-08-18 19:37:45 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:37:48 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:38:15 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:38:15 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:38:31 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:38:31 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:38:31 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:38:31 - INFO - 成功！当前在订阅页面。
2025-08-18 19:38:31 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:38:31 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:38:31 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:38:34 - INFO - 代码信息: {
  "codeVerifier": "no7OXnfAADhXanjABkyg_VZj7dgJI-S28-ac8NbSkDg",
  "code_challenge": "FX9607TKYfC5P9l7cePrqve-sB8b_rUFq1Yo8hvJzyY",
  "state": "400b712c-3d05-4d68-83a7-2b375a255b6a"
}
2025-08-18 19:38:34 - INFO - ==================================================
2025-08-18 19:38:35 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_4b9b5366d66a916b04d94789f7fe7a75&state=400b712c-3d05-4d68-83a7-2b375a255b6a&tenant_url=https%3A%2F%2Fd20.api.augmentcode.com%2F
2025-08-18 19:38:37 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:38:37 - INFO - 添加session: <EMAIL>   1cd5481c3b09b1b2adde0140abec65e8857bd14265125eec459eb9ad0f603d48   https://d20.api.augmentcode.com/  2025-08-25T11:38:17Z
2025-08-18 19:38:37 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2BaStvFk29CFnYxjbRgQRP_3l1CPHicyXzfvKCdJUdKklY4rvkmBQwUw_hoE0WBI0AB53CX9JcDz-1tkdwG3gqJFMa3QzJaIdVGMw6V8UwDi6FtnqbUb6StlW2qxiqvnNZoKoMF7JrdsJmuzzCP2ltrtVfGnJZIeeWpv0g-TGkMSeBH7Md9jd67jkuH1pU1q6bsBLFstKdKi2Pqevh8AdJbRWE.aKMQuw.CB1DC2mRvHhCWpjUrNIWrgLrIXM
2025-08-18 19:38:37 - INFO - 
自动化流程成功完成！
2025-08-18 19:38:37 - INFO - 添加第71个
2025-08-18 19:38:59 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56255,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:38:59 - INFO - 127.0.0.1:56255
2025-08-18 19:39:00 - INFO - <EMAIL>
2025-08-18 19:39:00 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:39:54 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:39:58 - INFO - 找到 Turnstile...
2025-08-18 19:39:58 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:40:01 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:40:01 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:40:32 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:40:41 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:40:41 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:40:41 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:40:41 - INFO - 成功！当前在订阅页面。
2025-08-18 19:40:41 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:40:41 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:40:41 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:40:44 - INFO - 代码信息: {
  "codeVerifier": "oIRtSVgnYK8MbDp6f_Aatd-cx5Oel52Yqqg6cdE7dtg",
  "code_challenge": "LvRxLjHQGghVEU3omjIgNH-kUpEgdG9k3mW_MQM5jDg",
  "state": "a773d6ca-5361-4c34-8066-a3dfc4380146"
}
2025-08-18 19:40:44 - INFO - ==================================================
2025-08-18 19:40:45 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_16c1e4a88af3b123a3a65fae6cea72e6&state=a773d6ca-5361-4c34-8066-a3dfc4380146&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-18 19:40:47 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:40:47 - INFO - 添加session: <EMAIL>   809a5fa08072438634245745cd72eaf2681fb1e2c173ef847e8b4d5b649e48f3   https://d12.api.augmentcode.com/  2025-08-25T11:40:46.977734518Z
2025-08-18 19:40:47 - INFO - email:<EMAIL> === cookie:.eJxNjc1ugzAQhN9lzxCx_sHrnPomyHjXyCo2kUNaVUnevQj10OOM5vvmCdNNWglV6g7XvT2kgxRKXn-mGorAFaCDJX9J_Zcz36bHXdqU-SikhLy-RgoacUhGI1NS2vEcnZb5mNetxoNEPZB1NDjrPHlLgzEdnJrTcJiW7xYTOmstOtTjx72EtvMWP6VdtrrmKvBHnMfKa0eM3I_Bp94oY3qKI_aJMLGw8hIVvH8BugxFMQ.aKMRPQ.SU6-qA5su94jZrhaA57Xwqp9yF0
2025-08-18 19:40:47 - INFO - 
自动化流程成功完成！
2025-08-18 19:40:47 - INFO - 添加第72个
2025-08-18 19:41:07 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56562,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:41:07 - INFO - 127.0.0.1:56562
2025-08-18 19:41:07 - INFO - <EMAIL>
2025-08-18 19:41:07 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:42:09 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:42:13 - INFO - 找到 Turnstile...
2025-08-18 19:42:40 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:42:43 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:42:43 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:43:12 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:43:17 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:43:17 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:43:17 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:43:17 - INFO - 成功！当前在订阅页面。
2025-08-18 19:43:17 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:43:17 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:43:18 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:43:21 - INFO - 代码信息: {
  "codeVerifier": "ixMmkSrvzsZRPQT9oN_IkRjlZRLwjHZfTRrnfMdl3BU",
  "code_challenge": "lpAnFpdyQx0qUoXwKuu-x0v7GdJQpm8yDU8fIfbBE7Y",
  "state": "c1f9b3e7-0fae-48f2-9db8-87180b3b295a"
}
2025-08-18 19:43:21 - INFO - ==================================================
2025-08-18 19:43:21 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_21a3c9eafeb45db0ac1f32ae10f5e1a3&state=c1f9b3e7-0fae-48f2-9db8-87180b3b295a&tenant_url=https%3A%2F%2Fd1.api.augmentcode.com%2F
2025-08-18 19:43:24 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:43:24 - INFO - 添加session: <EMAIL>   77ae5218f800ede5f2efc8608419880e4eee53f8ec52d29effcc9df969ec1458   https://d1.api.augmentcode.com/  2025-08-25T11:43:16Z
2025-08-18 19:43:24 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAURP_lrsFQ-rpl5Z-Q0t6aKi1YkWjUf5cQFy5nMufMC_qZSrKZ8gLdUu5UQbApjs8-20TQAVRwiivlvxz93N9vVProt4KSjeNboeWMGSU48xharr0zTmm5zfOU3UYy1igluWlkI1pjGq2xgl2zGzbTY3XnK9NSSqZbxY-3ZMviJ3ehcpjyGDPBj9iPMaBBb1yNg_a1GAZWI1eyFmgGhSIElAifL39JRHs.aKMR2g.28SBcstAQCVdmB70uKV_nKOmge0
2025-08-18 19:43:24 - INFO - 
自动化流程成功完成！
2025-08-18 19:43:24 - INFO - 添加第73个
2025-08-18 19:43:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57000,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:43:40 - INFO - 127.0.0.1:57000
2025-08-18 19:43:41 - INFO - <EMAIL>
2025-08-18 19:43:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:44:45 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:44:49 - INFO - 找到 Turnstile...
2025-08-18 19:45:31 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:45:34 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:45:34 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:45:43 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:45:56 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:45:56 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:45:56 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:45:56 - INFO - 成功！当前在订阅页面。
2025-08-18 19:45:56 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:45:56 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:45:56 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:45:59 - INFO - 代码信息: {
  "codeVerifier": "WgH05kqnfRtWTIIsNKHnBIu5WC8OR_vTrKJcBxtdi98",
  "code_challenge": "LtMCIwIErJ9EmjC-ccAR1A2lJq0UPO2UtHJRQ9OBbZI",
  "state": "d7a36e33-074d-476d-88ea-0410af13bc98"
}
2025-08-18 19:45:59 - INFO - ==================================================
2025-08-18 19:45:59 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_2b4558f6e46066eb1e3098fb5d37bd03&state=d7a36e33-074d-476d-88ea-0410af13bc98&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 19:46:02 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:46:02 - INFO - 添加session: <EMAIL>   dc5a4670f19f7d7508c9cb2f67524ee5f42b0165fa92d1521c8b29a5a95408fe   https://d9.api.augmentcode.com/  2025-08-25T11:45:47Z
2025-08-18 19:46:02 - INFO - email:<EMAIL> === cookie:.eJxNjUuOwjAQRO_S6wS53f6FFTeJmriNPBM7yITRIGbuThSxYFmleq-eMF6lFa5SVziu7S4dJC55foyVi8ARoINL_pH6kXO8jvebtDHHrZDCef5zgQk1aUMYQ9LkYwxs0W3zutRpIy0OejAmeOUDYSBSuoNdsxs208yPr1_01lr0Bv3pVritcZm-pR2WOucq8Cb240k7NunMPUdxvXGD789IU69SIKUcJh4s_L8AgeREvQ.aKMSeA.4V0KGGSLBy5CkgiYd-4Aay4_UdU
2025-08-18 19:46:02 - INFO - 
自动化流程成功完成！
2025-08-18 19:46:02 - INFO - 添加第74个
2025-08-18 19:46:18 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57479,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:46:18 - INFO - 127.0.0.1:57479
2025-08-18 19:46:18 - INFO - <EMAIL>
2025-08-18 19:46:18 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:47:11 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:47:15 - INFO - 找到 Turnstile...
2025-08-18 19:47:37 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:47:41 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:48:07 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:48:07 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:48:30 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:48:30 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:48:30 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:48:30 - INFO - 成功！当前在订阅页面。
2025-08-18 19:48:30 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:48:30 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:48:30 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:48:33 - INFO - 代码信息: {
  "codeVerifier": "WrzqZfXlklry58q5koJlRNngk6Vg_jUNzqUnrAaLtLo",
  "code_challenge": "gKj97454RXcjPSG2MSJN4_UdM-QyZ1qsNjhxYEf7naA",
  "state": "feeec550-a4f3-462a-9f5e-f1cae7565ca3"
}
2025-08-18 19:48:33 - INFO - ==================================================
2025-08-18 19:48:34 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_1f9c792b6caa5b7f381c24315c7887ed&state=feeec550-a4f3-462a-9f5e-f1cae7565ca3&tenant_url=https%3A%2F%2Fd8.api.augmentcode.com%2F
2025-08-18 19:48:37 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:48:37 - INFO - 添加session: <EMAIL>   25c9f61e569acd9c5368fffc928e83ebb01feda66a874a8991d723c14c65bbda   https://d8.api.augmentcode.com/  2025-08-25T11:48:10Z
2025-08-18 19:48:37 - INFO - email:<EMAIL> === cookie:.eJxNjdEKgzAMRf8lzzrWNLbVp_2JVJtKma1SdTC2_fuK7GEPCeRyz8kL-pVztInTDt2eD67A2xjmZ59sZOgAKpjCg9PfHdzaHxvnPrgScLRhfitjpcCRSApnPErtWHnystTTksZCSoNaSFItoUZEMldTwak5DcXkt2mPQjdNU5am2xZt3t0y3jlfljSHxPAjzsfOahzYyHocvKip1a4ug7Wy2pMyvnEDw-cLuH5FnA.aKMTEg.OxKwoPU_Lo69TJs-iAOGo9M7lP8
2025-08-18 19:48:37 - INFO - 
自动化流程成功完成！
2025-08-18 19:48:37 - INFO - 添加第75个
2025-08-18 19:48:56 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58021,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:48:56 - INFO - 127.0.0.1:58021
2025-08-18 19:48:57 - INFO - <EMAIL>
2025-08-18 19:48:57 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:50:02 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:50:06 - INFO - 找到 Turnstile...
2025-08-18 19:50:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:50:11 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:50:11 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:50:18 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:50:18 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:50:18 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:50:18 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:50:25 - INFO -    等待 3.85 秒...
2025-08-18 19:50:33 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:50:33 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:50:33 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:50:33 - INFO - 成功！当前在订阅页面。
2025-08-18 19:50:33 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:50:33 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:50:33 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:50:36 - INFO - 代码信息: {
  "codeVerifier": "E8lQQNrsf3cq8oRiQ9m_32qz35mZZEUc0PwyDnT74Uw",
  "code_challenge": "Y2Qhn0gnc5vcOB-NeVPfk52fOdB1FNmONh84Ozp0SoQ",
  "state": "75754afa-e515-4cf5-a9de-615eb1db42b4"
}
2025-08-18 19:50:36 - INFO - ==================================================
2025-08-18 19:50:37 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_2da3973a51b7434c023e12ce34b9b727&state=75754afa-e515-4cf5-a9de-615eb1db42b4&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-18 19:50:40 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:50:40 - INFO - 添加session: <EMAIL>   30580b63f6219d23ba4dd9a1373b44811e4e59509f7ba1147c1724ffc729f201   https://d5.api.augmentcode.com/  2025-08-25T11:50:21Z
2025-08-18 19:50:40 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3ZkWBZwTv0TawNLimpwSpxKTZt_j2XlkOOM5r35g-kirXCVusJxbTfpIHHJ8-9UuQgcATo45x-pbznHy3S7Spty3AopnOd_6xkV2mRQRZ80uphcMpy2eV1q2EhENNoMAxkalB4tWuxg1-yGzfR9D-dP5YhIOYf4cS3c1riEL2mHpc65CryI_fh0QqEYxt7pKL0h1D3z4Po0Om88BZ_IwuMJxjRFJw.aKMTjQ.Oqn0qlrrO9yUiu0YC3diyN6UFVw
2025-08-18 19:50:40 - INFO - 
自动化流程成功完成！
2025-08-18 19:50:40 - INFO - 添加第76个
2025-08-18 19:50:54 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58237,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:50:54 - INFO - 127.0.0.1:58237
2025-08-18 19:50:54 - INFO - <EMAIL>
2025-08-18 19:50:54 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:51:51 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:51:55 - INFO - 找到 Turnstile...
2025-08-18 19:52:37 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:52:40 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:52:40 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:53:05 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 19:53:15 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:53:15 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:53:15 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:53:15 - INFO - 成功！当前在订阅页面。
2025-08-18 19:53:15 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:53:15 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:53:15 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:53:18 - INFO - 代码信息: {
  "codeVerifier": "y0JcNddM-_RVENSLLaItCAKIgmnFD6m50ka-WZFWBQg",
  "code_challenge": "4vJSE704YJzBelDAB4NhEac1ttcBfVa6CQYj9ggNI2Y",
  "state": "cb9a7345-b789-4fa3-bff0-a492b0ab1104"
}
2025-08-18 19:53:18 - INFO - ==================================================
2025-08-18 19:53:19 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_398d9193abd0ff24481b5c65a569d599&state=cb9a7345-b789-4fa3-bff0-a492b0ab1104&tenant_url=https%3A%2F%2Fd17.api.augmentcode.com%2F
2025-08-18 19:53:21 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:53:21 - INFO - 添加session: <EMAIL>   397eca48bc1ca20f561d18c4a1c9d2e8280e6defbe0f73513f56191c872f22ff   https://d17.api.augmentcode.com/  2025-08-25T11:53:20.870657464Z
2025-08-18 19:53:21 - INFO - email:<EMAIL> === cookie:.eJxNzUFuwyAQBdC7sLajAYaCvcpNrDEzNCgGR8SJlLS9e51s2tXof329-VLTRVqhKnVT49Zu0qlEJS-PqVIRNSrVqc98l_ovZ75Mt6u0KfNeSKG8fH8EstpyRKs5JGM9gwWJss_rWvc7GjcMwTrvYQCNgB6hU2_mLbyk54mS9s457YOD47VQ23iNZ2mHtS65vrS_xyzRIA7SmwCxx4SunzXY3sxkmWeXCI36-QW-SEVT.aKMULw.EuNDurLoxnoopYr1oPOnLG6f-Qc
2025-08-18 19:53:21 - INFO - 
自动化流程成功完成！
2025-08-18 19:53:21 - INFO - 添加第77个
2025-08-18 19:53:43 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58758,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:53:43 - INFO - 127.0.0.1:58758
2025-08-18 19:53:43 - INFO - <EMAIL>
2025-08-18 19:53:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:54:25 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:54:25 - INFO - 准备重启流程...
2025-08-18 19:54:27 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58886,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:54:27 - INFO - 127.0.0.1:58886
2025-08-18 19:54:28 - INFO - <EMAIL>
2025-08-18 19:54:28 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:55:06 - INFO - 获取 cap_value 验证码成功...
2025-08-18 19:55:09 - INFO - 找到 Turnstile...
2025-08-18 19:55:09 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 19:55:13 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 19:55:20 - INFO - 验证码已提交，等待跳转...
2025-08-18 19:55:20 - INFO - 
第 1/5 次尝试注册...
2025-08-18 19:55:20 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:56:14 - INFO -    等待 4.62 秒...
2025-08-18 19:56:22 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 19:56:22 - INFO - 
第 2/5 次尝试注册...
2025-08-18 19:56:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 19:56:22 - INFO - 成功！当前在订阅页面。
2025-08-18 19:56:22 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 19:56:22 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 19:56:32 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 19:56:35 - INFO - 代码信息: {
  "codeVerifier": "1SgBLqKdDv7ZrzAcGVtkKf2lqXIaE0q-ZDRriQNUVQg",
  "code_challenge": "q6fk-Y4j0g5Y7KD1yre2ymtDgoUIt93EatKY37NkbfU",
  "state": "2dcf9711-0712-40df-93cb-a8551dd34e53"
}
2025-08-18 19:56:35 - INFO - ==================================================
2025-08-18 19:56:36 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_ce7c9f24db8143d910111e37ef254900&state=2dcf9711-0712-40df-93cb-a8551dd34e53&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 19:56:38 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 19:56:38 - INFO - 添加session: <EMAIL>   460d3bccb9f316740beeb04aa4cefed68c0299bcf689a4ab437609a22f83bc4e   https://d9.api.augmentcode.com/  2025-08-25T11:56:16Z
2025-08-18 19:56:38 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2C6_ceTb0JadjFVWkhFE6O-u4R48DiT-b55Qb9wzaFwWeG41js3MIacpmdfQmY4AjRwTg8ufznR0t9vXPtEW8E5pOltfVCou1ErJD9K5QhtjGy2eZnLsJHOaC-FQxRd51EKKW0Du2Y3bKYlL3xBZ4xBL7A73XKoK83DlethLlMqDD9iPx6d8xSjbQfFttVMoY1K2pZJBCG1M54YPl-1WkV4.aKMU9A.PaH57nhXQTaHuy8JPIJbHBu8uUw
2025-08-18 19:56:38 - INFO - 
自动化流程成功完成！
2025-08-18 19:56:38 - INFO - 添加第78个
2025-08-18 19:57:01 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59198,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:57:01 - INFO - 127.0.0.1:59198
2025-08-18 19:57:02 - INFO - <EMAIL>
2025-08-18 19:57:02 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:57:44 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:57:44 - INFO - 准备重启流程...
2025-08-18 19:57:46 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59241,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:57:46 - INFO - 127.0.0.1:59241
2025-08-18 19:57:47 - INFO - <EMAIL>
2025-08-18 19:57:47 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 19:58:29 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 19:58:29 - INFO - 准备重启流程...
2025-08-18 19:58:32 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:59293,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 19:58:32 - INFO - 127.0.0.1:59293
2025-08-18 19:58:32 - INFO - <EMAIL>
2025-08-18 19:58:32 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:00:40 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:00:44 - INFO - 找到 Turnstile...
2025-08-18 20:00:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:00:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:01:15 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:01:15 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:01:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:01:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:01:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:01:21 - INFO - 成功！当前在订阅页面。
2025-08-18 20:01:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:01:21 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:01:24 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:01:27 - INFO - 代码信息: {
  "codeVerifier": "OWaLro452UL-qImOisIzw7oU-Bdsc1Q15BXMYXKmE6c",
  "code_challenge": "65eQDSxDQeh6xaGosCpEOh_S8gFYm4Yht6sP_8FPPR8",
  "state": "78dd7a06-6416-42b1-9ec3-a475b43b1bb1"
}
2025-08-18 20:01:27 - INFO - ==================================================
2025-08-18 20:01:27 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_fe32e47eebdf6b7b7b643ee9ac2296a2&state=78dd7a06-6416-42b1-9ec3-a475b43b1bb1&tenant_url=https%3A%2F%2Fd20.api.augmentcode.com%2F
2025-08-18 20:06:28 - INFO - 
主流程中发生严重错误: 'WebDriver' object has no attribute 'decode'
2025-08-18 20:06:28 - INFO - 准备重启流程...
2025-08-18 20:06:32 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60278,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:06:32 - INFO - 127.0.0.1:60278
2025-08-18 20:06:33 - INFO - <EMAIL>
2025-08-18 20:06:33 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:07:08 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:07:12 - INFO - 找到 Turnstile...
2025-08-18 20:07:34 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:07:38 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:07:44 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:07:44 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:07:44 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:07:44 - INFO - 成功！当前在订阅页面。
2025-08-18 20:07:44 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:07:44 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:07:51 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:07:54 - INFO - 代码信息: {
  "codeVerifier": "a6jAWQs0qSU110CGY9aqBPOPEyjFwweNkPtmUQtPtCc",
  "code_challenge": "5j6nen8UqPV4RU_nPBFf2X48MVvzAK2F7TM7iPKDPZ0",
  "state": "ae51c30c-bb75-4104-9514-ea358b8c993f"
}
2025-08-18 20:07:54 - INFO - ==================================================
2025-08-18 20:07:55 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_6850bf4c00237e8fe9f41474a9f8cbab&state=ae51c30c-bb75-4104-9514-ea358b8c993f&tenant_url=https%3A%2F%2Fd20.api.augmentcode.com%2F
2025-08-18 20:07:58 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:07:58 - INFO - 添加session: <EMAIL>   d2632218b8917c52efb09efd09ece353a5992ba0ea67e1d19cd1629beab9eebf   https://d20.api.augmentcode.com/  2025-08-25T12:01:19Z
2025-08-18 20:07:58 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgjAQRP9lz2DcLZXiyT9ptu1iqrSQAiZG_XcJ8eBxJvPevMBOUhJnyQucl7JKBT2nODxt5iRwBqjgGh-S_3IMk11nKTaGrZDEcXifDCvUIo3CYHpSbVDsybttnsfsN5J0g9gpQq0bfTpiRxXsll2wiW7FXVdstdZoCM1lTlyWMPq7lMOYh5gFfsT-ezRE4oXqnr2vm4BYu86pGrFvlWmdYwzw-QKfUEV9.aKMXmw.Wvu4AGOI4t0qLBTEZbjFASU9Znc
2025-08-18 20:07:58 - INFO - 
自动化流程成功完成！
2025-08-18 20:07:58 - INFO - 添加第79个
2025-08-18 20:08:16 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60532,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:08:16 - INFO - 127.0.0.1:60532
2025-08-18 20:08:16 - INFO - <EMAIL>
2025-08-18 20:08:16 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:09:26 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:09:30 - INFO - 找到 Turnstile...
2025-08-18 20:09:52 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:09:55 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:09:56 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:10:24 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:10:25 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:10:25 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:10:25 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:10:32 - INFO -    等待 4.99 秒...
2025-08-18 20:10:40 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 20:10:40 - INFO - 
第 2/5 次尝试注册...
2025-08-18 20:10:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:10:40 - INFO - 成功！当前在订阅页面。
2025-08-18 20:10:40 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:10:40 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:10:50 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 20:10:53 - INFO - 代码信息: {
  "codeVerifier": "CPeipu-p2R1z3j6MOoEYw72MigTlpgfEQUJMJW73AYQ",
  "code_challenge": "__tdbjTwfA_Fk_iDbFPaBlU0P_L_PINry9dGhJk0L7A",
  "state": "3d3291c3-6e16-4e89-836b-d47d8b98682b"
}
2025-08-18 20:10:53 - INFO - ==================================================
2025-08-18 20:10:54 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_211c7a65def86c48df05ecc592576472&state=3d3291c3-6e16-4e89-836b-d47d8b98682b&tenant_url=https%3A%2F%2Fd16.api.augmentcode.com%2F
2025-08-18 20:10:57 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:10:57 - INFO - 添加session: <EMAIL>   c1881817184a22de4d81db706c9ac3c29fa96ef80ffdc600b4135e5a359a8f4a   https://d16.api.augmentcode.com/  2025-08-25T12:10:27Z
2025-08-18 20:10:57 - INFO - email:<EMAIL> === cookie:.eJztk12PmzgUhv8LUnPT-cAQAkSK2k7TkIkGdoYQE7hBxjaDE2NYMJOPbv_7GnZXO1J7uZcrsIDj877nyOfhu5Y1tK2QoEJqc9n29EbLOokkzVAvSz3j9SsTGa7gFXtujw1YkDU_pRE45EbIN1XQoRj2ZOU26RIcyD7QcwNwzABA2_SSxmGjtAe1D3B1dNE61PHa577-jfvHRK20xOsvfbKHIh98vOAt9XZ9YrhyE208FdfR190lrIIy984siYN2IwI92Yf8OVpFO9ickqPcQJGcn-KgiYCvR8uHR8LDKRLNGUWwgavy5FfBi883bWiGfhLjSvkfU-gekLfqcg_2qbFzoUh_p-vykhwTiQ6rJ_pNtjBOW7wvyy18eMNV-BBVpws5lunOTNPICBsYb9h2m45nsTeCOonPXRpbQnnLdB_WufHi7q6PIGApeN4FSwhXu63uXoKVf9Lm3zWCJBqeohaYanNtH7FWXNdvHhT2pvxt-SJRqN1oLSWspVhmfctUVill083v74cJ3aH-tVLDwzWhd7iu7ushatxjxHmO8FGp-5a_E40D_Uk1iOqWXemnlnZNLTqayUtDF0PCBHOmcjNGFif-FMHYWxaPMnbXV_ZYn7Yhoy9BeOH-c4Qm7ztdjCU_mF8-GCt1_6pbFf6rX_XyT8eTDteqslqCkY-0Qox_bNq6YJxORjIX_9P439M4UUdcNXIx4jEZeVz8ksYfNxo9N9oc2JZlGcbUde7s2dRWl9opUMX4JROoGmhW7L2yNyrefTPSZH1HWwWTCozD_WPmIBPYRT41AXEKw7SJbZsWoir97_8CuMDSXd1ydN0CpjO1TVNhPdiMDsqpwpyQsSXgOC743FWolaTGR9re1YIzMbj9W9g0ZoWrO-YtnSH7dmrNzFvXKsxbBxeFDaZEzwnVfvwJ78u9Mg.aKMYTw.jRDK_ZLiROQHPKZoA0SIy-FyY8Q
2025-08-18 20:10:57 - INFO - 
自动化流程成功完成！
2025-08-18 20:10:57 - INFO - 添加第80个
2025-08-18 20:11:19 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:60850,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:11:19 - INFO - 127.0.0.1:60850
2025-08-18 20:11:20 - INFO - <EMAIL>
2025-08-18 20:11:20 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:12:53 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:12:57 - INFO - 找到 Turnstile...
2025-08-18 20:12:59 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:13:03 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:13:29 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:13:29 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:13:30 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:13:30 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:13:30 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:13:34 - INFO -    等待 4.87 秒...
2025-08-18 20:13:44 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 20:13:44 - INFO - 
第 2/5 次尝试注册...
2025-08-18 20:13:44 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:13:44 - INFO - 成功！当前在订阅页面。
2025-08-18 20:13:44 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:13:44 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:13:44 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:13:47 - INFO - 代码信息: {
  "codeVerifier": "E7Va-lgzpuNOsX7lJpIyxYXoYIcra1sQog1IUSQVDGM",
  "code_challenge": "tQPByqoFeDS-ETkHPVuImKohGrfXpaDm7uoZjqhgLME",
  "state": "54b73213-77f5-4855-bb08-9aaed5d83c0a"
}
2025-08-18 20:13:47 - INFO - ==================================================
2025-08-18 20:13:48 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_62e54f5f8b5fc2bbc73c11affe43ca1e&state=54b73213-77f5-4855-bb08-9aaed5d83c0a&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-18 20:13:51 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:13:51 - INFO - 添加session: <EMAIL>   5053828b6b60e4b2d8f02e5d9bd75c5ea19eda51984632a42bb1bbeb324edcb6   https://d13.api.augmentcode.com/  2025-08-25T12:13:32Z
2025-08-18 20:13:51 - INFO - email:<EMAIL> === cookie:.eJztU9mO2jAU_ZdIw0tnsR0gCRJqZ4EAnWSGQBb8EjmOGQzOMokzLNP-e03aSiO1j32sZDvx9T3nHvkev2txyaqM5CyX2kBWDbvU4loSyWLSyA2IRfHC85hmwYnaVkNRsE4nYo-XcJsgT8wytyZh0KRjq8QPcJtGLkgQFJRDSBb4iEOvVNitOoc021lk4gE6cYQDRsLZrdTEGzq5bVZRkCdnHtt9w7bfrJAlZ8uZreKA3PtHL3M3iX3gq9CtZrkLVpEnngMM_V0p053YB_4IeNFoj8NgvozuZhFMyeqUvjmBO3uKNtNkO_7q596rs5zqQeRkin-HA2tL7HGd2EGDkW8tci8It65wt9j3Jj5YjCzo68FoJQJvIVx9jg5lKG51xxZzfA8P6QN-YuPN3F_g9i4i5Bar8FDjsJcrbokjr0jQ3PJPU-hyDJ-j8cLZecAfz8RiPO9qg3ctJZKcv3mRU6YNtOXjjL6NHi3TWN81xqve6PWddqlVLOUVozJuKq6yNlKW9eDm5tyha9K8ZKp5tEjZNS2ym-IcRTeUCJEQulPophIfQG1D_0CdQUXFT-xzxeqyyGsWy2PJhueEDhVc5cY8He7F4zII7Yf1VIbW5MSnxX7hcTZ3vaNwnpek81HpsC15od9eoLEaf1Orwj_1qp_fijs1LVRlNXOefmIZ4eJTWRVrLlindebwvxv_vRs76oqzUg5be3RaPw7_6sbvlxo7lNoAGr1eD6G-ia5R3zBBz1Qna5JxcYxzkp3drLz3wt9Y_mHP0zJualYpM6lA29xvfZPo0KSkq8PUXCPdUGu3D4hK__UuUB-YOugaEKrRAwY0e8rWZ5qWQTHVbF_KVhK0gNH7UmekkmlBd6y6LnLBc6b9QrSFDUZTYpngak275lW3C9EV0UH_KtF1avXhWu9Con3_AfI1td0.aKMY_A.pgeaf_cd-9FGBfdmkMPAKYeI_PI
2025-08-18 20:13:51 - INFO - 
自动化流程成功完成！
2025-08-18 20:13:51 - INFO - 添加第81个
2025-08-18 20:14:09 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61149,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:14:09 - INFO - 127.0.0.1:61149
2025-08-18 20:14:10 - INFO - <EMAIL>
2025-08-18 20:14:10 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:14:33 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:14:37 - INFO - 找到 Turnstile...
2025-08-18 20:14:39 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:14:42 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:15:09 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:15:09 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:15:24 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:15:24 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:15:24 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:15:24 - INFO - 成功！当前在订阅页面。
2025-08-18 20:15:24 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:15:24 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:15:24 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:15:27 - INFO - 代码信息: {
  "codeVerifier": "qTDWG-VJkwNii7tkgS7zzfkZLhjSmuaCcAIgG-6f0NA",
  "code_challenge": "E3dLmjlsoj2VYiHJgfmf3SfC5u_NuVUKDfascBrhEoE",
  "state": "09e787f7-5401-4a41-af8a-ffc9650d0fdf"
}
2025-08-18 20:15:27 - INFO - ==================================================
2025-08-18 20:15:28 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_215dc492cf3f9970f5da3f08096f863f&state=09e787f7-5401-4a41-af8a-ffc9650d0fdf&tenant_url=https%3A%2F%2Fd3.api.augmentcode.com%2F
2025-08-18 20:15:30 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:15:30 - INFO - 添加session: <EMAIL>   9c5c12e7f2c13d06696561058d7d70d796391b6e988108025f6995af46e767e1   https://d3.api.augmentcode.com/  2025-08-25T12:15:13Z
2025-08-18 20:15:30 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwyAQRP9lz3ZlDAtsTvkTa2OWCtngCDtRqyb_XmT10OOM5r35gekuNXORcsDlqA_pIHJO6_dUOAtcADr4TE8p_3IK9-mxS51SaIVkTuvLetaKxmi0Cj6O2gXiSJHbvGxlbqQbvXXaGuPMMJJTeqAOTs1paKb1-FpuyiFiMxm87pnrEbZ5kfqxlTUVgT_iPLaofPDa9XSL1BuedU9oubdzJPaoEI2C9y_ErkTC.aKMZYA.PwV6t9s_DysUrYxgg8zSV7RCr_I
2025-08-18 20:15:30 - INFO - 
自动化流程成功完成！
2025-08-18 20:15:30 - INFO - 添加第82个
2025-08-18 20:15:53 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61321,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:15:53 - INFO - 127.0.0.1:61321
2025-08-18 20:15:53 - INFO - <EMAIL>
2025-08-18 20:15:53 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:16:38 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:16:43 - INFO - 找到 Turnstile...
2025-08-18 20:16:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:16:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:17:53 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:17:53 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:17:55 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:17:55 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:17:55 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:18:01 - INFO -    等待 4.99 秒...
2025-08-18 20:18:09 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 20:18:09 - INFO - 
第 2/5 次尝试注册...
2025-08-18 20:18:09 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:18:12 - INFO - 成功！当前在订阅页面。
2025-08-18 20:18:12 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:18:12 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:18:12 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:18:15 - INFO - 代码信息: {
  "codeVerifier": "KeCzR_AvuFgUGyfxuh-B4V_kJWpHdst3nXTyS-7sBqQ",
  "code_challenge": "82ZJsolJvyKCPB5eUO58-dzi6Rm4jmgmGMMrpbI93Qg",
  "state": "b632c978-b4b3-48b3-9d5c-c7fb451b4a97"
}
2025-08-18 20:18:15 - INFO - ==================================================
2025-08-18 20:18:16 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_cc903f9514a5665a6004e6951090fffd&state=b632c978-b4b3-48b3-9d5c-c7fb451b4a97&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-18 20:18:18 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:18:18 - INFO - 添加session: <EMAIL>   61be3fca581633e1e482d62095e73059d71c2da5a46117f4f1b161a3f11c549d   https://d12.api.augmentcode.com/  2025-08-25T12:18:11Z
2025-08-18 20:18:18 - INFO - email:<EMAIL> === cookie:.eJxNjdEKgzAMRf8lzzqstbb1aX9SUptKma1SdTC2_fs62cMIBHI59-QJZqUcMVHaYdjzQRV4jGF-mISRYACoYAp3Sn93cKs5NsomuBJQxDC_eoWcaRw7zpzyLZcOtUXGCp6WNJYm07yTZRrJhOZKaCUqODWnoZimyVJkUgjxZfV1i5h3t4w3ypclzSER_BrnY9F7blWr6qbAdYc41po3ZVmNXnomLXl4fwC47kVM.aKMaCA.mxmCn2DbrALJgbwI6CQLHJA7dmM
2025-08-18 20:18:18 - INFO - 
自动化流程成功完成！
2025-08-18 20:18:18 - INFO - 添加第83个
2025-08-18 20:18:33 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61651,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:18:33 - INFO - 127.0.0.1:61651
2025-08-18 20:18:33 - INFO - <EMAIL>
2025-08-18 20:18:33 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:19:21 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:19:25 - INFO - 找到 Turnstile...
2025-08-18 20:19:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:19:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:20:30 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:20:30 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:20:30 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:20:43 - INFO -    等待 3.93 秒...
2025-08-18 20:20:50 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 20:20:50 - INFO - 
第 2/5 次尝试注册...
2025-08-18 20:20:50 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:20:50 - INFO - 成功！当前在订阅页面。
2025-08-18 20:20:50 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:20:50 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:21:01 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 20:21:04 - INFO - 代码信息: {
  "codeVerifier": "IX6XDkXBDA_jSfPXknPGGt0feghGZrXrR_wrRbQER-4",
  "code_challenge": "5_XX0ixwUDBHadxQ7LVq2O262uR6y27THp3ZIts_UEg",
  "state": "80051bf4-5123-46a4-8240-c15360002c1a"
}
2025-08-18 20:21:04 - INFO - ==================================================
2025-08-18 20:21:05 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_229f97fdeaa74445a241d166aae8f3f3&state=80051bf4-5123-46a4-8240-c15360002c1a&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-18 20:21:08 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:21:08 - INFO - 添加session: <EMAIL>   ed713880b27e8503b5376a51c3c51360304f96cd3438995619f35e8e3ebafe08   https://d5.api.augmentcode.com/  2025-08-25T12:20:46Z
2025-08-18 20:21:08 - INFO - email:<EMAIL> === cookie:.eJxNjsFugzAQRP9lzxCxGBtvTvkTtGHXrRVskEMqNW3-PRbqoccZzbyZH5g2LYmz5h3Oe3loA4FTXL6nzEnhDNDAR_zS_E9H2abHXcsUpRqaOC6_zrNBHmQwKD70ZpTr7CVwjec1z7VpCU1PFjvrEZGIBtPAgTkIlXST8PzE0VqLZDu63BOXXdb5puW05iVmhb_GMTw7sqZjbNERtUNAakk6br1z9cK1p3Hs4fUG4_lE0w.aKMasQ.cUhABltJyPzbg9lOwnSW-weCByU
2025-08-18 20:21:08 - INFO - 
自动化流程成功完成！
2025-08-18 20:21:08 - INFO - 添加第84个
2025-08-18 20:21:23 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62477,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:21:23 - INFO - 127.0.0.1:62477
2025-08-18 20:21:24 - INFO - <EMAIL>
2025-08-18 20:21:24 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:22:27 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:22:32 - INFO - 找到 Turnstile...
2025-08-18 20:22:33 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:22:37 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:22:37 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:22:45 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:22:50 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:22:50 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:22:50 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:22:50 - INFO - 成功！当前在订阅页面。
2025-08-18 20:22:50 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:22:50 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:23:01 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 20:23:04 - INFO - 代码信息: {
  "codeVerifier": "7lIHVF8TjgIkQCLEeG008SHfOO9IStGFsZZAFnhH9r4",
  "code_challenge": "g0mGRLdxMv5QIr0ASqu8pDdTp39DHMp1x586WK9PX_M",
  "state": "cb4161e7-17ac-4e0b-bd43-98bd8f89a494"
}
2025-08-18 20:23:04 - INFO - ==================================================
2025-08-18 20:23:05 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_fc357216d43edec93cc3bbc72a599cd9&state=cb4161e7-17ac-4e0b-bd43-98bd8f89a494&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-18 20:23:07 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:23:07 - INFO - 添加session: <EMAIL>   c78d6e94662ed785f5cc930b6156b6b13bd5fe1a7f8f2e693a081ec22194e8d4   https://d13.api.augmentcode.com/  2025-08-25T12:22:47Z
2025-08-18 20:23:07 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mAo9OnKPyFtZzCNdCAFTYz671Y3uryvcx8wrlSyZ-Idjnu5UgOTz2m-j-wzwRGggXO6Ef_phOt43aiMCatB2af5qa0fROicHATaqR8MYo_eUq3zwrEujVY1dEJaJ5VWg5ANfClfQAXtnAiFUUoJp407bdmXHZd4oXJYeE78gf1-RaToZGdbMqFvpQ-mDc6qFqUKGFEbOzl4vQGQ_EVl.aKMbKQ.Vg70BnNMX7fUNIB_Xb_hmfP052M
2025-08-18 20:23:07 - INFO - 
自动化流程成功完成！
2025-08-18 20:23:07 - INFO - 添加第85个
2025-08-18 20:23:22 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63281,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:23:22 - INFO - 127.0.0.1:63281
2025-08-18 20:23:23 - INFO - <EMAIL>
2025-08-18 20:23:23 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:23:51 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:23:55 - INFO - 找到 Turnstile...
2025-08-18 20:23:57 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:24:00 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:24:00 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:24:26 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:24:26 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:24:31 - INFO -    等待 4.80 秒...
2025-08-18 20:24:41 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 20:24:41 - INFO - 
第 2/5 次尝试注册...
2025-08-18 20:24:41 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:24:41 - INFO - 成功！当前在订阅页面。
2025-08-18 20:24:41 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:24:41 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:24:51 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 20:24:54 - INFO - 代码信息: {
  "codeVerifier": "sfL49LsXXGuSE827YXYd7mE6GYgWCD0TYuTF9dmRLAk",
  "code_challenge": "WfXvvylXjEWDX3a_x384JyCazp0fBdD3Tij2jyMs_FQ",
  "state": "456428fb-7747-492b-8472-7cc699e3dc82"
}
2025-08-18 20:24:54 - INFO - ==================================================
2025-08-18 20:24:55 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_6d4a538701770710d4cf6603804300ec&state=456428fb-7747-492b-8472-7cc699e3dc82&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-18 20:25:00 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:25:00 - INFO - 添加session: <EMAIL>   19b85c2287ffc056044e4d80ebec841aed054febbac6d8fc7867e6610289d855   https://d9.api.augmentcode.com/  2025-08-25T12:24:33Z
2025-08-18 20:25:00 - INFO - email:<EMAIL> === cookie:.eJxNjcFuwzAMQ_9F56SwYiuOe9qfBKqldsZip3PTAUO3f68R9FCAFxLk4wPmq9bMRcsGx63etYMz57T8zoWzwhGgg0v60fLmk1zn-03rnKQFmjktf-PEFk8UnUWZzoP1InESE1u9rCW2pSdyAcPghiZLIxJ1sGN2QiOxxO9PbD3C4EP4uGWum6zxS-thLUsqCq_FfoxGdTgx9V686d2I2LPzrqfJGbbiiIKB_yfLHURs.aKMblw.2sGPFqWBOo-BePqfFII3QzMV_1g
2025-08-18 20:25:00 - INFO - 
自动化流程成功完成！
2025-08-18 20:25:00 - INFO - 添加第86个
2025-08-18 20:25:17 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63593,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:25:17 - INFO - 127.0.0.1:63593
2025-08-18 20:25:18 - INFO - <EMAIL>
2025-08-18 20:25:18 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:26:20 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:26:24 - INFO - 找到 Turnstile...
2025-08-18 20:26:25 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:26:29 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:26:54 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:26:54 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:27:08 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:27:08 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:27:08 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:27:11 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 20:27:11 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 20:27:14 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63857,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:27:14 - INFO - 127.0.0.1:63857
2025-08-18 20:27:15 - INFO - <EMAIL>
2025-08-18 20:27:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:28:03 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:28:06 - INFO - 找到 Turnstile...
2025-08-18 20:28:08 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:28:11 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:28:20 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:28:20 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:28:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:28:21 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:28:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:28:28 - INFO -    等待 4.10 秒...
2025-08-18 20:28:35 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 20:28:35 - INFO - 
第 2/5 次尝试注册...
2025-08-18 20:28:35 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:28:38 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 20:28:38 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 20:28:42 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64029,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:28:42 - INFO - 127.0.0.1:64029
2025-08-18 20:28:42 - INFO - <EMAIL>
2025-08-18 20:28:42 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:29:21 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:29:25 - INFO - 找到 Turnstile...
2025-08-18 20:29:30 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:29:33 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:29:41 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:29:41 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:29:55 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:29:55 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:29:55 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:29:55 - INFO - 成功！当前在订阅页面。
2025-08-18 20:29:55 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:29:55 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:30:06 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 20:30:09 - INFO - 代码信息: {
  "codeVerifier": "L3vqb5HPMVrCuUQozfDoHiztl0YBX-B-5frJ3Lka7Cc",
  "code_challenge": "2MihiuNTT2d6xmns6V5D6KsWQIst-z-AG5bHps3z0Sw",
  "state": "f56084e4-8069-4499-8d40-9155fa9bcecd"
}
2025-08-18 20:30:09 - INFO - ==================================================
2025-08-18 20:30:10 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_73f2f9704003f78a75792f6144e27146&state=f56084e4-8069-4499-8d40-9155fa9bcecd&tenant_url=https%3A%2F%2Fd15.api.augmentcode.com%2F
2025-08-18 20:30:12 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:30:12 - INFO - 添加session: <EMAIL>   72d9f3871cba3abc3fb8b10fc28dfbca3910f5000299eeb883533c309b4fa4a1   https://d15.api.augmentcode.com/  2025-08-25T12:29:44Z
2025-08-18 20:30:12 - INFO - email:<EMAIL> === cookie:.eJxNjc0OwiAQhN9lz61h-RHoyTdp1rJYYqEGq8ao727TePA4k_m-eUF_4ZqpcFmgW-qNG4iU0_TsC2WGDqCBU7pz-cspXPrblWufwlpwpjS9944UHiNqhcFFqWxg1qjkOi9zGVbSaYEOvbBKCInorDANbJrNsJpGnh8jWmMMeo_6cM1UlzAPZ667uUypMPyI7ZgiUgzatlZ63Wreu9aFwbQyOPbRSiYp4fMFqLlFGQ.aKMc0g.uNBuulbdE3CoaNI7rXmT1YA3j0I
2025-08-18 20:30:12 - INFO - 
自动化流程成功完成！
2025-08-18 20:30:12 - INFO - 添加第87个
2025-08-18 20:30:31 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64242,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:30:31 - INFO - 127.0.0.1:64242
2025-08-18 20:30:32 - INFO - <EMAIL>
2025-08-18 20:30:32 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:31:33 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:31:37 - INFO - 找到 Turnstile...
2025-08-18 20:32:18 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:32:21 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:32:21 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:33:05 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:33:07 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:33:07 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:33:07 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:33:51 - INFO -    等待 4.07 秒...
2025-08-18 20:34:00 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 20:34:00 - INFO - 
第 2/5 次尝试注册...
2025-08-18 20:34:00 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:34:03 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 20:34:03 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 20:34:07 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64434,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:34:07 - INFO - 127.0.0.1:64434
2025-08-18 20:34:08 - INFO - <EMAIL>
2025-08-18 20:34:08 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:34:36 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:34:40 - INFO - 找到 Turnstile...
2025-08-18 20:35:03 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:35:06 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:35:06 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:35:31 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:35:44 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:35:44 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:35:44 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:35:44 - INFO - 成功！当前在订阅页面。
2025-08-18 20:35:44 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:35:44 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:35:44 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:35:47 - INFO - 代码信息: {
  "codeVerifier": "zNeH-R4Ht0QJ9ZBCzIh3dusgF6hV2eOcxHUg_fhszc4",
  "code_challenge": "BCyzXIVyIJfFNo3nhAvH_HW9nlodDw3KCAuXbfNw4Bg",
  "state": "26147fee-5958-498f-8fef-c32115882742"
}
2025-08-18 20:35:47 - INFO - ==================================================
2025-08-18 20:35:48 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_431878046752d940315b5c2609fa6e1f&state=26147fee-5958-498f-8fef-c32115882742&tenant_url=https%3A%2F%2Fd2.api.augmentcode.com%2F
2025-08-18 20:35:50 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:35:50 - INFO - 添加session: <EMAIL>   16f5e14b8010844af55c9de0608df570f44695b0f44a56dac90838de3d73cc0f   https://d2.api.augmentcode.com/  2025-08-25T12:35:35Z
2025-08-18 20:35:50 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2Da7R9w8k1IYRdtpMVU0Bj13W2IBy-TzGTmmxf0V87RJ04rdGveuILJxzA_--QjQwdQwSncOf35QNd-u3HuA5WAow_z2zZeSUKvizYTKkdS0ThMpZ6WNJYlWitEq9EKg9hqZ42rYMfshEJ6rHwm6YwxKBDd8RZ9XmkZL5wPS5pDYvgt9mNlyZBSuh5Ey7UmxnqQnuqmlYNQdpRaG_h8AdHDRP4.aKMeJA.E8nfdZQ4zx6THyaFGFzjYuThFlU
2025-08-18 20:35:50 - INFO - 
自动化流程成功完成！
2025-08-18 20:35:50 - INFO - 添加第88个
2025-08-18 20:36:34 - INFO - 请求出现错误: HTTPSConnectionPool(host='es.slogo.eu.org', port=443): Max retries exceeded with url: /admin/new_address (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))
2025-08-18 20:36:34 - INFO - 添加第89个
2025-08-18 20:36:54 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65100,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:36:54 - INFO - 127.0.0.1:65100
2025-08-18 20:36:54 - INFO - <EMAIL>
2025-08-18 20:36:54 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:37:25 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:37:29 - INFO - 找到 Turnstile...
2025-08-18 20:37:33 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:37:36 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:37:37 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:38:23 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:38:38 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:38:38 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:38:38 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:38:38 - INFO - 成功！当前在订阅页面。
2025-08-18 20:38:38 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:38:38 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:38:38 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:38:41 - INFO - 代码信息: {
  "codeVerifier": "GtVKsUqPOJeuK1EzprBmq7YuwYMRBS5p1fNf9Ke7X48",
  "code_challenge": "O5H7UZ18XKooBjE4LwjzfufB9hyHynfTF1w0Tnd8Jz8",
  "state": "98f654c1-9d30-45ca-9952-c2e1479e412c"
}
2025-08-18 20:38:41 - INFO - ==================================================
2025-08-18 20:38:42 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_57dd0ce5ba902da70fe32b888589259a&state=98f654c1-9d30-45ca-9952-c2e1479e412c&tenant_url=https%3A%2F%2Fd19.api.augmentcode.com%2F
2025-08-18 20:38:44 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:38:44 - INFO - 添加session: <EMAIL>   8e6773db1302819a8936baa228909423c9b5e14a0db21bdf1ad93c3fee4bd37b   https://d19.api.augmentcode.com/  2025-08-25T12:38:26Z
2025-08-18 20:38:44 - INFO - email:<EMAIL> === cookie:.eJxNjU0OgjAQhe8yazCUdtrCypuQQqemkQ6koAlR726jLly-v-89YFgpJ8fEO_R7vlEFwaU4HwO7RNADVHCJd-I_Hf063DbKQ_TFoOTi_NTWSUHWKSm8Da00XlKHZix1XngqS2XRGo0oTaO1wraxWMEH8yEU0hb4OIRBLKEWzXlLLu9-ma6UTwvPkQl-i--xnkh0wdfKBqyVnpp6NK2vqev0iMZjax283sTNRVo.aKMe0g.krI4vS6h6-lAL8HilizZ_R3R63w
2025-08-18 20:38:44 - INFO - 
自动化流程成功完成！
2025-08-18 20:38:44 - INFO - 添加第90个
2025-08-18 20:38:59 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65327,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:38:59 - INFO - 127.0.0.1:65327
2025-08-18 20:38:59 - INFO - <EMAIL>
2025-08-18 20:38:59 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:39:40 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:39:44 - INFO - 找到 Turnstile...
2025-08-18 20:39:44 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:39:47 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:39:47 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:39:54 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:39:59 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:39:59 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:39:59 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:39:59 - INFO - 成功！当前在订阅页面。
2025-08-18 20:39:59 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:39:59 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:40:01 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:40:04 - INFO - 代码信息: {
  "codeVerifier": "Z3JtEka0w8VK9GlLbhI3JGmdWXi2jnR0CEhFE04GqQg",
  "code_challenge": "oC2kRl7i4np9XXtFosrKKSILP0D-YDIfEpXwtcc-a-Q",
  "state": "2163a8e0-40ee-45c1-9e9b-e86f80f99f1c"
}
2025-08-18 20:40:04 - INFO - ==================================================
2025-08-18 20:40:05 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_0c995f611e872b82c338ef92c9118d7f&state=2163a8e0-40ee-45c1-9e9b-e86f80f99f1c&tenant_url=https%3A%2F%2Fd1.api.augmentcode.com%2F
2025-08-18 20:40:07 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:40:07 - INFO - 添加session: <EMAIL>   96723fd21aa2a3ef787b538bc09103356e69ae4e0f0e27e665d766d76c794ef1   https://d1.api.augmentcode.com/  2025-08-25T12:39:57Z
2025-08-18 20:40:07 - INFO - email:<EMAIL> === cookie:.eJxNjdEOwiAMRf-lz5sZg0Lnk3-yIJSFOJhhm4lR_12y-OBLm97cc_qC8c4l2cx5g_NWdm4g2BTn55htYjgDNDDFB-e_O_r7uK9cxuhrwMnG-a3JShGEUFJ4Cr00XvG146HW85JdJUkIaYQe6iatpOlQN3BoDkM1hSmWIgwi9p2ReFmTLZtf3I3LaclzzAw_4nisnEYazNAi1aEo-NZ6p1pCJVWPJpBD-HwBluREUw.aKMfJQ.ZNbvvfu00l2djk0lFrs4XsK6Bq8
2025-08-18 20:40:07 - INFO - 
自动化流程成功完成！
2025-08-18 20:40:07 - INFO - 添加第91个
2025-08-18 20:40:29 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:49157,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:40:29 - INFO - 127.0.0.1:49157
2025-08-18 20:40:29 - INFO - <EMAIL>
2025-08-18 20:40:29 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:41:47 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:41:51 - INFO - 找到 Turnstile...
2025-08-18 20:41:59 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:42:02 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:42:02 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:42:09 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:42:22 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:42:22 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:42:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:42:22 - INFO - 成功！当前在订阅页面。
2025-08-18 20:42:22 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:42:22 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:42:22 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:42:25 - INFO - 代码信息: {
  "codeVerifier": "iOBpXD66tHzVmM_QX6DWq6idRNfULCaLEUI23nO0N8w",
  "code_challenge": "L_uWhI5r1pQ5LOXAYSa-v1pLpYDWYE-gvAjyXqKRZ4s",
  "state": "798ef7af-78b6-402d-a89a-dcc6ab7be216"
}
2025-08-18 20:42:25 - INFO - ==================================================
2025-08-18 20:42:25 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_b85e0bfa4edc07221a07b4ee9882210f&state=798ef7af-78b6-402d-a89a-dcc6ab7be216&tenant_url=https%3A%2F%2Fd20.api.augmentcode.com%2F
2025-08-18 20:42:28 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:42:28 - INFO - 添加session: <EMAIL>   9c6f94b5e083f8b00cadc78e8ff08cd61a4af2d0a7ecd3e73af5d8302c739305   https://d20.api.augmentcode.com/  2025-08-25T12:42:12Z
2025-08-18 20:42:28 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtR1EpL5Z9SYGYVGFUIsOZKdA0fbuMYwsspzBvDe_MN2lN1bRHW57f8gAhVtdfiblJnADGOCzfou-5Zrv02OTPtV8FNK4Ln8-MpmSvCWTY0EK2UnJno65rjofpA1kKFnrPKarT97FAU7LKThE21qbmuCcw2tE97E17nte5y_pl1WXqgIv4vwlYpoZ_Ziyi6M1iGMSH8cQA2OYS-CI8P8EcM1EWA.aKMfsg.FjGfZDDb0omY-Vza3_V6dMNWuv8
2025-08-18 20:42:28 - INFO - 
自动化流程成功完成！
2025-08-18 20:42:28 - INFO - 添加第92个
2025-08-18 20:42:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:49378,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:42:44 - INFO - 127.0.0.1:49378
2025-08-18 20:42:45 - INFO - <EMAIL>
2025-08-18 20:42:45 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:43:54 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:43:58 - INFO - 找到 Turnstile...
2025-08-18 20:43:58 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:44:02 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:44:48 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:44:48 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:44:57 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:44:57 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:44:57 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:44:57 - INFO - 成功！当前在订阅页面。
2025-08-18 20:44:57 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:44:57 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:44:57 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:45:00 - INFO - 代码信息: {
  "codeVerifier": "sB04uEmJn7xoo7_VLCd-vCKtPS7tTWzgNXJHaYwQvKk",
  "code_challenge": "ucIaBeqJokled-BMJknSvoD4sjfaMOxSGmHT0Mtd3Ho",
  "state": "a582a4c8-cabe-46f2-b9fe-d12aa57932e7"
}
2025-08-18 20:45:00 - INFO - ==================================================
2025-08-18 20:45:01 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_6a7ddb9e9467916d8c4c5e04cfa23ee3&state=a582a4c8-cabe-46f2-b9fe-d12aa57932e7&tenant_url=https%3A%2F%2Fd3.api.augmentcode.com%2F
2025-08-18 20:45:03 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:45:03 - INFO - 添加session: <EMAIL>   955d8f81b32eaf2c1f217a3265ade72cc6868b5bd7cfb106e58b4e55aa0de9dd   https://d3.api.augmentcode.com/  2025-08-25T12:44:51Z
2025-08-18 20:45:03 - INFO - email:<EMAIL> === cookie:.eJxNjktuwzAQQ-8ya7sY_S2vehND1YwDIdY4UOwCRdu7V80mWREkiEd-w3LjVpOwHDAf7eQB1lTL9rVIqgwzwACX8sny4gvdlvPObSnUA66pbD9-SkYjrtYomlZtAnmKOXOvyy5dZ22M9VFFpZUJBi36MMAD8yB0Uj6PVVRwzmmMHt_vNbWD9nzl9rbLVuSf9hy2LmrjshuRch4tEo79wzSG6YOj56QVKfj9A7GfRNg.aKMgTg.wFoFNuJZs3yJfifReTAqh1Fj7WY
2025-08-18 20:45:03 - INFO - 
自动化流程成功完成！
2025-08-18 20:45:03 - INFO - 添加第93个
2025-08-18 20:45:22 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:50214,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:45:22 - INFO - 127.0.0.1:50214
2025-08-18 20:45:23 - INFO - <EMAIL>
2025-08-18 20:45:23 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:46:05 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:46:05 - INFO - 准备重启流程...
2025-08-18 20:46:07 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:50269,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:46:07 - INFO - 127.0.0.1:50269
2025-08-18 20:46:08 - INFO - <EMAIL>
2025-08-18 20:46:08 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:47:12 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:47:16 - INFO - 找到 Turnstile...
2025-08-18 20:47:58 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:48:01 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:48:01 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:48:01 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:48:01 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:48:14 - INFO -    等待 3.47 秒...
2025-08-18 20:48:24 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 20:48:24 - INFO - 
第 2/5 次尝试注册...
2025-08-18 20:48:24 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:48:24 - INFO - 成功！当前在订阅页面。
2025-08-18 20:48:24 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:48:24 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:48:34 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-18 20:48:37 - INFO - 代码信息: {
  "codeVerifier": "3zxirHylRfBTgj1dzKJ9GjabA_0UkR9sGGYxO8OD5PQ",
  "code_challenge": "YPjQO4OZUZ06YEKEZWWn8jhoSss9rxOCMqZyAT3g13k",
  "state": "f107f294-e312-4f3c-a491-cec9d5c613b0"
}
2025-08-18 20:48:37 - INFO - ==================================================
2025-08-18 20:48:38 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_9a8370936a49dfb178829fbd2bf2024b&state=f107f294-e312-4f3c-a491-cec9d5c613b0&tenant_url=https%3A%2F%2Fd20.api.augmentcode.com%2F
2025-08-18 20:48:40 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:48:40 - INFO - 添加session: <EMAIL>   28bc65e41bec558d3657b055eee58012cfa021fde913735c4ffa50a20c57f6fa   https://d20.api.augmentcode.com/  2025-08-25T12:48:11Z
2025-08-18 20:48:40 - INFO - email:<EMAIL> === cookie:.eJxNjcFugzAQRP9lz1DZa9asc8qfIJddIivYIEMiRUn_vQj10OOM5r15w7BqzbFo2eGy14c2MMWc5tdQYla4ADRwS08t_3KSdXhsWockR6E5pvnjOTo0Qp2zwhO6Xpgm7viYl6WMB4nOUDBIjjEEwo5MA6flFByi73Xbk-2JCK21fN1yrLss413r11LmVBT-iPN3Uu9HF8bWoIS2Q6E2aC-tF2JRz2Kth59fRLBEdw.aKMhJg.gIVNTRVvWSeZVi5SJ1N6BgqGQds
2025-08-18 20:48:40 - INFO - 
自动化流程成功完成！
2025-08-18 20:48:40 - INFO - 添加第94个
2025-08-18 20:48:56 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:50953,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:48:56 - INFO - 127.0.0.1:50953
2025-08-18 20:48:56 - INFO - <EMAIL>
2025-08-18 20:48:56 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:49:38 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:49:42 - INFO - 找到 Turnstile...
2025-08-18 20:50:04 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:50:07 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:50:08 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:50:52 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:51:07 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:51:07 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:51:07 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:51:10 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 20:51:10 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 20:51:13 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52036,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:51:13 - INFO - 127.0.0.1:52036
2025-08-18 20:51:14 - INFO - <EMAIL>
2025-08-18 20:51:14 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:52:43 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:52:47 - INFO - 找到 Turnstile...
2025-08-18 20:52:48 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:52:51 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:52:52 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:53:18 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:53:26 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:53:26 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:53:26 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:53:26 - INFO - 成功！当前在订阅页面。
2025-08-18 20:53:26 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:53:26 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:53:26 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:53:29 - INFO - 代码信息: {
  "codeVerifier": "ib3elS0OL4IiVFaYZF7q2JIGyjqka1Jb-xJNh-f3JTU",
  "code_challenge": "LhDwpaG-_iEGdNjCMMwXq0npFil5iV8VjXiXG5k8iAI",
  "state": "f3befcef-590a-47b5-83be-c3d22ebde723"
}
2025-08-18 20:53:29 - INFO - ==================================================
2025-08-18 20:53:30 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_67206ebcfb593e531891c4cc4dabf1e7&state=f3befcef-590a-47b5-83be-c3d22ebde723&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-18 20:53:32 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:53:32 - INFO - 添加session: <EMAIL>   a2e7bcb74439b9755417dcd10e7daa9f2d812386fc11048aa333818ca96f0d3c   https://d5.api.augmentcode.com/  2025-08-25T12:53:21Z
2025-08-18 20:53:32 - INFO - email:<EMAIL> === cookie:.eJztU02PmzAU_C9IzWW_sAlJiBS12-****************************-hrbRSe-yxEhYwfjNv5Df-pqUllTkSVNTatJYNvdbSqkY1TVFTH_SUF3smUpyHLXasBsNwR-b8nATgmEGfL3OvQlHYENsqk0_gSLaenkHAMQMAPSeXJPJLxT2qfYDzk4Xmvo7nLnf1R-6eYrWSA57fN_E2FFmn43hfE2fTxNCql8HSUbiOHjYXP_cOmfPC4siTS-Hp8dbnTyEJkEF2ceuvwsA2N4CvohNff4mWnwNRGnF00N0oBu6cbIluukF-APSRLNawypX-KQmtI3LsKnPCJoEby9sk8yDwpb_1VxgsWmR8FGHgMy-qoWvbsWcvd9GGOL7gDwkInSxI4Mrem6uHpD-LLfSKOHqpksgUSrtOtn6RwbW1aRfAYwl4ChbtWscX1w4l3npSm37TCKpR9xaFwFSbavujnQTn9ll4jx_X8vPlfp-ctWtNUsIkxXXaSKaqDnVdVtO7u25Ct6jZ52p4uCD0Fhf5XdGh8A4jzjOET4rdSP6G1A_0D1ZHKiRr6XtJq7IQFU3rS0lnXcEAc6ZqU0ZmZ_4lCCPn025RR9a8ZYvi_Owzuvb8C3efAjR463TWt3xn3L-Dtnr-5lbBP_2qj9-OBxUuVGe1BCNXNEeMX5Wy2DFOB30yZ__T-O_TOFBHnJf1rI_HoM_j7K9pfL3W6EupTcHYNE1o6uPRrQGhPjKHameHcsYvqUB5l2aVvT37SsWbf0bKtKmoVGFSQD_c76MJMiAY46EByGQHjTGx0EQhqvzXvei6jI0hBMAaDkemORmNVaw7mV5BKbVVQdhPS8Aw4IcqR7ImBT5ReVsIzgTVfjH6xoaumwaxxjcQTsDNMCPGTQYIulFcirA5AnA00l5_AAmFtjQ.aKMiSg.Chk3aQtsTS2iRmzeduWTxEcZz8M
2025-08-18 20:53:32 - INFO - 
自动化流程成功完成！
2025-08-18 20:53:32 - INFO - 添加第95个
2025-08-18 20:53:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52254,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:53:51 - INFO - 127.0.0.1:52254
2025-08-18 20:53:51 - INFO - <EMAIL>
2025-08-18 20:53:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:54:19 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:54:23 - INFO - 找到 Turnstile...
2025-08-18 20:55:04 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:55:08 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:55:08 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:55:53 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:56:15 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:56:15 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:56:15 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:56:15 - INFO - 成功！当前在订阅页面。
2025-08-18 20:56:15 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:56:15 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:56:15 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:56:18 - INFO - 代码信息: {
  "codeVerifier": "lZE_TwVQIcOKb8W1orapfj1J7pdhDUCSIVPfi5w5XhA",
  "code_challenge": "-gWhdHmSv2eOsUXYtLwKVN0xk84vYRDh2RikmWHgzYc",
  "state": "9920febc-7c30-4f2d-8626-c6d93fcf10f7"
}
2025-08-18 20:56:18 - INFO - ==================================================
2025-08-18 20:56:20 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_973b0f2be593610d9c00429acf2f3c24&state=9920febc-7c30-4f2d-8626-c6d93fcf10f7&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-18 20:56:22 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:56:22 - INFO - 添加session: <EMAIL>   55f2308784a054402974e4b9dd8391503301ebec5f26bb0c34de7a15f72071fe   https://d10.api.augmentcode.com/  2025-08-25T12:55:56Z
2025-08-18 20:56:22 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mD6oLS48k9I2xlMIy2kFBOj_ruVuHB5X-c-YVwpR5soFTiXvFMDk41hfozJRoIzQAPXcKf0pwOu475RHgNWg6IN86s3VgphWCc5mklIjc6ZgXytpyX5utTC9GbgTNRQKSUZlw0cmINQSbsr886_oeC90Jct2lxw8TfKpyXNIRH8FscxrzXHvGr7zrG2mzy1jmlsvUOGTpFXFuH9AavzRcs.aKMi9A.XRAvVJfP28jgYz49gpoUPqxOkt4
2025-08-18 20:56:22 - INFO - 
自动化流程成功完成！
2025-08-18 20:56:22 - INFO - 添加第96个
2025-08-18 20:56:38 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52568,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:56:38 - INFO - 127.0.0.1:52568
2025-08-18 20:56:39 - INFO - <EMAIL>
2025-08-18 20:56:39 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 20:57:24 - INFO - 获取 cap_value 验证码成功...
2025-08-18 20:57:28 - INFO - 找到 Turnstile...
2025-08-18 20:57:29 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 20:57:33 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 20:57:33 - INFO - 验证码已提交，等待跳转...
2025-08-18 20:57:58 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 20:58:28 - INFO - 鼠标左键按住并无规律滑动操作完成。
2025-08-18 20:58:28 - INFO - 鼠标左键放开操作完成。
2025-08-18 20:58:28 - INFO - 鼠标左键点击页面操作完成。
2025-08-18 20:58:34 - INFO - 再次执行鼠标左键按下操作完成。
2025-08-18 20:58:37 - INFO - 验证成功，已进入目标页面。
2025-08-18 20:58:37 - INFO - 
第 1/5 次尝试注册...
2025-08-18 20:58:37 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 20:58:37 - INFO - 成功！当前在订阅页面。
2025-08-18 20:58:37 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 20:58:37 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 20:58:37 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 20:58:40 - INFO - 代码信息: {
  "codeVerifier": "1RZNSWTaBwSXBfs1omy_J3a4ylWPIiuInyRYSiHPMPE",
  "code_challenge": "VWGH4aIXdH0rpSZyjnEl3MvCdTLwgdL9kAPZBr_IBcE",
  "state": "43125184-d898-4a67-8c37-6c1e13288927"
}
2025-08-18 20:58:40 - INFO - ==================================================
2025-08-18 20:58:41 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_b1acdf069c350320b552d689716dc4d5&state=43125184-d898-4a67-8c37-6c1e13288927&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-18 20:58:43 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 20:58:43 - INFO - 添加session: <EMAIL>   708f0240050333d77f4c2c784175bf5ccbf796a154515a28b93ed7bdff0c0a2f   https://d12.api.augmentcode.com/  2025-08-25T12:58:01Z
2025-08-18 20:58:43 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mBoS1-u_BNSpoM20oIFTIz67zbowuV9nfuEbqYcXaK0wnHNG1UwuBjGR5dcJDgCVHAOd0p_Ovi52xbKXfDFoOjC-FLGCS6EbQXzZuBCey_YwGSppylhWcqmxFYyrhtlrBENVxXsmJ1QSBdc5hvTUkrOtG1PS3R59RNeKR-mNIZE8Ft8j4W2RmFfDx5N3WLPa-MaXStEQ0opzwjh_QGCZ0U3.aKMjgQ.Eo4sQhS62-itK0sDjk5E1V2LRM8
2025-08-18 20:58:43 - INFO - 
自动化流程成功完成！
2025-08-18 20:58:43 - INFO - 添加第97个
2025-08-18 20:59:05 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52871,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 20:59:05 - INFO - 127.0.0.1:52871
2025-08-18 20:59:05 - INFO - <EMAIL>
2025-08-18 20:59:05 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 21:00:05 - INFO - 获取 cap_value 验证码成功...
2025-08-18 21:00:09 - INFO - 找到 Turnstile...
2025-08-18 21:00:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 21:00:18 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 21:00:19 - INFO - 验证码已提交，等待跳转...
2025-08-18 21:00:29 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 21:00:29 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 21:00:29 - INFO - 
第 1/5 次尝试注册...
2025-08-18 21:00:29 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 21:00:38 - INFO -    等待 3.70 秒...
2025-08-18 21:00:47 - INFO - 刷新完成，继续下一次尝试。
2025-08-18 21:00:47 - INFO - 
第 2/5 次尝试注册...
2025-08-18 21:00:47 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 21:00:47 - INFO - 成功！当前在订阅页面。
2025-08-18 21:00:47 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-18 21:00:47 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-18 21:00:47 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 21:00:50 - INFO - 代码信息: {
  "codeVerifier": "a3nxAGMhkTCibl1XkRFrlWzhGxNoCajUQ9Z0hmzXV68",
  "code_challenge": "fj8C3jVC6xYmZTEJgIFXrJ-K1WtVgpW0bp2Ef2ihMAc",
  "state": "c87a2748-f4e4-4d43-875e-2dfeb3b62584"
}
2025-08-18 21:00:50 - INFO - ==================================================
2025-08-18 21:00:51 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_7ce649930161822977b5c26289185e05&state=c87a2748-f4e4-4d43-875e-2dfeb3b62584&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-18 21:00:53 - INFO - 添加session成功: {'status': 'success'}
2025-08-18 21:00:53 - INFO - 添加session: <EMAIL>   94aa7f9e9b9604793435439abaaf27b66227042db7e271c97bec6a5161a8e2c1   https://d12.api.augmentcode.com/  2025-08-25T13:00:52.422899135Z
2025-08-18 21:00:53 - INFO - email:<EMAIL> === cookie:.eJxNjcEOwiAQRP9lz62BBYR68k-aFRZDLNRgqzHqv0saDx5nMu_NC8Yr10yFywKHpa7cQaScpudYKDMcADo4pzuXv5zCdVxvXMcUWsGZ0vTeO1KoAmklg4uobGBHHoc2L3PxjVQW91qj0GoQxsiWsINNsxma6bk-liytMQbloOXxlqkuYfYXrru5TKkw_IjtWESNcThRzychex2t610g6q0QwvkoPKKHzxfT50WH.aKMkAw.O75SxU23jjigBodxGdm3vy8kHR0
2025-08-18 21:00:53 - INFO - 
自动化流程成功完成！
2025-08-18 21:00:53 - INFO - 添加第98个
2025-08-18 21:01:12 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53188,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-18 21:01:12 - INFO - 127.0.0.1:53188
2025-08-18 21:01:12 - INFO - <EMAIL>
2025-08-18 21:01:12 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-18 21:02:33 - INFO - 获取 cap_value 验证码成功...
2025-08-18 21:02:37 - INFO - 找到 Turnstile...
2025-08-18 21:02:39 - INFO - 登录信息已提交，等待验证页面...
2025-08-18 21:02:42 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-18 21:02:51 - INFO - 验证码已提交，等待跳转...
2025-08-18 21:02:51 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-18 21:03:00 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-18 21:03:00 - INFO - 
第 1/5 次尝试注册...
2025-08-18 21:03:00 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-18 21:03:03 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-18 21:03:03 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-18 21:03:20 - INFO - 浏览器打开失败: {'code': 101, 'msg': 'timeout of 15000ms exceeded', 'data': None}
