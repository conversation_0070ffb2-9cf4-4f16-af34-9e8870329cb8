2025-08-14 09:22:32 - INFO - 测试 HAR 功能...
2025-08-14 09:22:32 - INFO - HAR 文件已保存: har_files\test_har_20250814_092232.har
2025-08-14 09:22:32 - INFO - 捕获了 1 个网络请求
2025-08-14 09:22:32 - INFO - 捕获了 1 个 WebSocket 连接
2025-08-14 09:22:32 - INFO -   WebSocket: wss://app.augmentcode.com/ws - 4 个消息帧
2025-08-14 09:22:32 - INFO - WebSocket 摘要已保存: websocket_logs\test_websocket_20250814_092232.json
2025-08-14 09:22:32 - INFO - HAR 和 WebSocket 功能测试成功！
2025-08-14 09:22:32 - INFO - HAR 文件: har_files\test_har_20250814_092232.har
2025-08-14 09:22:32 - INFO - WebSocket 摘要: websocket_logs\test_websocket_20250814_092232.json
2025-08-14 09:22:32 - INFO - WebSocket 测试统计: {'total_connections': 1, 'active_connections': 0, 'closed_connections': 1, 'total_frames': 4, 'sent_frames': 2, 'received_frames': 2}
2025-08-14 09:22:32 - INFO - ==================================================
2025-08-14 09:22:35 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52517,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:22:38 - INFO - 
主流程中发生严重错误: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: loggingPrefs
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff678488c8d]
	(No symbol) [0x0x7ff67848a5c8]
	(No symbol) [0x0x7ff678484a5a]
	(No symbol) [0x0x7ff6784e4062]
	(No symbol) [0x0x7ff6784e3977]
	(No symbol) [0x0x7ff6784e5880]
	(No symbol) [0x0x7ff6784e5630]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:22:38 - INFO - 准备重启流程...
2025-08-14 09:22:38 - INFO - HAR 文件已保存: har_files\exception_jzgdo1755134552_smartdocker.online_20250814_092238.har
2025-08-14 09:22:38 - INFO - 捕获了 1 个网络请求
2025-08-14 09:22:38 - INFO - 捕获了 1 个 WebSocket 连接
2025-08-14 09:22:38 - INFO -   WebSocket: wss://app.augmentcode.com/ws - 4 个消息帧
2025-08-14 09:22:38 - INFO - WebSocket 摘要已保存: websocket_logs\exception_jzgdo1755134552_smartdocker.online_20250814_092238_ws.json
2025-08-14 09:22:40 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52550,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:22:43 - INFO - 
主流程中发生严重错误: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: loggingPrefs
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff678488c8d]
	(No symbol) [0x0x7ff67848a5c8]
	(No symbol) [0x0x7ff678484a5a]
	(No symbol) [0x0x7ff6784e4062]
	(No symbol) [0x0x7ff6784e3977]
	(No symbol) [0x0x7ff6784e5880]
	(No symbol) [0x0x7ff6784e5630]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:22:43 - INFO - 准备重启流程...
2025-08-14 09:22:45 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52585,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:22:47 - INFO - 
主流程中发生严重错误: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: loggingPrefs
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff678488c8d]
	(No symbol) [0x0x7ff67848a5c8]
	(No symbol) [0x0x7ff678484a5a]
	(No symbol) [0x0x7ff6784e4062]
	(No symbol) [0x0x7ff6784e3977]
	(No symbol) [0x0x7ff6784e5880]
	(No symbol) [0x0x7ff6784e5630]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:22:47 - INFO - 准备重启流程...
2025-08-14 09:22:50 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:52618,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:22:52 - INFO - Traceback (most recent call last):

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m362[0m, in [35mstart_session[0m
    response = [31mself.execute[0m[1;31m(Command.NEW_SESSION, caps)[0m["value"]
               [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m454[0m, in [35mexecute[0m
    [31mself.error_handler.check_response[0m[1;31m(response)[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\errorhandler.py"[0m, line [35m232[0m, in [35mcheck_response[0m
    raise exception_class(message, screen, stacktrace)

2025-08-14 09:22:52 - INFO - [1;35mselenium.common.exceptions.InvalidArgumentException[0m: [35mMessage: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: loggingPrefs
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff678488c8d]
	(No symbol) [0x0x7ff67848a5c8]
	(No symbol) [0x0x7ff678484a5a]
	(No symbol) [0x0x7ff6784e4062]
	(No symbol) [0x0x7ff6784e3977]
	(No symbol) [0x0x7ff6784e5880]
	(No symbol) [0x0x7ff6784e5630]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]
[0m

2025-08-14 09:22:52 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-14 09:22:52 - INFO - Traceback (most recent call last):

2025-08-14 09:22:52 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m849[0m, in [35mcreate_connection[0m
    [31msock.connect[0m[1;31m(sa)[0m
    [31m~~~~~~~~~~~~[0m[1;31m^^^^[0m

2025-08-14 09:22:52 - INFO - [1;35mTimeoutError[0m: [35mtimed out[0m

2025-08-14 09:22:52 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-14 09:22:52 - INFO - Traceback (most recent call last):

2025-08-14 09:22:52 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1904[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1656[0m, in [35mmain[0m
    driver = setup_driver()

2025-08-14 09:22:52 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m752[0m, in [35msetup_driver[0m
    driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\chrome\webdriver.py"[0m, line [35m47[0m, in [35m__init__[0m
    [31msuper().__init__[0m[1;31m([0m
    [31m~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mbrowser_name=DesiredCapabilities.CHROME["browserName"],[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    ...<3 lines>...
        [1;31mkeep_alive=keep_alive,[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\chromium\webdriver.py"[0m, line [35m69[0m, in [35m__init__[0m
    [31msuper().__init__[0m[1;31m(command_executor=executor, options=options)[0m
    [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m261[0m, in [35m__init__[0m
    [31mself.start_session[0m[1;31m(capabilities)[0m
    [31m~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m367[0m, in [35mstart_session[0m
    [31mself.service.stop[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\service.py"[0m, line [35m152[0m, in [35mstop[0m
    [31mself.send_remote_shutdown_command[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\service.py"[0m, line [35m137[0m, in [35msend_remote_shutdown_command[0m
    if not [31mself.is_connectable[0m[1;31m()[0m:
           [31m~~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\service.py"[0m, line [35m126[0m, in [35mis_connectable[0m
    return [31mutils.is_connectable[0m[1;31m(self.port)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^[0m

2025-08-14 09:22:52 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\utils.py"[0m, line [35m99[0m, in [35mis_connectable[0m
    socket_ = socket.create_connection((host, port), 1)

2025-08-14 09:22:52 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m856[0m, in [35mcreate_connection[0m
    [31mexceptions.clear[0m[1;31m()[0m  # raise only the last error
    [31m~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 09:22:52 - INFO - [1;35mKeyboardInterrupt[0m

