2025-08-20 23:24:45 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56827,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:24:45 - INFO - 127.0.0.1:56827
2025-08-20 23:24:46 - INFO - <EMAIL>
2025-08-20 23:24:46 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-20 23:25:04 - INFO - 获取 cap_value 验证码成功...
2025-08-20 23:25:04 - INFO - 0.jRgjvqETQGFO-3DaXNWns4asZuHcN0uk7Dl2NPhlYf3w54uZHH22lXXmD7gPAgXnmAQOo7jZLcj3FxxW3UCdZ78E64dmt3E3EplJBaltjashAvkpD0lfYVGlYcaZz4zHTunU3PpmK-J_NvCqFSPpYJg7RtA1FBYc4LCUsJtuEQggQu8IuIcdZitOzNonJ7ksTDUfpkB3Z3VVFkxA0Fs3X1HYO8VcYDCipJOr6mpVho17W8PwNPYtK0IbpePuBM6KQexISfXdMp0gdXsnPXVMlrCtI5NGpdwmGbmCa_yyEOMLmlL9gIyQMHw-x2b7amZz-1zbx5p15rpf5-gQpv6B7zOv_1U920f21_KGpZGHoP9cqrreBsyB1o0CEUvWHhw_JNV--9KGmPPfpsqebivJEAdUZfTEF8r09XL45Y-hgV17LuexJ-aXa_X5YiH_XJoMH3uwzMWC3xr4RcyKqQN_s3JSROSBx6mU4Z6PfHpvW-lKimpVau7Y8dibvhc7s-vBjo0morsGE6UcMsLt2Efr98lyVrAdC8yxHitKoIVGf_KlVOiPRplS6nA3Cn8uyrwOYpJSsv8QgISkKj8Th99WnBJobl2Jk-nJ84ZEo_C0dawP6O6rXlHK-oX4fVVzs4x1DUvcITJwIzfVryootCHxu8SayGMOF6tsT5NWWehML4g1FuboV0WcQ3WMqMAvy5c3cB9UL3aFaPSbeW982fgJOvNGFDfGL5s7W8CkzURUyzncwLuMlQt2DT9B1UQJT_q4s8y7zcxUS_mSwbu4pE8kWuYNH8xgjHB4YpuTgmb5CVs_o7Ud2ssw1fmPOyWCteYldt7zxocRy3E4Pk8Rca2oX9jSmE80QUuSRx2szTGHYlvcILoHWp3dGmqKh1GSeV7E.5CuHx6vc98MIuuAD-Jx-Ww.685b4cdabd7a63361bbcc10dec384259a5f33b91ccef13a2b050a11a7e26f381
2025-08-20 23:25:14 - INFO - 找到 Turnstile...
2025-08-20 23:25:20 - INFO - 
主流程中发生严重错误: Message: stale element reference: stale element not found
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#staleelementreferenceexception
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608490224]
	(No symbol) [0x0x7ff60848ecf3]
	(No symbol) [0x0x7ff6084824e9]
	(No symbol) [0x0x7ff608482663]
	(No symbol) [0x0x7ff60848039f]
	(No symbol) [0x0x7ff608484b71]
	(No symbol) [0x0x7ff6085219cb]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-20 23:25:20 - INFO - 准备重启流程...
2025-08-20 23:25:23 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:56901,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-20 23:25:23 - INFO - 127.0.0.1:56901
2025-08-20 23:25:24 - INFO - Traceback (most recent call last):

2025-08-20 23:25:24 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m849[0m, in [35mcreate_connection[0m
    [31msock.connect[0m[1;31m(sa)[0m
    [31m~~~~~~~~~~~~[0m[1;31m^^^^[0m

2025-08-20 23:25:24 - INFO - [1;35mTimeoutError[0m: [35mtimed out[0m

2025-08-20 23:25:24 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-20 23:25:24 - INFO - Traceback (most recent call last):

2025-08-20 23:25:24 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\runpy.py"[0m, line [35m198[0m, in [35m_run_module_as_main[0m
    return _run_code(code, main_globals, None,
                     "__main__", mod_spec)

2025-08-20 23:25:24 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\runpy.py"[0m, line [35m88[0m, in [35m_run_code[0m
    [31mexec[0m[1;31m(code, run_globals)[0m
    [31m~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\launcher/../..\debugpy\__main__.py"[0m, line [35m71[0m, in [35m<module>[0m
    [31mcli.main[0m[1;31m()[0m
    [31m~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\launcher/../..\debugpy/..\debugpy\server\cli.py"[0m, line [35m501[0m, in [35mmain[0m
    [31mrun[0m[1;31m()[0m
    [31m~~~[0m[1;31m^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\launcher/../..\debugpy/..\debugpy\server\cli.py"[0m, line [35m351[0m, in [35mrun_file[0m
    [31mrunpy.run_path[0m[1;31m(target, run_name="__main__")[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py"[0m, line [35m310[0m, in [35mrun_path[0m
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)

2025-08-20 23:25:24 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py"[0m, line [35m127[0m, in [35m_run_module_code[0m
    [31m_run_code[0m[1;31m(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)[0m
    [31m~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py"[0m, line [35m118[0m, in [35m_run_code[0m
    [31mexec[0m[1;31m(code, run_globals)[0m
    [31m~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m946[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m898[0m, in [35mmain[0m
    driver = setup_driver()

2025-08-20 23:25:24 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m191[0m, in [35msetup_driver[0m
    driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

2025-08-20 23:25:24 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\chrome\webdriver.py"[0m, line [35m47[0m, in [35m__init__[0m
    [31msuper().__init__[0m[1;31m([0m
    [31m~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mbrowser_name=DesiredCapabilities.CHROME["browserName"],[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    ...<3 lines>...
        [1;31mkeep_alive=keep_alive,[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-20 23:25:24 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\chromium\webdriver.py"[0m, line [35m58[0m, in [35m__init__[0m
    [31mself.service.start[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\service.py"[0m, line [35m109[0m, in [35mstart[0m
    if [31mself.is_connectable[0m[1;31m()[0m:
       [31m~~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\service.py"[0m, line [35m126[0m, in [35mis_connectable[0m
    return [31mutils.is_connectable[0m[1;31m(self.port)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^[0m

2025-08-20 23:25:24 - INFO -   File [35m"e:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\utils.py"[0m, line [35m99[0m, in [35mis_connectable[0m
    socket_ = socket.create_connection((host, port), 1)

2025-08-20 23:25:24 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m856[0m, in [35mcreate_connection[0m
    [31mexceptions.clear[0m[1;31m()[0m  # raise only the last error
    [31m~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-20 23:25:24 - INFO - [1;35mKeyboardInterrupt[0m

