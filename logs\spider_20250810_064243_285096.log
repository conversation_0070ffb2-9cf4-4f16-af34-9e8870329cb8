2025-08-10 06:42:47 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61981,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:42:48 - INFO - <EMAIL>
2025-08-10 06:42:48 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:43:01 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:43:04 - INFO - 找到 Turnstile...
2025-08-10 06:43:05 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:43:07 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:43:20 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:43:20 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:43:20 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:43:20 - INFO - 检测到 'Verifying you are human...'（首次），使用网络节流模式
2025-08-10 06:43:20 - INFO - 检测到人机验证，执行 CDP 网络节流 10 秒
2025-08-10 06:43:20 - INFO - 执行网络节流前的拟人操作...
2025-08-10 06:43:20 - INFO - 已执行拟人滚动: 49px
2025-08-10 06:43:20 - INFO - 已执行页面焦点操作
2025-08-10 06:43:21 - INFO - 已执行拟人操作完成，总停顿: 1.07秒
2025-08-10 06:43:27 - INFO - CDP: 已将网络设为节流模式（延迟7755ms），保持 9.3 秒……
2025-08-10 06:43:37 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:43:37 - INFO - 已执行拟人滚动: 49px
2025-08-10 06:43:39 - INFO - 已执行拟人操作完成，总停顿: 1.30秒
2025-08-10 06:43:39 - INFO - CDP: 网络节流已恢复为正常状态。
2025-08-10 06:43:39 - INFO - 成功！当前在订阅页面。
2025-08-10 06:43:39 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:43:39 - INFO - 代码信息: {
  "codeVerifier": "tEAbMih340jN86LozQbNetsufTkao3uh9-Dtlk6u8QY",
  "code_challenge": "awoV_FLtIvkiR_lkvs1s-9WVJOBKup_1a4kzUpYZRdk",
  "state": "90ec98cb-ed55-4266-b9c4-e918c19ff1e9"
}
2025-08-10 06:43:39 - INFO - ==================================================
2025-08-10 06:43:39 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_09ae860d2ae85e01f432c8bfca6efba5&state=90ec98cb-ed55-4266-b9c4-e918c19ff1e9&tenant_url=https%3A%2F%2Fd11.api.augmentcode.com%2F
2025-08-10 06:43:42 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:43:42 - INFO - 添加session: <EMAIL>   f26ce5aea1f6cd3395ae5ca51131f67a7ccc09a4a7c9d4a85ce1500cfa7f65a1   https://d11.api.augmentcode.com/  2025-08-16T22:43:25Z
2025-08-10 06:43:42 - INFO - email:<EMAIL> === cookie:.eJxNjUsOgkAQRO_SazDMt2lX3oTMp8dMAiMikBj17k6IC5dVefXqBcPMy-QKlxXO67JxA8lNeXwOxU0MZ4AGrnnn8pdznIftwcuQYy14cnl8254wcHKsWUZNMXnpySRT8XIroS6xt0qTRmlN1yF1ArGBQ3MYqmnZ734TaDQiKasuxppe4ymkCD_yOJSOggxWtEKF2GqjXFttvmUbUy8lsVYEny984UEB.aJfPHA.7kaVuerIiOUdCc_xHaLkyWq6whM
2025-08-10 06:43:42 - INFO - 
自动化流程成功完成！
2025-08-10 06:43:42 - INFO - 添加第1个
2025-08-10 06:44:06 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62162,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:44:06 - INFO - <EMAIL>
2025-08-10 06:44:06 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:44:21 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:44:24 - INFO - 找到 Turnstile...
2025-08-10 06:44:26 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:44:26 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:44:27 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:44:27 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:44:27 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:44:39 - INFO - 检测到 'Verifying you are human...'（第2次），使用网络离线模式
2025-08-10 06:44:39 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:44:39 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:44:39 - INFO - 已执行拟人滚动: -134px
2025-08-10 06:44:40 - INFO - 已执行页面焦点操作
2025-08-10 06:44:40 - INFO - 已执行拟人操作完成，总停顿: 1.20秒
2025-08-10 06:44:46 - INFO - CDP: 已将网络设为离线，保持 7.7 秒……
2025-08-10 06:44:55 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:44:55 - INFO - 已执行拟人滚动: 92px
2025-08-10 06:44:56 - INFO - 已执行拟人操作完成，总停顿: 0.81秒
2025-08-10 06:44:56 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:44:56 - INFO - 成功！当前在订阅页面。
2025-08-10 06:44:56 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:44:56 - INFO - 代码信息: {
  "codeVerifier": "eZyah5kDluM59XVYYpTKeO0bHb0Fry5x2mFFG0VfsY8",
  "code_challenge": "BFFxGOSb_X4PblXQXGLD-wUD3LicRPABfuuwiSpagkY",
  "state": "e67647f1-9eb8-43a4-a033-98e8b4d50a49"
}
2025-08-10 06:44:56 - INFO - ==================================================
2025-08-10 06:44:56 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_5e8debbb841bfd62012074a95b4844f2&state=e67647f1-9eb8-43a4-a033-98e8b4d50a49&tenant_url=https%3A%2F%2Fd5.api.augmentcode.com%2F
2025-08-10 06:44:58 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:44:58 - INFO - 添加session: <EMAIL>   54b6e5ff99288e68c6bd0b5baac09b13da984c762e86d45c4061851f69f99cb8   https://d5.api.augmentcode.com/  2025-08-16T22:44:44Z
2025-08-10 06:44:58 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2Cg3f4sJ9-ElHarTaASBI1R391KPHiZZCYz3zyhn3mZXOa8QrcuG1cQ3ZTGR5_dxNABVHBKN85_PoW536689CmUgCeXxpe2ZHxEYmQRkEIcZDsYhaWeL9mXpUKphdZWaCFISIutrmDH7IRCGhd3P7dlZAwhNkellUVz8DHAr7kfSoPNF1CTL4LWY02NknWDkUxgrxQRvD8PgT-H.aJfPaQ.erCPOslrLm5ODOCcwvNNVvqI0Ok
2025-08-10 06:44:58 - INFO - 
自动化流程成功完成！
2025-08-10 06:44:58 - INFO - 添加第2个
2025-08-10 06:45:19 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62302,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:45:20 - INFO - <EMAIL>
2025-08-10 06:45:20 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:45:34 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:45:37 - INFO - 找到 Turnstile...
2025-08-10 06:45:39 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:45:39 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:45:39 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:45:39 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:45:39 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:45:52 - INFO - 检测到 'Verifying you are human...'（第3次），使用网络离线模式
2025-08-10 06:45:52 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:45:52 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:45:52 - INFO - 已执行拟人滚动: -20px
2025-08-10 06:45:52 - INFO - 已执行页面焦点操作
2025-08-10 06:45:53 - INFO - 已执行拟人操作完成，总停顿: 0.90秒
2025-08-10 06:45:53 - INFO - CDP: 已将网络设为离线，保持 7.2 秒……
2025-08-10 06:46:01 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:46:01 - INFO - 已执行拟人滚动: -93px
2025-08-10 06:46:01 - INFO - 已执行页面焦点操作
2025-08-10 06:46:02 - INFO - 已执行拟人操作完成，总停顿: 1.12秒
2025-08-10 06:46:02 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:46:18 - INFO -    等待 4.56 秒...
2025-08-10 06:46:28 - INFO - 刷新完成，继续下一次尝试。
2025-08-10 06:46:28 - INFO - 
第 2/5 次尝试注册...
2025-08-10 06:46:28 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:46:43 - INFO - 检测到 'Sign-up rejected'。准备回退并刷新。
2025-08-10 06:46:43 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-10 06:47:08 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62467,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:47:09 - INFO - <EMAIL>
2025-08-10 06:47:09 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:47:19 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:47:22 - INFO - 找到 Turnstile...
2025-08-10 06:47:24 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:47:24 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:47:32 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:47:32 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:47:32 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:47:32 - INFO - 检测到 'Verifying you are human...'（第4次），使用网络离线模式
2025-08-10 06:47:32 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:47:32 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:47:32 - INFO - 已执行拟人滚动: -52px
2025-08-10 06:47:38 - INFO - 已执行页面焦点操作
2025-08-10 06:47:39 - INFO - 已执行拟人操作完成，总停顿: 1.27秒
2025-08-10 06:47:39 - INFO - CDP: 已将网络设为离线，保持 9.1 秒……
2025-08-10 06:47:49 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:47:49 - INFO - 已执行拟人滚动: -122px
2025-08-10 06:47:50 - INFO - 已执行页面焦点操作
2025-08-10 06:47:51 - INFO - 已执行拟人操作完成，总停顿: 1.28秒
2025-08-10 06:47:51 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:47:51 - INFO - 成功！当前在订阅页面。
2025-08-10 06:47:51 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:47:51 - INFO - 代码信息: {
  "codeVerifier": "iMfHwm-r25f15VL5EIiDxuU3y5wd07gu38PaBQcFAT4",
  "code_challenge": "A7ats5ssSsElE_V5F-CcyE-IhF1-SZqVzLJS8AiOwHA",
  "state": "0e57bd85-059d-4fde-b8a5-eb8854579f57"
}
2025-08-10 06:47:51 - INFO - ==================================================
2025-08-10 06:47:51 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_0e5a26ff3084a0ba1bce419caaa28ff9&state=0e57bd85-059d-4fde-b8a5-eb8854579f57&tenant_url=https%3A%2F%2Fd17.api.augmentcode.com%2F
2025-08-10 06:47:53 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:47:53 - INFO - 添加session: <EMAIL>   6cfb0de3104c9813a4fdba4fba371da651abd16fe955497e405a968d86147416   https://d17.api.augmentcode.com/  2025-08-16T22:47:37Z
2025-08-10 06:47:53 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2D6s-22nHwTAu3WNKGVIGiM-u4S4sHjTL755gX9zEsZKtcVunXZuIE0lDw9-zoUhg6ggUu-c_3LOc79duOlz3EvuAx5elvnKSSvGFlF9DGNmhx5u-P1WsO-dEaT1spaoQQhOmmogUNzGHZT2dbykGSQyBuJZ2ONQzqFFOFHHodSuySSVK0MNLYYvWy9GH0rWKnRpcBoNHy-GohASw.aJfQGA._s_62d5g-pGvlDCdb-5DnrgR1-k
2025-08-10 06:47:53 - INFO - 
自动化流程成功完成！
2025-08-10 06:47:53 - INFO - 添加第3个
2025-08-10 06:48:09 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62588,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:48:10 - INFO - <EMAIL>
2025-08-10 06:48:10 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:48:20 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:48:23 - INFO - 找到 Turnstile...
2025-08-10 06:48:23 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:48:24 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:48:32 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:48:32 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:48:32 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:48:32 - INFO - 检测到 'Verifying you are human...'（第5次），使用网络离线模式
2025-08-10 06:48:32 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:48:32 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:48:32 - INFO - 已执行拟人滚动: -53px
2025-08-10 06:48:38 - INFO - 已执行页面焦点操作
2025-08-10 06:48:39 - INFO - 已执行拟人操作完成，总停顿: 0.99秒
2025-08-10 06:48:39 - INFO - CDP: 已将网络设为离线，保持 9.9 秒……
2025-08-10 06:48:50 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:48:50 - INFO - 已执行拟人滚动: -67px
2025-08-10 06:48:50 - INFO - 已执行页面焦点操作
2025-08-10 06:48:51 - INFO - 已执行拟人操作完成，总停顿: 1.08秒
2025-08-10 06:48:51 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:48:51 - INFO - 成功！当前在订阅页面。
2025-08-10 06:48:51 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:48:51 - INFO - 代码信息: {
  "codeVerifier": "5ibD4AETEvTgnz6jDdvRA4V0VNFBtMphjq_Kx_IIsfk",
  "code_challenge": "PIsId-tLYmFop0Sip_iUVdrYFniN4ECm-1foUIH37_w",
  "state": "d9635d54-dd1a-492c-ace3-992452a87a37"
}
2025-08-10 06:48:51 - INFO - ==================================================
2025-08-10 06:48:51 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_a48680dfedb49fa25c39049dfa7ffa3d&state=d9635d54-dd1a-492c-ace3-992452a87a37&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-10 06:48:53 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:48:53 - INFO - 添加session: <EMAIL>   10ae20da52699c96209d10bc8925d4ac43369160cd87218f905eb52191195b46   https://d6.api.augmentcode.com/  2025-08-16T22:48:37Z
2025-08-10 06:48:53 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgyAQRP9lz9ooLLB46p8YhKUhEWqsNjFt_73E9NDjTGbee8G48Jpd4bLBsK07NxBdTvMxFpcZBoAGbunJ5S-nsIz7g9cxhVpwdml-a7ImdJIYWQS0IU6IxH1X5-VefH32QloSUnYCJZHstJENnJiTUEnTkVzujUJjrCa8Kq0IzcXHAL_lKcTAXEmmjd70LWKMLWklWuVtNWBQXkv4fAEIp0AH.aJfQVA.AWbOTBvgEQOLxAIiCJbZAGVWdL0
2025-08-10 06:48:53 - INFO - 
自动化流程成功完成！
2025-08-10 06:48:53 - INFO - 添加第4个
2025-08-10 06:49:10 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62702,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:49:11 - INFO - <EMAIL>
2025-08-10 06:49:11 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:49:26 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:49:29 - INFO - 找到 Turnstile...
2025-08-10 06:49:30 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:49:31 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:49:31 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:49:31 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:49:31 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:49:40 - INFO - 检测到 'Verifying you are human...'（第6次），使用网络离线模式
2025-08-10 06:49:40 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:49:40 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:49:40 - INFO - 已执行拟人滚动: -107px
2025-08-10 06:49:40 - INFO - 已执行页面焦点操作
2025-08-10 06:49:40 - INFO - 已执行拟人操作完成，总停顿: 0.83秒
2025-08-10 06:49:46 - INFO - CDP: 已将网络设为离线，保持 9.0 秒……
2025-08-10 06:49:56 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:49:56 - INFO - 已执行拟人滚动: -91px
2025-08-10 06:49:57 - INFO - 已执行页面焦点操作
2025-08-10 06:49:57 - INFO - 已执行拟人操作完成，总停顿: 0.78秒
2025-08-10 06:49:57 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:49:57 - INFO - 成功！当前在订阅页面。
2025-08-10 06:49:57 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:49:57 - INFO - 代码信息: {
  "codeVerifier": "IIKkt6l9Ia013-T7gH_NZ-sE10MCCkNoCoZ5Da_g3Hs",
  "code_challenge": "loZ6i1LmDlsNMr2gXSjs6K7VkW52TSHGbuoBwaOF0uk",
  "state": "d4e9c103-78fd-4375-abd9-0f2aa348f9de"
}
2025-08-10 06:49:57 - INFO - ==================================================
2025-08-10 06:49:57 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_b9c9661beb05df6dc5b5317d67c974e8&state=d4e9c103-78fd-4375-abd9-0f2aa348f9de&tenant_url=https%3A%2F%2Fd19.api.augmentcode.com%2F
2025-08-10 06:49:59 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:49:59 - INFO - 添加session: <EMAIL>   021c92f455e4702d8a495db47e7570446730326e0371516b0b4e5e59a2e43a67   https://d19.api.augmentcode.com/  2025-08-16T22:49:45Z
2025-08-10 06:49:59 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtV1INSmKWeUmBiVRhYBYDpwPUrS9ew2jiy5n8ObNF8xX2xbt1u9wum8PG6Dq0i6fc9fF4AQwwEd7Wv-XW7nOj5ttcyt7YYu2y3eIwsWxGtp7QSk1oSRMsuN97XlfxsA4CRF5iWESh-QGODSHYTe9nnldPRMyCyOdKVBEfsu1wB95HGaZlCTlsXJ2I_qqo4bIozqfk0rwFCL8_AJWpkEJ.aJfQlg.a-KfdhRitj71bzZgJbIMuEnd12Y
2025-08-10 06:49:59 - INFO - 
自动化流程成功完成！
2025-08-10 06:49:59 - INFO - 添加第5个
2025-08-10 06:50:23 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62853,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:50:23 - INFO - <EMAIL>
2025-08-10 06:50:23 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:50:39 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:50:42 - INFO - 找到 Turnstile...
2025-08-10 06:50:44 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:50:44 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:50:44 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:50:44 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:50:44 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:50:52 - INFO - 检测到 'Verifying you are human...'（第7次），使用网络离线模式
2025-08-10 06:50:52 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:50:52 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:50:52 - INFO - 已执行拟人滚动: 126px
2025-08-10 06:50:54 - INFO - 已执行拟人操作完成，总停顿: 1.13秒
2025-08-10 06:51:00 - INFO - CDP: 已将网络设为离线，保持 8.7 秒……
2025-08-10 06:51:10 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:51:10 - INFO - 已执行拟人滚动: -22px
2025-08-10 06:51:11 - INFO - 已执行页面焦点操作
2025-08-10 06:51:11 - INFO - 已执行拟人操作完成，总停顿: 1.13秒
2025-08-10 06:51:11 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:51:11 - INFO - 成功！当前在订阅页面。
2025-08-10 06:51:11 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:51:11 - INFO - 代码信息: {
  "codeVerifier": "skQEC-wn8u9LfGyOZIf-6tWR3gaEipZRMX7Nj9cxKVw",
  "code_challenge": "aYAK1weKXZjVGX-hRmn350mz5yggPbSiimb-uZVG35Y",
  "state": "024162f5-3fb7-4e4f-bce1-16ba8f409467"
}
2025-08-10 06:51:11 - INFO - ==================================================
2025-08-10 06:51:12 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_0089eef17dcc1d276cb312bd0328cab3&state=024162f5-3fb7-4e4f-bce1-16ba8f409467&tenant_url=https%3A%2F%2Fd4.api.augmentcode.com%2F
2025-08-10 06:51:14 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:51:14 - INFO - 添加session: <EMAIL>   cf40bc2017790ef9d80a3cd9d7cf34d94a108c9bcadb690b86f4545985a2f7f4   https://d4.api.augmentcode.com/  2025-08-16T22:50:59Z
2025-08-10 06:51:14 - INFO - email:<EMAIL> === cookie:.eJxNjcsOgjAQRf9l1mBaOn2x8k_I0E61CVTCKzHqv0uIC5f35txzX9BNPI9UuKzQrvPGFSQa8_DsCo0MLUAFt7xz-cs5Tt228NzleBQ8Uh7exnkbRUBGbiL6mHpMpAIdeHmUcCy99EKjMI2y0qNEI0UFp-Y0HKayL3SXVqO13kl71UY7tJeQIvzI87B3waFwTU3GY40mhbpXJtSEKmg0TJ4SfL5j40E8.aJfQ4Q.u6ltBgMywG3flMG6eXdDB871e88
2025-08-10 06:51:14 - INFO - 
自动化流程成功完成！
2025-08-10 06:51:14 - INFO - 添加第6个
2025-08-10 06:51:37 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62996,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:51:38 - INFO - <EMAIL>
2025-08-10 06:51:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:51:48 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:51:51 - INFO - 找到 Turnstile...
2025-08-10 06:51:53 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:51:53 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:52:01 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:52:01 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:52:01 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:52:01 - INFO - 检测到 'Verifying you are human...'（第8次），使用网络离线模式
2025-08-10 06:52:01 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:52:01 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:52:01 - INFO - 已执行拟人滚动: -34px
2025-08-10 06:52:02 - INFO - 已执行拟人操作完成，总停顿: 0.96秒
2025-08-10 06:52:08 - INFO - CDP: 已将网络设为离线，保持 9.7 秒……
2025-08-10 06:52:19 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:52:19 - INFO - 已执行拟人滚动: -118px
2025-08-10 06:52:19 - INFO - 已执行页面焦点操作
2025-08-10 06:52:20 - INFO - 已执行拟人操作完成，总停顿: 1.08秒
2025-08-10 06:52:20 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:52:20 - INFO - 成功！当前在订阅页面。
2025-08-10 06:52:20 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:52:20 - INFO - 代码信息: {
  "codeVerifier": "AIbTCt-GHJsXvzC88tdTAV-0vdSXbqyyDmrnQ-KLuI0",
  "code_challenge": "69MTReUYi4XYmm5G3KGwrnc9TNnco69iFambJYcOpq4",
  "state": "f542b656-51f1-4141-bf1f-8fde5197f118"
}
2025-08-10 06:52:20 - INFO - ==================================================
2025-08-10 06:52:20 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_beb4ed756e8aa5aee1c79332d712a90b&state=f542b656-51f1-4141-bf1f-8fde5197f118&tenant_url=https%3A%2F%2Fd1.api.augmentcode.com%2F
2025-08-10 06:52:22 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:52:22 - INFO - 添加session: <EMAIL>   f4c835c94c7a3dcf9503bf071385a7d281fcc6c1b35747c5ed9865caf4d1a8fa   https://d1.api.augmentcode.com/  2025-08-16T22:52:07Z
2025-08-10 06:52:22 - INFO - email:<EMAIL> === cookie:.eJxNjcEKgzAQRP9lz1pM2GSTnvonIWZXCWgUq4XS9t8bpIfeZoaZNy8Iq2xzLFJ2uO7bIQ0Mcc7TM5Q4C1wBGhjzQ8qfz7yG4y5byFwDmWOe3tZ5YtV5QdGMnofeGJ2Mr_WylFSXrtPktNHUofVVITVwUk5ABRXOy6jIIJF3Xt2MNQ7pkgaGX_P8i4hoVLStdUm3GBW1PXNqyQ6uR01EkeDzBdIyP_w.aJfRJQ.DwoOI_jcZ03MbPFKFjNzbWFiq0Y
2025-08-10 06:52:22 - INFO - 
自动化流程成功完成！
2025-08-10 06:52:22 - INFO - 添加第7个
2025-08-10 06:52:47 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63135,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:52:48 - INFO - <EMAIL>
2025-08-10 06:52:48 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:52:58 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:53:01 - INFO - 找到 Turnstile...
2025-08-10 06:53:03 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:53:03 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:53:12 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:53:12 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:53:12 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:53:12 - INFO - 检测到 'Verifying you are human...'（第9次），使用网络离线模式
2025-08-10 06:53:12 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:53:12 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:53:12 - INFO - 已执行拟人滚动: -16px
2025-08-10 06:53:13 - INFO - 已执行拟人操作完成，总停顿: 1.05秒
2025-08-10 06:53:18 - INFO - CDP: 已将网络设为离线，保持 8.4 秒……
2025-08-10 06:53:27 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:53:27 - INFO - 已执行拟人滚动: -28px
2025-08-10 06:53:28 - INFO - 已执行拟人操作完成，总停顿: 1.17秒
2025-08-10 06:53:28 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:53:28 - INFO - 成功！当前在订阅页面。
2025-08-10 06:53:28 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:53:28 - INFO - 代码信息: {
  "codeVerifier": "5P5Rot4BByh8HGh10vdhynMgKhnOmggMLOqIfaqHwt4",
  "code_challenge": "J5vgD6ARG6_eezusEWOaW_vzBWg904f5UhAMSlmzwsQ",
  "state": "24245dec-a1d9-4923-91f2-140888342355"
}
2025-08-10 06:53:28 - INFO - ==================================================
2025-08-10 06:53:29 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_83ebdc2956e2dd4c42641a355b5d7769&state=24245dec-a1d9-4923-91f2-140888342355&tenant_url=https%3A%2F%2Fd19.api.augmentcode.com%2F
2025-08-10 06:53:30 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:53:30 - INFO - 添加session: <EMAIL>   c61cb06df3dca8c52667820dbd299b7416696d870ffbff815579ca7e297ec94c   https://d19.api.augmentcode.com/  2025-08-16T22:53:17Z
2025-08-10 06:53:30 - INFO - email:<EMAIL> === cookie:.eJxNjcEOwiAQRP9lz60RCrvQk39CoCyGpGBTW5NG_Xdr48HjTN68eYKbeC6-cl2gX-aVG0i-5HFz1ReGHqCBa35w_cs5Tm698-xy3AsuPo8vNJaiUMyKZVQ2pqC9TQZ3vN7qsC9RkCBjhDkjUSdQi66BQ3MYvqbA6yZIKyJrUV40aqPoNKQIP_I4lF4PgZFbyda3KtiuDTjI1keWWqkuMSK8P1XFQVE.aJfRag.0pGVc9eFXu5tBFSbjKiALxkP1-A
2025-08-10 06:53:30 - INFO - 
自动化流程成功完成！
2025-08-10 06:53:30 - INFO - 添加第8个
2025-08-10 06:53:55 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63264,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:53:55 - INFO - <EMAIL>
2025-08-10 06:53:55 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:54:05 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:54:08 - INFO - 找到 Turnstile...
2025-08-10 06:54:10 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:54:10 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:54:19 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:54:19 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:54:19 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:54:19 - INFO - 检测到 'Verifying you are human...'（第10次），使用网络离线模式
2025-08-10 06:54:19 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:54:19 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:54:19 - INFO - 已执行拟人滚动: -35px
2025-08-10 06:54:25 - INFO - 已执行页面焦点操作
2025-08-10 06:54:26 - INFO - 已执行拟人操作完成，总停顿: 0.97秒
2025-08-10 06:54:26 - INFO - CDP: 已将网络设为离线，保持 7.5 秒……
2025-08-10 06:54:35 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:54:35 - INFO - 已执行拟人滚动: 101px
2025-08-10 06:54:35 - INFO - 已执行页面焦点操作
2025-08-10 06:54:36 - INFO - 已执行拟人操作完成，总停顿: 1.22秒
2025-08-10 06:54:36 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:54:36 - INFO - 成功！当前在订阅页面。
2025-08-10 06:54:36 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:54:36 - INFO - 代码信息: {
  "codeVerifier": "cwFB4J66bpBwxJPRADHYz5cKBrxFjJNYxQXtSSky6Nc",
  "code_challenge": "LXYySBqVxL_zUzRRrw4NnL7F3tUfKLSjMFWBO3uLKGU",
  "state": "a6fa1e06-d2ff-4544-8e50-9baaeae8c225"
}
2025-08-10 06:54:36 - INFO - ==================================================
2025-08-10 06:54:36 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_3b6695c34901c13cf6d90f48ce3bff60&state=a6fa1e06-d2ff-4544-8e50-9baaeae8c225&tenant_url=https%3A%2F%2Fd12.api.augmentcode.com%2F
2025-08-10 06:54:38 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:54:38 - INFO - 添加session: <EMAIL>   052144fb372abee81d063ab9c373bb837764e53c9078247885dcfadd62e7197e   https://d12.api.augmentcode.com/  2025-08-16T22:54:24Z
2025-08-10 06:54:38 - INFO - email:<EMAIL> === cookie:.eJxNjUuKwzAQRO_Sa3vQr9VWVnMTI6lbRmBrgiYOhGTuHmOymGUVr149Yb5K32KTdoPLre8yQIlbXR9zi5vABWCApd6l_cuVr_P-K32ufBSyxbq-_BSIdTDixLALXBIWVpgPvP20fCw16cl5ZckgotXkgx3g1JyGwxSXvnRN6GhSyoRv9Dg5-sqF4UOeh5hMRI1h1Art6ArbMeWcRkVEoaBnTA7-3i7jQG8.aJfRrQ.ml-3heSYuQhSHTSshRPCzihuIDw
2025-08-10 06:54:38 - INFO - 
自动化流程成功完成！
2025-08-10 06:54:38 - INFO - 添加第9个
2025-08-10 06:55:01 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63424,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:55:01 - INFO - <EMAIL>
2025-08-10 06:55:01 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:55:17 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:55:20 - INFO - 找到 Turnstile...
2025-08-10 06:55:22 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:55:22 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:55:22 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:55:22 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:55:22 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:55:30 - INFO - 检测到 'Verifying you are human...'（第11次），使用网络离线模式
2025-08-10 06:55:30 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:55:30 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:55:30 - INFO - 已执行拟人滚动: 131px
2025-08-10 06:55:31 - INFO - 已执行拟人操作完成，总停顿: 0.99秒
2025-08-10 06:55:37 - INFO - CDP: 已将网络设为离线，保持 8.0 秒……
2025-08-10 06:55:46 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:55:46 - INFO - 已执行拟人滚动: 114px
2025-08-10 06:55:47 - INFO - 已执行页面焦点操作
2025-08-10 06:55:48 - INFO - 已执行拟人操作完成，总停顿: 1.49秒
2025-08-10 06:55:48 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:55:48 - INFO - 成功！当前在订阅页面。
2025-08-10 06:55:48 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:55:48 - INFO - 代码信息: {
  "codeVerifier": "Vr9wKnaCK1S1g5FQFV3NynqfUB9ZiOfq3qd62B92ol0",
  "code_challenge": "-RlDr3z4FThpRDaOg6wL0hBdi6ooutmLh9wOga2LCq8",
  "state": "0efd0fc5-2a47-485b-808c-972da1d05833"
}
2025-08-10 06:55:48 - INFO - ==================================================
2025-08-10 06:55:48 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_a8f0fc74d32308323f077d55c422a2a1&state=0efd0fc5-2a47-485b-808c-972da1d05833&tenant_url=https%3A%2F%2Fd15.api.augmentcode.com%2F
2025-08-10 06:55:50 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 06:55:50 - INFO - 添加session: <EMAIL>   2eff60fde0a26e01a42a7637701edeaef9263be347b0ee40e6bd944e9ed44d97   https://d15.api.augmentcode.com/  2025-08-16T22:55:36Z
2025-08-10 06:55:50 - INFO - email:<EMAIL> === cookie:.eJxNjcEKgzAQRP9lz1pisnETT_0Tie6mBEwqVgvS9t8r0kOPM7x584J-liWHImWFbl02qSCGnKa9LyELdAAV3NJTyl9OPPfbQ5Y-8VFIDml6t84TNxwERTN6jkNrKTg58HIv47E05BskT9YZqxUaxApOyyk4RCXuIzdkkZxS3lxtax3SZYwMP_L8a1nEeRlqMWhqjFrVgzWhHpREto4waIbPFyv-QTg.aJfR9Q.STkVjbjLG8BiCHqDTmkeY_JvP-I
2025-08-10 06:55:50 - INFO - 
自动化流程成功完成！
2025-08-10 06:55:50 - INFO - 添加第10个
2025-08-10 06:56:14 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63629,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:56:15 - INFO - <EMAIL>
2025-08-10 06:56:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:56:25 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:56:28 - INFO - 找到 Turnstile...
2025-08-10 06:56:30 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:56:30 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:56:30 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:56:30 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:56:30 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:56:39 - INFO - 检测到 'Verifying you are human...'（第12次），使用网络离线模式
2025-08-10 06:56:39 - INFO - 检测到人机验证，执行 CDP 网络离线 8 秒
2025-08-10 06:56:39 - INFO - 执行网络阻塞前的拟人操作...
2025-08-10 06:56:39 - INFO - 已执行拟人滚动: 127px
2025-08-10 06:56:40 - INFO - 已执行拟人操作完成，总停顿: 1.22秒
2025-08-10 06:56:45 - INFO - CDP: 已将网络设为离线，保持 9.3 秒……
2025-08-10 06:56:55 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 06:56:55 - INFO - 已执行拟人滚动: 140px
2025-08-10 06:56:56 - INFO - 已执行拟人操作完成，总停顿: 0.88秒
2025-08-10 06:56:56 - INFO - CDP: 网络已恢复为在线状态。
2025-08-10 06:56:56 - INFO - 成功！当前在订阅页面。
2025-08-10 06:56:56 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 06:56:56 - INFO - 代码信息: {
  "codeVerifier": "W88sMoFO8XXNdf_mWipf9cs8IIXr1f1LL3HY028ANZI",
  "code_challenge": "KreoNxmxcbSmGgDDwbGlQJW0tVl7Yx_9-wKdBYCBCjc",
  "state": "ec38709c-5c1e-43f6-b638-de31670be50c"
}
2025-08-10 06:56:56 - INFO - ==================================================
2025-08-10 06:56:57 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_61678d02048ebb2a9cc1df5d3b02c1ee&state=ec38709c-5c1e-43f6-b638-de31670be50c&tenant_url=https%3A%2F%2Fd11.api.augmentcode.com%2F
2025-08-10 06:56:57 - INFO - 
主流程中发生严重错误: 'WebDriver' object has no attribute 'decode'
2025-08-10 06:56:57 - INFO - 准备重启流程...
2025-08-10 06:57:29 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63787,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 06:57:30 - INFO - <EMAIL>
2025-08-10 06:57:30 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 06:57:45 - INFO - 获取 cap_value 验证码成功...
2025-08-10 06:57:48 - INFO - 找到 Turnstile...
2025-08-10 06:57:50 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 06:57:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 06:57:52 - INFO - 验证码已提交，等待跳转...
2025-08-10 06:57:52 - INFO - 
第 1/5 次尝试注册...
2025-08-10 06:57:52 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 06:57:58 - INFO - Traceback (most recent call last):

2025-08-10 06:57:58 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-10 06:57:58 - INFO - [1;35mConnectionResetError[0m: [35m[WinError 10054] 远程主机强迫关闭了一个现有的连接。[0m

2025-08-10 06:57:58 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-10 06:57:58 - INFO - Traceback (most recent call last):

2025-08-10 06:57:58 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m965[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m933[0m, in [35mmain[0m
    result = attempt_signup_with_retry(driver)

2025-08-10 06:57:58 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m751[0m, in [35mattempt_signup_with_retry[0m
    [31mWebDriverWait(driver, 2).until[0m[1;31m([0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mEC.presence_of_element_located((By.XPATH, "//*[contains(., 'Verifying you are human')]"))[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\support\wait.py"[0m, line [35m129[0m, in [35muntil[0m
    value = method(self._driver)

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\support\expected_conditions.py"[0m, line [35m104[0m, in [35m_predicate[0m
    return [31mdriver.find_element[0m[1;31m(*locator)[0m
           [31m~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m922[0m, in [35mfind_element[0m
    return [31mself.execute[0m[1;31m(Command.FIND_ELEMENT, {"using": by, "value": value})[0m["value"]
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m451[0m, in [35mexecute[0m
    response = cast(RemoteConnection, self.command_executor).execute(driver_command, params)

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m404[0m, in [35mexecute[0m
    return [31mself._request[0m[1;31m(command_info[0], url, body=data)[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m428[0m, in [35m_request[0m
    response = self._conn.request(method, url, body=body, headers=headers, timeout=self._client_config.timeout)

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m143[0m, in [35mrequest[0m
    return [31mself.request_encode_body[0m[1;31m([0m
           [31m~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mmethod, url, fields=fields, headers=headers, **urlopen_kw[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m278[0m, in [35mrequest_encode_body[0m
    return [31mself.urlopen[0m[1;31m(method, url, **extra_kw)[0m
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\poolmanager.py"[0m, line [35m459[0m, in [35murlopen[0m
    response = conn.urlopen(method, u.request_uri, **kw)

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-10 06:57:58 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1432[0m, in [35mgetresponse[0m
    [31mself.close[0m[1;31m()[0m
    [31m~~~~~~~~~~[0m[1;31m^^[0m

2025-08-10 06:57:58 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m369[0m, in [35mclose[0m
    def close(self) -> None:
    

2025-08-10 06:57:58 - INFO - [1;35mKeyboardInterrupt[0m

