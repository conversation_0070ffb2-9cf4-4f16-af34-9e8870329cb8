2025-08-14 09:43:29 - INFO - 测试 HAR 和 WebSocket 功能...
2025-08-14 09:43:29 - INFO - HAR 文件已保存: har_files\test_har_20250814_094329.har
2025-08-14 09:43:29 - INFO - 捕获了 1 个网络请求
2025-08-14 09:43:29 - INFO - 捕获了 1 个 WebSocket 连接
2025-08-14 09:43:29 - INFO -   WebSocket: wss://app.augmentcode.com/ws - 4 个消息帧
2025-08-14 09:43:29 - INFO - WebSocket 摘要已保存: websocket_logs\test_websocket_20250814_094329.json
2025-08-14 09:43:29 - INFO - HAR 和 WebSocket 功能测试成功！
2025-08-14 09:43:29 - INFO - HAR 文件: har_files\test_har_20250814_094329.har
2025-08-14 09:43:29 - INFO - WebSocket 摘要: websocket_logs\test_websocket_20250814_094329.json
2025-08-14 09:43:29 - INFO - WebSocket 测试统计: {'total_connections': 1, 'active_connections': 0, 'closed_connections': 1, 'total_frames': 4, 'sent_frames': 2, 'received_frames': 2}
2025-08-14 09:43:29 - INFO - HAR 和 WebSocket 功能测试完成
2025-08-14 09:43:29 - INFO - 当前网络日志数量: 1
2025-08-14 09:43:29 - INFO - 当前 WebSocket 日志数量: 1
2025-08-14 09:43:29 - INFO - ==================================================
2025-08-14 09:43:33 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53326,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:43:34 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 09:43:34 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 09:43:34 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 09:43:34 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 09:43:34 - INFO - 记录了 34 个页面资源
2025-08-14 09:43:34 - INFO - 页面支持 WebSocket
2025-08-14 09:43:34 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 09:43:34 - INFO - <EMAIL>
2025-08-14 09:43:34 - INFO - 网络捕获: 主函数开始
2025-08-14 09:43:34 - INFO - 记录了 34 个页面资源
2025-08-14 09:43:34 - INFO - 页面支持 WebSocket
2025-08-14 09:43:34 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 09:43:43 - INFO - 获取 cap_value 验证码成功...
2025-08-14 09:43:47 - INFO - 找到 Turnstile...
2025-08-14 09:43:49 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 09:43:52 - INFO - 网络捕获: 登录完成后
2025-08-14 09:43:52 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBvQ0Z3dWFMT0N4QmRLcThDSEtKbDRLTGF6VVpHT1Z3aqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIF92am9FMklXRC1zWlZDNTlwc1pEN2F4elNNSFRWb3Fmo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 09:43:52 - INFO - 记录了 21 个页面资源
2025-08-14 09:43:52 - INFO - 页面支持 WebSocket
2025-08-14 09:43:52 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 09:43:52 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 09:43:52 - INFO - CDP:启用节流模式
2025-08-14 09:44:06 - INFO - 验证码已提交，等待跳转...
2025-08-14 09:44:06 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 09:44:06 - INFO - 记录页面访问: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=878eQgPQJLY74DhQMa22ARKQ1ij-pLX2AJy4pvNoY1s&code_challenge=7fbU37LwGXY9SudvWkIjWiCWWl0mpfpRrODq3dLmsZE&code_challenge_method=S256 (标题: Augment Verification)
2025-08-14 09:44:06 - INFO - 记录了 19 个页面资源
2025-08-14 09:44:06 - INFO - 页面支持 WebSocket
2025-08-14 09:44:06 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 09:44:06 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 09:44:06 - INFO - ReCAPTCHA Token: 
2025-08-14 09:44:06 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 09:44:17 - INFO - 执行鼠标操作时发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459dea]
	(No symbol) [0x0x7ff678445f15]
	(No symbol) [0x0x7ff67846abf4]
	(No symbol) [0x0x7ff6784dfa85]
	(No symbol) [0x0x7ff6784fff72]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:44:17 - INFO - 网络捕获: 人类验证过程中
2025-08-14 09:44:17 - INFO - CDP 网络捕获失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff6784a055f]
	(No symbol) [0x0x7ff6784d8332]
	(No symbol) [0x0x7ff6784d2e53]
	(No symbol) [0x0x7ff6784d1f19]
	(No symbol) [0x0x7ff678424b05]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff678423b00]
	GetHandleVerifier [0x0x7ff678a984c8+4357608]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:44:17 - INFO - WebSocket 检测失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff6784a055f]
	(No symbol) [0x0x7ff6784d8332]
	(No symbol) [0x0x7ff6784d2e53]
	(No symbol) [0x0x7ff6784d1f19]
	(No symbol) [0x0x7ff678424b05]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff678423b00]
	GetHandleVerifier [0x0x7ff678a984c8+4357608]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:44:17 - INFO - AJAX 检测失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff6784a055f]
	(No symbol) [0x0x7ff6784d8332]
	(No symbol) [0x0x7ff6784d2e53]
	(No symbol) [0x0x7ff6784d1f19]
	(No symbol) [0x0x7ff678424b05]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff678423b00]
	GetHandleVerifier [0x0x7ff678a984c8+4357608]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:44:17 - INFO - 
第 1/5 次尝试注册...
2025-08-14 09:44:17 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 09:44:17 - INFO - 在尝试注册时发生未知错误: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff6784a055f]
	(No symbol) [0x0x7ff6784d8332]
	(No symbol) [0x0x7ff6784d2e53]
	(No symbol) [0x0x7ff6784d1f19]
	(No symbol) [0x0x7ff678424b05]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff678423b00]
	GetHandleVerifier [0x0x7ff678a984c8+4357608]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:44:17 - ERROR - 在尝试注册时发生未知错误: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff6784a055f]
	(No symbol) [0x0x7ff6784d8332]
	(No symbol) [0x0x7ff6784d2e53]
	(No symbol) [0x0x7ff6784d1f19]
	(No symbol) [0x0x7ff678424b05]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff678423b00]
	GetHandleVerifier [0x0x7ff678a984c8+4357608]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 09:44:17 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 09:44:17 - INFO - HAR 文件已保存: har_files\error_edykq1755135809_smartdocker.online_20250814_094417.har
2025-08-14 09:44:17 - INFO - 捕获了 69 个网络请求
2025-08-14 09:44:17 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 09:44:17 - INFO - WebSocket 摘要已保存: websocket_logs\error_edykq1755135809_smartdocker.online_20250814_094417_ws.json
2025-08-14 09:44:19 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53450,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
