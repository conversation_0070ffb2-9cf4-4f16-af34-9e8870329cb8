2025-08-14 08:34:26 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64480,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 08:34:27 - INFO - <EMAIL>
2025-08-14 08:34:27 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 08:34:39 - INFO - 获取 cap_value 验证码成功...
2025-08-14 08:34:43 - INFO - 找到 Turnstile...
2025-08-14 08:34:45 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 08:34:49 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 08:34:49 - INFO - CDP:启用节流模式
2025-08-14 08:35:02 - INFO - 验证码已提交，等待跳转...
2025-08-14 08:35:02 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 08:35:02 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 08:35:02 - INFO - ReCAPTCHA Token: 
2025-08-14 08:35:02 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 08:35:26 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 08:35:26 - INFO - 
第 1/5 次尝试注册...
2025-08-14 08:35:26 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 08:35:26 - INFO - 成功！当前在订阅页面。
2025-08-14 08:35:26 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-14 08:35:26 - INFO - 未检测到验证页面，跳过操作。
2025-08-14 08:35:29 - INFO - 代码信息: {
  "codeVerifier": "I-wOqsxKg4ezzri4SfxYtvxy0bQKD8gyX70dmG27Bug",
  "code_challenge": "CVw34tWIgeSICpdCzpLiu1f92dZvGqMYlICmlsdPf_Y",
  "state": "ef5ccab8-0bca-4477-bc7d-63fabb4b5d15"
}
2025-08-14 08:35:29 - INFO - ==================================================
2025-08-14 08:35:30 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_5700acd69dde849c713b0b498a8bb2d1&state=ef5ccab8-0bca-4477-bc7d-63fabb4b5d15&tenant_url=https%3A%2F%2Fd18.api.augmentcode.com%2F
2025-08-14 08:35:32 - INFO - 添加session成功: {'status': 'success'}
2025-08-14 08:35:32 - INFO - 添加session: <EMAIL>   d0988eef8d3e0f94fe286afe69801bf101dd402ec850fe885412f7d1a876af0b   https://d18.api.augmentcode.com/  2025-08-21T00:35:05Z
2025-08-14 08:35:32 - INFO - email:<EMAIL> === cookie:.eJxNzU0SgjAMBeC7ZA0OoSltXXkTJtDgdKTFKeD4e3fRjS7z5r0vD2jPkiMnSQvsl7xKAQPHMN7axFFgD1DAMVwk_d3Bn9t1ltwGvwUSOYzPxjpfDzWRQm-HWhnPLBZlq6cp9dvSocHKOqNVg1oRGmcK-DJfYZPu8326otEaFTYNHebIefFTf5K8m9IY0kf7Pe64w44NluS1L4m1KZn7qnTOVo6EqK8dvN7lG0WE.aJ0vUw.T5BsgKwkeJzxAF_dpM-hRIAzsJs
2025-08-14 08:35:32 - INFO - 
自动化流程成功完成！
2025-08-14 08:35:32 - INFO - 添加第1个
2025-08-14 08:35:55 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64650,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 08:35:56 - INFO - <EMAIL>
2025-08-14 08:35:56 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 08:36:10 - INFO - 获取 cap_value 验证码成功...
2025-08-14 08:36:13 - INFO - 找到 Turnstile...
2025-08-14 08:36:16 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 08:36:19 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 08:36:19 - INFO - CDP:启用节流模式
2025-08-14 08:36:32 - INFO - 验证码已提交，等待跳转...
2025-08-14 08:36:32 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 08:36:32 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 08:36:32 - INFO - ReCAPTCHA Token: 
2025-08-14 08:36:32 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 08:36:45 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff678676e75+26517]
	(No symbol) [0x0x7ff6785d0780]
	(No symbol) [0x0x7ff678459c1c]
	(No symbol) [0x0x7ff67850b9be]
	(No symbol) [0x0x7ff6784d846a]
	(No symbol) [0x0x7ff67850065c]
	(No symbol) [0x0x7ff6784d8243]
	(No symbol) [0x0x7ff6784a1431]
	(No symbol) [0x0x7ff6784a21c3]
	GetHandleVerifier [0x0x7ff678a084ad+3767757]
	GetHandleVerifier [0x0x7ff678a2bb03+3912739]
	GetHandleVerifier [0x0x7ff678a2009d+3865021]
	GetHandleVerifier [0x0x7ff67875827e+949150]
	(No symbol) [0x0x7ff6785dc59f]
	(No symbol) [0x0x7ff6785d7f54]
	(No symbol) [0x0x7ff6785d8109]
	(No symbol) [0x0x7ff6785c6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-14 08:36:45 - INFO - 
第 1/5 次尝试注册...
2025-08-14 08:36:45 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-14 08:36:48 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-14 08:37:18 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-14 08:37:21 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64773,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 08:37:22 - INFO - <EMAIL>
2025-08-14 08:37:22 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 08:37:38 - INFO - 获取 cap_value 验证码成功...
2025-08-14 08:37:42 - INFO - 找到 Turnstile...
2025-08-14 08:37:44 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 08:37:47 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 08:37:47 - INFO - CDP:启用节流模式
2025-08-14 08:37:47 - INFO - 验证码已提交，等待跳转...
2025-08-14 08:38:00 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 08:38:00 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 08:38:00 - INFO - ReCAPTCHA Token: 
2025-08-14 08:38:00 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 08:38:02 - INFO - Traceback (most recent call last):

2025-08-14 08:38:02 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1244[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-14 08:38:02 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1213[0m, in [35mmain[0m
    [31mbypass_human_verification[0m[1;31m(driver, duration=30)[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 08:38:02 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m746[0m, in [35mbypass_human_verification[0m
    [31mtime.sleep[0m[1;31m(random.uniform(0.1, 0.5))[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 08:38:02 - INFO - [1;35mKeyboardInterrupt[0m

