2025-08-19 10:28:53 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61818,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:28:53 - INFO - 127.0.0.1:61818
2025-08-19 10:28:54 - INFO - <EMAIL>
2025-08-19 10:28:54 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:29:34 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:29:38 - INFO - 找到 Turnstile...
2025-08-19 10:29:38 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:29:42 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:30:00 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:30:00 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:30:17 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:30:17 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:30:17 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:30:17 - INFO - 成功！当前在订阅页面。
2025-08-19 10:30:17 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-19 10:30:17 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-19 10:30:17 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:30:20 - INFO - 代码信息: {
  "codeVerifier": "lGMKq9J5qAqGGwAsk3tYbRTrORd4a-_f1oR0xaEKOPY",
  "code_challenge": "8ER0_OY02xx4MwFUsyGDZuW8Z_man2ZG8ze0PoJ23Ak",
  "state": "4ca61aa4-c1bd-41ad-878c-4742bc1ad799"
}
2025-08-19 10:30:20 - INFO - ==================================================
2025-08-19 10:30:21 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_a204fa68b5d87e812498e1c62921450d&state=4ca61aa4-c1bd-41ad-878c-4742bc1ad799&tenant_url=https%3A%2F%2Fd4.api.augmentcode.com%2F
2025-08-19 10:30:23 - INFO - 添加session成功: {'status': 'success'}
2025-08-19 10:30:23 - INFO - 添加session: <EMAIL>   f546470fa8133bd751ed1f0e5f7dcc529fa6846aa161ed9a92479f7ffeb81e72   https://d4.api.augmentcode.com/  2025-08-26T02:30:04Z
2025-08-19 10:30:23 - INFO - email:<EMAIL> === cookie:.eJxNjUtuhDAQRO_Sa4j8of2Z1dwE2XQ7soINMjDSzCR3D0JZZFmleq_eMK7cSqhcd7jt7eAOUih5fo41FIYbQAef-cH1X860jsfGbcx0FlxCnr-NC5qlN4OW5JLSlpAHac05r0udTlKh80qhF8JKrYz0Aju4NJfhNOV1eZG0iGgFKn_fSmg7LdMXt4-lzrky_BHXsWEdCa3rkxBTP0SR-ogUeylRTdH5mMjAzy-G1EUo.aKPhvw.4ChjhqHVJCyNqU94FG_j9lH570s
2025-08-19 10:30:23 - INFO - 
自动化流程成功完成！
2025-08-19 10:30:23 - INFO - 添加第1个
2025-08-19 10:30:41 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:61993,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:30:41 - INFO - 127.0.0.1:61993
2025-08-19 10:30:41 - INFO - <EMAIL>
2025-08-19 10:30:41 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:31:25 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:31:30 - INFO - 找到 Turnstile...
2025-08-19 10:31:30 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:31:33 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:31:33 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:31:40 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:31:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:31:45 - INFO -    等待 4.98 秒...
2025-08-19 10:31:54 - INFO - 刷新完成，继续下一次尝试。
2025-08-19 10:31:54 - INFO - 
第 2/5 次尝试注册...
2025-08-19 10:31:54 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:31:54 - INFO - 成功！当前在订阅页面。
2025-08-19 10:31:54 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-19 10:31:54 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-19 10:32:04 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-19 10:32:07 - INFO - 代码信息: {
  "codeVerifier": "PkoniOH-vhg6__ATtfZkrMqFcxfFgyiXyLDfdZFcUd4",
  "code_challenge": "xDOJfUgOY8HYa8hglssamo02OiWR1ogWwNgRaxq0udA",
  "state": "710dd6a1-6886-4205-9b92-7e041382947f"
}
2025-08-19 10:32:07 - INFO - ==================================================
2025-08-19 10:32:09 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_52e648f5f8398f579058709faca1bc5c&state=710dd6a1-6886-4205-9b92-7e041382947f&tenant_url=https%3A%2F%2Fd2.api.augmentcode.com%2F
2025-08-19 10:32:11 - INFO - 添加session成功: {'status': 'success'}
2025-08-19 10:32:11 - INFO - 添加session: <EMAIL>   0ee7a6910d230bfa7413079f31bbd284c1916938634e2bb72ecb61d5357a3fbf   https://d2.api.augmentcode.com/  2025-08-26T02:31:49Z
2025-08-19 10:32:11 - INFO - email:<EMAIL> === cookie:.eJxNjUFuwzAMBP_Csx1IIinROeUnhhLRhVBLNhSnaJD07zWMHnrcxc7sC8ZVW4lV6wbnrT20gymWPD_HGovCGaCDj_yl9V_OaR0fd21jTnuhJeb57SWiOkOENsnkMCQvgxfc53Wpt51kMhaRBkdmYBLjHXdwaA7Dblr0e77awMzBeAyXe4ltS8vtU9tpqXOuCn_EcRwkGCHLvRAPPbHaXpCvvbpI6GwKXgh-fgFChkMj.aKPiKg.rHlhQ5W-zpj6JCU_CXJstQmlt1E
2025-08-19 10:32:11 - INFO - 
自动化流程成功完成！
2025-08-19 10:32:11 - INFO - 添加第2个
2025-08-19 10:32:31 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62241,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:32:31 - INFO - 127.0.0.1:62241
2025-08-19 10:32:31 - INFO - <EMAIL>
2025-08-19 10:32:31 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:33:29 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:33:33 - INFO - 找到 Turnstile...
2025-08-19 10:33:33 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:33:36 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:33:37 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:34:02 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:34:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:34:21 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:34:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:34:21 - INFO - 成功！当前在订阅页面。
2025-08-19 10:34:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-19 10:34:21 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-19 10:34:32 - INFO - 鼠标左键按住并随机拖动操作完成。
2025-08-19 10:34:35 - INFO - 代码信息: {
  "codeVerifier": "HjaZg0RT4FEnWekFLn4iXe5O0hXbqk__aWAP0-ONF9M",
  "code_challenge": "S28YLOamHXJNOEMS-Cfr7desyJgTHg9JLuKFRLIDy-E",
  "state": "e83dd327-c3d1-4ad6-b153-433dab9905b4"
}
2025-08-19 10:34:35 - INFO - ==================================================
2025-08-19 10:34:36 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_bc9b97039c91828b463f86e1a7638d26&state=e83dd327-c3d1-4ad6-b153-433dab9905b4&tenant_url=https%3A%2F%2Fd6.api.augmentcode.com%2F
2025-08-19 10:34:37 - INFO - 添加session成功: {'status': 'success'}
2025-08-19 10:34:37 - INFO - 添加session: <EMAIL>   ef84d9127bec18d62c8e2d973a7f525e77a6c52b0337896234ed0569c7460c92   https://d6.api.augmentcode.com/  2025-08-26T02:34:08Z
2025-08-19 10:34:37 - INFO - email:<EMAIL> === cookie:.eJxNjc0OgjAQhN9lz2CgLd2Wk29CFrrVKi2moIl_7y5BDx5nMt83T-gunCMlTgu0S75yAZ5iGO9dosjQAhRwCDdOfzm4S3edOXfBrQVHCuNLG5IsTKVk7YwXEh2qinqzztOUhpVEZbVFYaTSaCUKWesCNs1mWE3z6XHkGpumwQoV7udIeXHTcOa8m9IYEsOP-B6zHdCRLD1LVaraV2VvvS9pENo53dtG9_D-ALMbRdc.aKPivQ._rQxO1g8418Kvw_3o35KiQoarUA
2025-08-19 10:34:37 - INFO - 
自动化流程成功完成！
2025-08-19 10:34:37 - INFO - 添加第3个
2025-08-19 10:34:58 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62448,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:34:58 - INFO - 127.0.0.1:62448
2025-08-19 10:34:58 - INFO - <EMAIL>
2025-08-19 10:34:58 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:35:47 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:35:51 - INFO - 找到 Turnstile...
2025-08-19 10:35:53 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:35:56 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:36:25 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:36:25 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:36:27 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:36:27 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:36:27 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:36:39 - INFO -    等待 4.27 秒...
2025-08-19 10:36:47 - INFO - 刷新完成，继续下一次尝试。
2025-08-19 10:36:47 - INFO - 
第 2/5 次尝试注册...
2025-08-19 10:36:47 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:36:47 - INFO - 成功！当前在订阅页面。
2025-08-19 10:36:47 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-19 10:36:47 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-19 10:36:47 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:36:50 - INFO - 代码信息: {
  "codeVerifier": "LnK6Ww6FNZK1Kpd0TyUKPgE83FI2rgSjodTdlo8VW-0",
  "code_challenge": "APpuJCvIURvL2PMwrECuQQ5oguXalpoBMNWf4MC53jM",
  "state": "59d566d7-aceb-44a5-9458-2a51585df085"
}
2025-08-19 10:36:50 - INFO - ==================================================
2025-08-19 10:36:52 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_df7c714ba19eef4612ea99263b194e8c&state=59d566d7-aceb-44a5-9458-2a51585df085&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
2025-08-19 10:36:54 - INFO - 添加session成功: {'status': 'success'}
2025-08-19 10:36:54 - INFO - 添加session: <EMAIL>   bae6abd1779169b58580a552f00738faecc3bc00da4379dedd41757323d15750   https://d13.api.augmentcode.com/  2025-08-26T02:36:31Z
2025-08-19 10:36:54 - INFO - email:<EMAIL> === cookie:.eJxNjUsOwjAMRO_idYvifJqEFTeprNpFEU2K0oLE7-5UFQuWM5r35gX9VWqmImWF41pv0sBIOU2PvlAWOAI0cE53KX858bW_LVL7xFshmdL07gIZMYqsQQ6jNp6DCh2qbV7mMmykdVqZGFFF67UPaDw2sGt2w2aqtT4f6J1zXoVoT0umuvI8XKQe5jKlIvAj9mM3UrCeXcs6YGtZYxsjudZ6JI2apaMBPl9_30SJ.aKPjRQ.qXEmIx4i0yKnf0CKBLMlNoyaA9A
2025-08-19 10:36:54 - INFO - 
自动化流程成功完成！
2025-08-19 10:36:54 - INFO - 添加第4个
2025-08-19 10:37:14 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62634,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:37:14 - INFO - 127.0.0.1:62634
2025-08-19 10:37:14 - INFO - <EMAIL>
2025-08-19 10:37:14 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:37:47 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:37:51 - INFO - 找到 Turnstile...
2025-08-19 10:37:55 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:37:58 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:37:58 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:38:42 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:38:51 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:38:51 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:38:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:38:54 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-19 10:38:54 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-19 10:38:57 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:62894,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:38:57 - INFO - 127.0.0.1:62894
2025-08-19 10:38:58 - INFO - <EMAIL>
2025-08-19 10:38:58 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:39:42 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:39:46 - INFO - 找到 Turnstile...
2025-08-19 10:39:46 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:39:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:39:56 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:40:04 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:40:06 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:40:06 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:40:06 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:40:40 - INFO - 未检测到 'Sign-up rejected'。
2025-08-19 10:40:40 - INFO - 步骤 8: 检查当前 URL 是否为订阅页面...
2025-08-19 10:40:40 - INFO - 成功！已跳转到订阅页面。
2025-08-19 10:40:40 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-19 10:40:40 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-19 10:40:40 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:40:43 - INFO - 代码信息: {
  "codeVerifier": "A1Sf1GWUvi7O6x1YOgBHzmJ8rdbO4H7TBZROpsnBozA",
  "code_challenge": "Qd0RObWdD2mf2jDBaMIHe17-GkCFXrzUjCUQcI4ODN8",
  "state": "fa25f671-11da-4c1c-98c3-ff84bf211820"
}
2025-08-19 10:40:43 - INFO - ==================================================
2025-08-19 10:40:44 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_3fc1c3ba4b3fe3e74f2e5c200c08e996&state=fa25f671-11da-4c1c-98c3-ff84bf211820&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-19 10:40:46 - INFO - 添加session成功: {'status': 'success'}
2025-08-19 10:40:46 - INFO - 添加session: <EMAIL>   7d0a993095ced75813455cc5a29ea95426950a8502ee760d36ddb27abe462ce1   https://d9.api.augmentcode.com/  2025-08-26T02:40:09Z
2025-08-19 10:40:46 - INFO - email:<EMAIL> === cookie:.eJxNjc1ugzAQhN9lz1B5Wf8sOfVN0II3yCo2kQONoibvXoR66HFG833zA8NNa5aiZYPLVndt4Co5Lc-hSFa4ADQwp28t_3KKt2G_ax1SPArNkpaXZyEl7ixh5GtHIfLoLeMxL2uZDpIcIiEa610wnpF8aODUnIbDtOfHrBiccwENmc97lrrFdfrS-rGWJRWFP-I8lt46xyit4aitNYxtL9G2I42dCzTRaD28fwFslEQi.aKPkLQ.FYUOjpoOxGPrp8wWAehY5rr3sl4
2025-08-19 10:40:46 - INFO - 
自动化流程成功完成！
2025-08-19 10:40:46 - INFO - 添加第5个
2025-08-19 10:41:09 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63176,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:41:09 - INFO - 127.0.0.1:63176
2025-08-19 10:41:09 - INFO - <EMAIL>
2025-08-19 10:41:09 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:41:41 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:41:41 - INFO - 准备重启流程...
2025-08-19 10:41:48 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63231,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:41:48 - INFO - 127.0.0.1:63231
2025-08-19 10:41:49 - INFO - <EMAIL>
2025-08-19 10:41:49 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:42:19 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:42:19 - INFO - 准备重启流程...
2025-08-19 10:42:26 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63282,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:42:26 - INFO - 127.0.0.1:63282
2025-08-19 10:42:27 - INFO - <EMAIL>
2025-08-19 10:42:27 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:42:57 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:42:57 - INFO - 准备重启流程...
2025-08-19 10:43:04 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63334,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:43:04 - INFO - 127.0.0.1:63334
2025-08-19 10:43:05 - INFO - <EMAIL>
2025-08-19 10:43:05 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:43:42 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:43:42 - INFO - 准备重启流程...
2025-08-19 10:43:50 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63380,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:43:50 - INFO - 127.0.0.1:63380
2025-08-19 10:43:51 - INFO - <EMAIL>
2025-08-19 10:43:51 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:44:13 - INFO - 
主流程中发生严重错误: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479dea]
	(No symbol) [0x0x7ff608476ba5]
	(No symbol) [0x0x7ff608467879]
	(No symbol) [0x0x7ff608469631]
	(No symbol) [0x0x7ff608467b96]
	(No symbol) [0x0x7ff6084675f6]
	(No symbol) [0x0x7ff6084672ba]
	(No symbol) [0x0x7ff608464e8b]
	(No symbol) [0x0x7ff60846570c]
	(No symbol) [0x0x7ff60847dd9a]
	(No symbol) [0x0x7ff60852143e]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:44:13 - INFO - 准备重启流程...
2025-08-19 10:44:16 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63425,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:44:16 - INFO - 127.0.0.1:63425
2025-08-19 10:44:16 - INFO - <EMAIL>
2025-08-19 10:44:16 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:44:41 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:44:44 - INFO - 找到 Turnstile...
2025-08-19 10:45:05 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:48:52 - INFO - 
主流程中发生严重错误: HTTPSConnectionPool(host='es.slogo.eu.org', port=443): Max retries exceeded with url: /api/mails?limit=10&offset=0 (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))
2025-08-19 10:48:52 - INFO - 准备重启流程...
2025-08-19 10:48:56 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63631,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:48:56 - INFO - 127.0.0.1:63631
2025-08-19 10:48:57 - INFO - <EMAIL>
2025-08-19 10:48:57 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:49:14 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:49:18 - INFO - 找到 Turnstile...
2025-08-19 10:49:20 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:49:24 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:49:24 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:49:24 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:49:24 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:49:31 - INFO -    等待 4.28 秒...
2025-08-19 10:49:40 - INFO - 刷新完成，继续下一次尝试。
2025-08-19 10:49:40 - INFO - 
第 2/5 次尝试注册...
2025-08-19 10:49:40 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:49:43 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-19 10:49:43 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-19 10:49:46 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63779,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:49:46 - INFO - 127.0.0.1:63779
2025-08-19 10:49:47 - INFO - <EMAIL>
2025-08-19 10:49:47 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:50:04 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:50:08 - INFO - 找到 Turnstile...
2025-08-19 10:50:10 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:50:14 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:50:19 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:50:20 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:50:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:50:21 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:50:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:50:25 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-19 10:50:25 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-19 10:50:28 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63919,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:50:28 - INFO - 127.0.0.1:63919
2025-08-19 10:50:29 - INFO - <EMAIL>
2025-08-19 10:50:29 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:50:41 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:50:45 - INFO - 找到 Turnstile...
2025-08-19 10:50:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:50:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:50:51 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:50:56 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:51:09 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:51:09 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:51:09 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:51:12 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-19 10:51:12 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-19 10:51:15 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64056,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:51:15 - INFO - 127.0.0.1:64056
2025-08-19 10:51:15 - INFO - <EMAIL>
2025-08-19 10:51:15 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:51:31 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:51:35 - INFO - 找到 Turnstile...
2025-08-19 10:51:37 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:51:41 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:51:46 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:51:46 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:51:51 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:51:51 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:51:51 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:51:54 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-19 10:51:54 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-19 10:51:57 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64178,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:51:57 - INFO - 127.0.0.1:64178
2025-08-19 10:51:58 - INFO - <EMAIL>
2025-08-19 10:51:58 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:52:08 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:52:12 - INFO - 找到 Turnstile...
2025-08-19 10:52:15 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:52:18 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:52:18 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:52:25 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:52:27 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:52:27 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:52:27 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:52:31 - INFO - 检测到 'Sign-up rejected'。等待30秒后重试。
2025-08-19 10:52:31 - INFO - 
检测到严重错误，将关闭浏览器并重新开始整个流程...
2025-08-19 10:52:34 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64352,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:52:34 - INFO - 127.0.0.1:64352
2025-08-19 10:52:35 - INFO - <EMAIL>
2025-08-19 10:52:35 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:52:53 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:52:57 - INFO - 找到 Turnstile...
2025-08-19 10:53:38 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:53:41 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:54:02 - INFO - 错误：验证页面加载超时或找不到验证元素。
2025-08-19 10:54:02 - INFO - 
主流程中发生严重错误: 邮箱验证步骤失败
2025-08-19 10:54:02 - INFO - 准备重启流程...
2025-08-19 10:54:06 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:64994,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:54:06 - INFO - 127.0.0.1:64994
2025-08-19 10:54:06 - INFO - <EMAIL>
2025-08-19 10:54:06 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:55:43 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:55:47 - INFO - 找到 Turnstile...
2025-08-19 10:55:50 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:55:53 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:55:53 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:56:02 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:56:21 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:56:21 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:56:21 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:56:21 - INFO - 成功！当前在订阅页面。
2025-08-19 10:56:21 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-19 10:56:21 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-19 10:56:21 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:56:24 - INFO - 代码信息: {
  "codeVerifier": "VRy0GE9bCqxhlqgBHBvhPyJkcEUpNkG_fuG1iBXckJM",
  "code_challenge": "zD-FHra9leF3cb5ImKpmsJiLOQIf_J21qSJysh_6CrU",
  "state": "20556ba0-2973-4909-ab0a-89c80fce8a34"
}
2025-08-19 10:56:24 - INFO - ==================================================
2025-08-19 10:56:25 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_ff9428fa870df5c665231df7fd7bc81e&state=20556ba0-2973-4909-ab0a-89c80fce8a34&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F
2025-08-19 10:56:27 - INFO - 添加session成功: {'status': 'success'}
2025-08-19 10:56:27 - INFO - 添加session: <EMAIL>   81dc145c937fd40e938f4d37eb856327759e4d93e26a68b41e06fb74c19b614e   https://d10.api.augmentcode.com/  2025-08-26T02:56:06Z
2025-08-19 10:56:27 - INFO - email:<EMAIL> === cookie:.eJxNjUtOxDAQRO_S6wT5k3bbs-ImkbE7yCLujJwMYpjh7lgRC5ZVqvfqAfOVW43CcsDlaDceYIm1rPdZYmW4AAzwXj5Z_uWSr_Nt5zaX3AuusaxP56NlZ_VkdfaLsZSTV0i2z2WT1EnSFEjpSQUKyqDCyQ1wak5DN91l__7ShIikjVOve43tyFv64PayyVqE4Y84jx2mhCbhmFXgcUrGjd7T29hha5acgosafn4BhLJEkg.aKPn2w.2V6u6QSSM0JglUeWpsSxV8pRFgk
2025-08-19 10:56:27 - INFO - 
自动化流程成功完成！
2025-08-19 10:56:27 - INFO - 添加第6个
2025-08-19 10:56:43 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:65296,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-19 10:56:43 - INFO - 127.0.0.1:65296
2025-08-19 10:56:43 - INFO - <EMAIL>
2025-08-19 10:56:43 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-19 10:58:12 - INFO - 获取验证码失败，重试次数: 1
2025-08-19 10:58:22 - INFO - 获取 cap_value 验证码成功...
2025-08-19 10:58:26 - INFO - 找到 Turnstile...
2025-08-19 10:58:30 - INFO - 登录信息已提交，等待验证页面...
2025-08-19 10:58:33 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-19 10:59:00 - INFO - 验证码已提交，等待跳转...
2025-08-19 10:59:00 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-19 10:59:00 - INFO - 执行鼠标操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:59:00 - INFO - 
第 1/5 次尝试注册...
2025-08-19 10:59:00 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:59:11 - INFO -    等待 3.55 秒...
2025-08-19 10:59:19 - INFO - 刷新完成，继续下一次尝试。
2025-08-19 10:59:19 - INFO - 
第 2/5 次尝试注册...
2025-08-19 10:59:19 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-19 10:59:19 - INFO - 成功！当前在订阅页面。
2025-08-19 10:59:19 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-19 10:59:19 - INFO - 检测到人类验证页面，开始执行鼠标左键按住并随机拖动 10 秒...
2025-08-19 10:59:20 - INFO - 执行鼠标拖动操作时发生错误: Message: move target out of bounds
  (Session info: chrome=138.0.7204.97)
Stacktrace:
	GetHandleVerifier [0x0x7ff608696e75+26517]
	(No symbol) [0x0x7ff6085f0780]
	(No symbol) [0x0x7ff608479c1c]
	(No symbol) [0x0x7ff60852b9be]
	(No symbol) [0x0x7ff6084f846a]
	(No symbol) [0x0x7ff60852065c]
	(No symbol) [0x0x7ff6084f8243]
	(No symbol) [0x0x7ff6084c1431]
	(No symbol) [0x0x7ff6084c21c3]
	GetHandleVerifier [0x0x7ff608a284ad+3767757]
	GetHandleVerifier [0x0x7ff608a4bb03+3912739]
	GetHandleVerifier [0x0x7ff608a4009d+3865021]
	GetHandleVerifier [0x0x7ff60877827e+949150]
	(No symbol) [0x0x7ff6085fc59f]
	(No symbol) [0x0x7ff6085f7f54]
	(No symbol) [0x0x7ff6085f8109]
	(No symbol) [0x0x7ff6085e6c68]
	BaseThreadInitThunk [0x0x7ffe477be8d7+23]
	RtlUserThreadStart [0x0x7ffe492bc34c+44]

2025-08-19 10:59:23 - INFO - 代码信息: {
  "codeVerifier": "QnEc5VaJ1cBhd8PVH4low0HWAbNjK1r-C2SdtmgBCfs",
  "code_challenge": "xM7g8PgHH7NGbuAz8NyQ8SYkmCstpRDTWgcyv2_EpUc",
  "state": "0993cf32-8d4c-447d-bfb6-7e3e97347126"
}
2025-08-19 10:59:23 - INFO - ==================================================
2025-08-19 10:59:24 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_92c73c27760996130cc735d331d94fa4&state=0993cf32-8d4c-447d-bfb6-7e3e97347126&tenant_url=https%3A%2F%2Fd4.api.augmentcode.com%2F
2025-08-19 10:59:26 - INFO - 添加session成功: {'status': 'success'}
2025-08-19 10:59:26 - INFO - 添加session: <EMAIL>   a7b15bd533ff5d513a44247f4a81d75e6132566c3b3469e2419fa4f44517008a   https://d4.api.augmentcode.com/  2025-08-26T02:59:04Z
2025-08-19 10:59:26 - INFO - email:<EMAIL> === cookie:.eJxNjUtuwzAMRO_CtR2Y1o_OKjcxGJMqhEpyIDsBgrZ3r2F00eUM5r35gvmhrXDVusN1b0_tIHJJ-T1XLgpXgA4-0kvrv5zkMT83bXOSo9DCKX97YqPknDUoFEcTJAZV9ce8rnU5yMkSWmcMEaINxjjq4LScgkPE-ZUjBudcGHGi21a47bIun9oua82pKvwR568gD-LHoUeK2Fu5a88sY--jD1a9TsEH-PkFmSlFNA.aKPojQ.uVSTP9Py3H3lrK6rpXcoUfEhZFs
2025-08-19 10:59:26 - INFO - 
自动化流程成功完成！
2025-08-19 10:59:26 - INFO - 添加第7个
2025-08-19 11:00:00 - INFO - 浏览器打开失败: {'code': 101, 'msg': 'timeout of 15000ms exceeded', 'data': None}
