{"log": {"version": "1.2", "creator": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0"}, "entries": [{"startedDateTime": "2025-08-14T11:28:38.389358Z", "time": 0, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "132235"}], "cookies": [], "content": {"size": 132235, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 132235}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "工作台"}, {"startedDateTime": "2025-08-14T11:28:38.392921Z", "time": 4.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DWNmc_MY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21479, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21479}, "cache": {}, "timings": {"send": 0, "wait": 4.299999997019768, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:38.392929Z", "time": 4.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-BKOrM9G3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 146848, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 146848}, "cache": {}, "timings": {"send": 0, "wait": 4.600000008940697, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392932Z", "time": 11.299999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DjWl3JGw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 786361, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 786361}, "cache": {}, "timings": {"send": 0, "wait": 11.299999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392935Z", "time": 5.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-B9PrHQl7.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4414, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4414}, "cache": {}, "timings": {"send": 0, "wait": 5.799999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392938Z", "time": 6.199999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CIEDqDGY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 468, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 468}, "cache": {}, "timings": {"send": 0, "wait": 6.199999988079071, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392940Z", "time": 6.899999991059303, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-H8ttwM1S.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 474, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 474}, "cache": {}, "timings": {"send": 0, "wait": 6.899999991059303, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392943Z", "time": 9.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BN5KR6Qs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5129, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5129}, "cache": {}, "timings": {"send": 0, "wait": 9.799999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392946Z", "time": 9.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtKobjF2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14336, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14336}, "cache": {}, "timings": {"send": 0, "wait": 9.799999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392949Z", "time": 12, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-R6XwfvJN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3351, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3351}, "cache": {}, "timings": {"send": 0, "wait": 12, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392952Z", "time": 12.400000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-Bd2HX4Pl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5330, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5330}, "cache": {}, "timings": {"send": 0, "wait": 12.400000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392955Z", "time": 12.900000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DeQidb8Q.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6357, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6357}, "cache": {}, "timings": {"send": 0, "wait": 12.900000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392958Z", "time": 13.*************36, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-kZT51Bed.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2762, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2762}, "cache": {}, "timings": {"send": 0, "wait": 13.*************36, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392961Z", "time": 11.799999997019768, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-BRMOCcEH.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 130305, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 130305}, "cache": {}, "timings": {"send": 0, "wait": 11.799999997019768, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392965Z", "time": 6.*************32, "request": {"method": "GET", "url": "http://127.0.0.1:45535/svg-icon-C_thiKp4.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3318, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 3318}, "cache": {}, "timings": {"send": 0, "wait": 6.*************32, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:28:38.392968Z", "time": 6.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-DwV2T02E.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 50384, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 50384}, "cache": {}, "timings": {"send": 0, "wait": 6.600000008940697, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:28:38.392971Z", "time": 8.800000011920929, "request": {"method": "GET", "url": "http://127.0.0.1:45535/alert-ntgrtH_u.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1564, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 1564}, "cache": {}, "timings": {"send": 0, "wait": 8.800000011920929, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:28:38.392975Z", "time": 8.900000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/index-CtkFttLu.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 42774, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 42774}, "cache": {}, "timings": {"send": 0, "wait": 8.900000005960464, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:28:38.392978Z", "time": 9.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/dashboard-DDWJuWk8.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 9043, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 9043}, "cache": {}, "timings": {"send": 0, "wait": 9.600000008940697, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:28:38.392982Z", "time": 13.400000005960464, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_user_key-BQXKMoNP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2168, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2168}, "cache": {}, "timings": {"send": 0, "wait": 13.400000005960464, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.392986Z", "time": 1.4000000059604645, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/app/config", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 408, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 408}, "cache": {}, "timings": {"send": 0, "wait": 1.4000000059604645, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:28:38.392990Z", "time": 1.7999999970197678, "request": {"method": "GET", "url": "http://127.0.0.1:45535/zh-CN-DTI-qNBp.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94467, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 94467}, "cache": {}, "timings": {"send": 0, "wait": 1.7999999970197678, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:38.392994Z", "time": 2.***********02322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/list?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6465, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 6465}, "cache": {}, "timings": {"send": 0, "wait": 2.***********02322, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:28:38.392998Z", "time": 2.8999999910593033, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/proxy/detection?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 654, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 654}, "cache": {}, "timings": {"send": 0, "wait": 2.8999999910593033, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:28:38.393002Z", "time": 3.2999999970197678, "request": {"method": "GET", "url": "http://127.0.0.1:45535/api/browser/core-info?dirId=1c42949de80fb5f609dc1fcee8fddde3", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 406, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 406}, "cache": {}, "timings": {"send": 0, "wait": 3.2999999970197678, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:28:38.393006Z", "time": 2.6000000089406967, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_spin-Dat2nEi2.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 822, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 822}, "cache": {}, "timings": {"send": 0, "wait": 2.6000000089406967, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:38.393010Z", "time": 3.199999988079071, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob1-CCAm7wau.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1143, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1143}, "cache": {}, "timings": {"send": 0, "wait": 3.199999988079071, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:38.393014Z", "time": 3.3999999910593033, "request": {"method": "GET", "url": "http://127.0.0.1:45535/blob2-CEoTd5f3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1157, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1157}, "cache": {}, "timings": {"send": 0, "wait": 3.3999999910593033, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:38.393019Z", "time": 25, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/JetBrains%20Mono/JetBrainsMono-Bold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 278128, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 278128}, "cache": {}, "timings": {"send": 0, "wait": 25, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:28:38.393023Z", "time": 25.599999994039536, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Regular.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 407356, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 407356}, "cache": {}, "timings": {"send": 0, "wait": 25.599999994039536, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:28:38.393027Z", "time": 25.600000008940697, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-Medium.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 411800, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 411800}, "cache": {}, "timings": {"send": 0, "wait": 25.600000008940697, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:28:38.393032Z", "time": 25.**************, "request": {"method": "GET", "url": "http://127.0.0.1:45535/fonts/Inter/Inter-SemiBold.ttf", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 414276, "mimeType": "font/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 414276}, "cache": {}, "timings": {"send": 0, "wait": 25.**************, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:28:38.393036Z", "time": 5.5999999940395355, "request": {"method": "GET", "url": "http://127.0.0.1:45535/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 285778, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 285778}, "cache": {}, "timings": {"send": 0, "wait": 5.5999999940395355, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:28:38.393041Z", "time": 2.*************322, "request": {"method": "GET", "url": "http://127.0.0.1:45535/US-BXoNwYaC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5807, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5807}, "cache": {}, "timings": {"send": 0, "wait": 2.*************322, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:38.393045Z", "time": 2.6000000089406967, "request": {"method": "GET", "url": "http://127.0.0.1:45535/rx_ic_speed_test-2BHwaLbY.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2258, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2258}, "cache": {}, "timings": {"send": 0, "wait": 2.6000000089406967, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.789087Z", "time": 0, "request": {"method": "GET", "url": "https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "50088"}], "cookies": [], "content": {"size": 50088, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 50088}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": "Augment Code"}, {"startedDateTime": "2025-08-14T11:28:55.793369Z", "time": 0, "request": {"method": "GET", "url": "https://cdn.auth0.com/ulp/react-components/1.146.2/css/main.cdn.min.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:28:55.793386Z", "time": 0, "request": {"method": "GET", "url": "https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793396Z", "time": 192.59999999403954, "request": {"method": "GET", "url": "https://www.augmentcode.com/android-chrome-512x512.png", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "image/*"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 192.59999999403954, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:28:55.793405Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793413Z", "time": 0.9000000059604645, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/v1/projects/ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg/settings", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0.9000000059604645, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:28:55.793423Z", "time": 190.1000000089407, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=login.augmentcode.com&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge&scrsrc=www.googletagmanager.com&frm=0&rnd=499284027.1755142134&dt=Augment%20Code&auid=**********.**********&navt=n&npa=0&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=1755142133642&tfd=830&apve=1&apvf=f", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 190.1000000089407, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:28:55.793431Z", "time": 202.**************, "request": {"method": "GET", "url": "https://googleads.g.doubleclick.net/pagead/viewthroughconversion/***********/?random=1755142133640&cv=11&fst=1755142133640&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=**********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&fledge=1&data=event%3Dgtag.config&rfmt=3&fmt=4", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2519, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2519}, "cache": {}, "timings": {"send": 0, "wait": 202.**************, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793440Z", "time": 206.30000001192093, "request": {"method": "GET", "url": "https://td.doubleclick.net/td/rul/***********?random=1755142133640&cv=11&fst=1755142133640&fmt=3&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=**********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&fledge=1&data=event%3Dgtag.config", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1298, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 1298}, "cache": {}, "timings": {"send": 0, "wait": 206.30000001192093, "receive": 0}, "resourceType": "iframe"}, {"startedDateTime": "2025-08-14T11:28:55.793448Z", "time": 205.59999999403954, "request": {"method": "GET", "url": "https://analytics.google.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=1755142133610&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=*********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dr=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&dt=Augment%20Code&en=page_view&_ee=1&tfd=840", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 205.59999999403954, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:28:55.793457Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/ajs-destination.bundle.8e6b895db75187c55313.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793465Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/analytics-next/bundles/schemaFilter.bundle.1b218d13fed021531d4e.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793474Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793482Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793491Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/actions/845/431110629a9fe8297174.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793500Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793509Z", "time": 0, "request": {"method": "GET", "url": "https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793518Z", "time": 0, "request": {"method": "GET", "url": "https://us.i.posthog.com/static/array.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793528Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_GIfRcxAmWBSIxniAKUhlM9pQ343wGGnAgC3iBMO1FSN/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:28:55.793537Z", "time": 241.59999999403954, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755142133688&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 241.59999999403954, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:28:55.793546Z", "time": 199.59999999403954, "request": {"method": "GET", "url": "https://www.google.com/pagead/1p-user-list/***********/?random=1755142133640&cv=11&fst=1755140400000&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je58c0v9191852910za200zd9191852910xec&gcd=13l3l3l3l1l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBXa1FQTnZ3ZlZuVHFQeGItOHFrSHY0cGtzei1ibjZUZaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIG80SmJFaTA4b3dLVmNtRU0zNnlPM3RBYzNGNTV1cHo5o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&hn=www.googleadservices.com&frm=0&tiba=Augment%20Code&npa=0&pscdl=noapi&auid=**********.**********&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&fledge=1&data=event%3Dgtag.config&rfmt=3&fmt=3&is_vtc=1&cid=CAQSigEA2abss8PZJE4hZuxD6OIhtVXV9YjQm5r5WuYc88Vr23iyLuaT703CrJuBjdDcC3W_EWI9QtLVis7upecV5X5jqYEkg2-Holn_mmdcpm7Q44_DqZ-WneptrniEKz5XZVqERNMmEHo_uNqgeMu06bTB_6lLnx-qSSzxndy6XliUQvPhV4gffFbBxmU&random=1712544533&rmt_tld=0&ipr=y", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 342, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 342}, "cache": {}, "timings": {"send": 0, "wait": 199.59999999403954, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:28:55.793556Z", "time": 0, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:29:22.909753Z", "time": 0, "request": {"method": "GET", "url": "https://app.augmentcode.com/account/subscription", "httpVersion": "HTTP/1.1", "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "text/html; charset=utf-8"}, {"name": "Content-Length", "value": "38986"}], "cookies": [], "content": {"size": 38986, "mimeType": "text/html", "text": ""}, "redirectURL": "", "headersSize": -1, "bodySize": 38986}, "cache": {}, "timings": {"send": 0, "wait": 0, "receive": 0}, "pageTitle": ""}, {"startedDateTime": "2025-08-14T11:29:22.914671Z", "time": 6826.***********, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-C-JJqdMW.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 94361, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 94361}, "cache": {}, "timings": {"send": 0, "wait": 6826.***********, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:29:22.914685Z", "time": 381.*************3, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-D6aQ-Xs1.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 416, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 416}, "cache": {}, "timings": {"send": 0, "wait": 381.*************3, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:29:22.914691Z", "time": 2524.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/tailwind-DxnphuB3.css", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5101, "mimeType": "text/css"}, "redirectURL": "", "headersSize": -1, "bodySize": 5101}, "cache": {}, "timings": {"send": 0, "wait": 2524.5, "receive": 0}, "resourceType": "link"}, {"startedDateTime": "2025-08-14T11:29:22.914697Z", "time": 2544.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/manifest-46feb8ab.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4124, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4124}, "cache": {}, "timings": {"send": 0, "wait": 2544.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914703Z", "time": 1819.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/entry.client-C92V1BwZ.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1792, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1792}, "cache": {}, "timings": {"send": 0, "wait": 1819.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914709Z", "time": 3117, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B7Ui2t93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5343, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5343}, "cache": {}, "timings": {"send": 0, "wait": 3117, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914716Z", "time": 4538.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/components--HLsvfrm.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13680, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13680}, "cache": {}, "timings": {"send": 0, "wait": 4538.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914722Z", "time": 5204.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BS38kjqr.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 21235, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 21235}, "cache": {}, "timings": {"send": 0, "wait": 5204.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914729Z", "time": 5930.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-Bi4s4-Io.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 46139, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 46139}, "cache": {}, "timings": {"send": 0, "wait": 5930.29999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914735Z", "time": 356.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/QueryClientProvider-CeGnmbe-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 696, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 696}, "cache": {}, "timings": {"send": 0, "wait": 356.**************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914741Z", "time": 1972.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/client-only-C74SDDMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2093, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 2093}, "cache": {}, "timings": {"send": 0, "wait": 1972.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914748Z", "time": 2667.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryClient.client-Dk3lS3wN.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3850, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3850}, "cache": {}, "timings": {"send": 0, "wait": 2667.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914754Z", "time": 4723.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index.modern-950P1XoK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 14011, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 14011}, "cache": {}, "timings": {"send": 0, "wait": 4723.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914761Z", "time": 4770.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/theme-C1ulz75E.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 13876, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 13876}, "cache": {}, "timings": {"send": 0, "wait": 4770.29999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:22.914767Z", "time": 513.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/container-BlJCmUTg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 884, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 884}, "cache": {}, "timings": {"send": 0, "wait": 513.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934028Z", "time": 529.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/card-BBgKeY7L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 865, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 865}, "cache": {}, "timings": {"send": 0, "wait": 529.1000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934056Z", "time": 560.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/link-CMt6MnuB.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 998, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 998}, "cache": {}, "timings": {"send": 0, "wait": 560.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934069Z", "time": 606.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/button-Dvrjyl3p.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 608, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 608}, "cache": {}, "timings": {"send": 0, "wait": 606.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934081Z", "time": 2746.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/flex-C9XhsxSj.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3855, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3855}, "cache": {}, "timings": {"send": 0, "wait": 2746.1000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934097Z", "time": 654.6999999880791, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DrFu-skq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1110, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1110}, "cache": {}, "timings": {"send": 0, "wait": 654.6999999880791, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934109Z", "time": 3239.8999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Toast-CG_NC-6_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 5695, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 5695}, "cache": {}, "timings": {"send": 0, "wait": 3239.8999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934121Z", "time": 669.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-C8U1uDHl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1132, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1132}, "cache": {}, "timings": {"send": 0, "wait": 669.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934133Z", "time": 685.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/jotaiStore.client-sdvKmlSn.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 430, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 430}, "cache": {}, "timings": {"send": 0, "wait": 685.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934145Z", "time": 716.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-BkoMXhYo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 462, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 462}, "cache": {}, "timings": {"send": 0, "wait": 716.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934156Z", "time": 780.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/spinner-Cq6egsy4.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1179, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1179}, "cache": {}, "timings": {"send": 0, "wait": 780.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934168Z", "time": 811.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAyM6kBC.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 766, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 766}, "cache": {}, "timings": {"send": 0, "wait": 811.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934180Z", "time": 827.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/get-subtree-8AxxbxX_.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 598, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 598}, "cache": {}, "timings": {"send": 0, "wait": 827.1000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934192Z", "time": 2139.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CI4icoal.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1970, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1970}, "cache": {}, "timings": {"send": 0, "wait": 2139.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934213Z", "time": 2154.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-CAuWbg93.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1857, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1857}, "cache": {}, "timings": {"send": 0, "wait": 2154.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934225Z", "time": 857.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-B5mzPb5P.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 935, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 935}, "cache": {}, "timings": {"send": 0, "wait": 857.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934237Z", "time": 872.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/base-button-Dk95TXPu.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1152, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1152}, "cache": {}, "timings": {"send": 0, "wait": 872.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934249Z", "time": 964.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/useQuery-BvSAfNQo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1398, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1398}, "cache": {}, "timings": {"send": 0, "wait": 964.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934261Z", "time": 2837.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/animations-CMbVnQEg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3734, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3734}, "cache": {}, "timings": {"send": 0, "wait": 2837.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934273Z", "time": 3287.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/root-Cup3efAs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6206, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6206}, "cache": {}, "timings": {"send": 0, "wait": 3287.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934289Z", "time": 3966.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/react-icons.esm-g3l3pVh3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8914, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8914}, "cache": {}, "timings": {"send": 0, "wait": 3966.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934303Z", "time": 1041.8999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/BaseHeader-y1wz0aO3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1603, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1603}, "cache": {}, "timings": {"send": 0, "wait": 1041.8999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934316Z", "time": 1087.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/guards-C20ItfmI.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 963, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 963}, "cache": {}, "timings": {"send": 0, "wait": 1087.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934328Z", "time": 2280, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/string-CwzBSc0v.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3135, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 3135}, "cache": {}, "timings": {"send": 0, "wait": 2280, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934340Z", "time": 3981.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/style-Bv9a6v44.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 8859, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 8859}, "cache": {}, "timings": {"send": 0, "wait": 3981.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934354Z", "time": 4907.899999991059, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/user-d_3utlAo.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 16299, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 16299}, "cache": {}, "timings": {"send": 0, "wait": 4907.899999991059, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934368Z", "time": 1135.4000000059605, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plans-D8s3V0en.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 510, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 510}, "cache": {}, "timings": {"send": 0, "wait": 1135.4000000059605, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934381Z", "time": 1103.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/subscription-creation-pending-Mylp5-_d.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1103.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934394Z", "time": 1150, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/feature-flags.client-BVZhVN7G.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 431, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 431}, "cache": {}, "timings": {"send": 0, "wait": 1150, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934407Z", "time": 1166.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/skeleton-qwMe81ym.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 880, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 880}, "cache": {}, "timings": {"send": 0, "wait": 1166.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934420Z", "time": 1213.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/box-DvlTT8Qh.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 824, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 824}, "cache": {}, "timings": {"send": 0, "wait": 1213.1000000089407, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934434Z", "time": 1244.300000011921, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-D-DXDI2l.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 575, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 575}, "cache": {}, "timings": {"send": 0, "wait": 1244.300000011921, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934449Z", "time": 1260, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/queryOptions-Yjo86aMs.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 721, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 721}, "cache": {}, "timings": {"send": 0, "wait": 1260, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934462Z", "time": 1292.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/toDate-qOSwr3PX.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 615, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 615}, "cache": {}, "timings": {"send": 0, "wait": 1292.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934476Z", "time": 1278.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/addLeadingZeros-6--iqVZy.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 456, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 456}, "cache": {}, "timings": {"send": 0, "wait": 1278.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934489Z", "time": 4414.29999999702, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/proto3-Bmo7MjaP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 10903, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 10903}, "cache": {}, "timings": {"send": 0, "wait": 4414.29999999702, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934503Z", "time": 1403.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/heading-Duq80h8F.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 991, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 991}, "cache": {}, "timings": {"send": 0, "wait": 1403.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934518Z", "time": 2359.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/ProgressPage-CfNQ5HtT.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1971, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1971}, "cache": {}, "timings": {"send": 0, "wait": 2359.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934533Z", "time": 3689.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout-s5dvxSMq.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 7715, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 7715}, "cache": {}, "timings": {"send": 0, "wait": 3689.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934546Z", "time": 1372.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account--DlvNh9L.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1172, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1172}, "cache": {}, "timings": {"send": 0, "wait": 1372.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934560Z", "time": 1418.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/MaterialIcon-BneX_s9R.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 745, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 745}, "cache": {}, "timings": {"send": 0, "wait": 1418.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934574Z", "time": 1494.8999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/number-BS8GKe3y.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1719, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1719}, "cache": {}, "timings": {"send": 0, "wait": 1494.8999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934588Z", "time": 2929.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/PlanPicker-CZi5-vIO.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 4367, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 4367}, "cache": {}, "timings": {"send": 0, "wait": 2929.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934605Z", "time": 3704.5999999940395, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Card-BR5rB2rc.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6435, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6435}, "cache": {}, "timings": {"send": 0, "wait": 3704.5999999940395, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934619Z", "time": 1510.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/plural-D9YAiM4O.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1040, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1040}, "cache": {}, "timings": {"send": 0, "wait": 1510.***********02, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934634Z", "time": 1526.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/icons--M48DCb3.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1161, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1161}, "cache": {}, "timings": {"send": 0, "wait": 1526.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934648Z", "time": 1557.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Enabled-BoZ8Au2f.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 860, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 860}, "cache": {}, "timings": {"send": 0, "wait": 1557.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934665Z", "time": 2421, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-BEyGE8AK.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 1912, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 1912}, "cache": {}, "timings": {"send": 0, "wait": 2421, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934681Z", "time": 1603.3999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constants-C5gnWpVx.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 387, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 387}, "cache": {}, "timings": {"send": 0, "wait": 1603.3999999910593, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934697Z", "time": 1618.2999999970198, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/url-_DgIuZOw.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 635, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 635}, "cache": {}, "timings": {"send": 0, "wait": 1618.2999999970198, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934712Z", "time": 3735.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/Badge-CCBfROU-.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6905, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6905}, "cache": {}, "timings": {"send": 0, "wait": 3735.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934727Z", "time": 1664.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/isBefore-DuJnhAXP.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 446, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 446}, "cache": {}, "timings": {"send": 0, "wait": 1664.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934742Z", "time": 1695.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/badge-CrInsKkE.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 986, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 986}, "cache": {}, "timings": {"send": 0, "wait": 1695.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934758Z", "time": 3750.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/index-DzvzAwJl.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 6526, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 6526}, "cache": {}, "timings": {"send": 0, "wait": 3750.*************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934773Z", "time": 1741.************, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/constructFrom-DWjd9ymD.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 441, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 441}, "cache": {}, "timings": {"send": 0, "wait": 1741.************, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934790Z", "time": 4988.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/assets/_layout.account.subscription-BBRL8heg.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 17282, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 17282}, "cache": {}, "timings": {"send": 0, "wait": 4988.5, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934805Z", "time": 1924.5, "request": {"method": "GET", "url": "https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400..700,0..1,0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 705, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 705}, "cache": {}, "timings": {"send": 0, "wait": 1924.5, "receive": 0}, "resourceType": "css"}, {"startedDateTime": "2025-08-14T11:29:24.934839Z", "time": 384.***********023, "request": {"method": "GET", "url": "https://app.augmentcode.com/favicon.ico", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 2118, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 2118}, "cache": {}, "timings": {"send": 0, "wait": 384.***********023, "receive": 0}, "resourceType": "other"}, {"startedDateTime": "2025-08-14T11:29:24.934855Z", "time": 492.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/user", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 483, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 483}, "cache": {}, "timings": {"send": 0, "wait": 492.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.934872Z", "time": 461.1000000089407, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/team/plan-change-pending", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 339, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 339}, "cache": {}, "timings": {"send": 0, "wait": 461.1000000089407, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.934888Z", "time": 1217.***********02, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/subscription", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 798, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 798}, "cache": {}, "timings": {"send": 0, "wait": 1217.***********02, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.934906Z", "time": 615.*************, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/plans", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 943, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 943}, "cache": {}, "timings": {"send": 0, "wait": 615.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.934922Z", "time": 436.**************, "request": {"method": "GET", "url": "https://app.augmentcode.com/augment-logo.svg", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 3975, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 3975}, "cache": {}, "timings": {"send": 0, "wait": 436.**************, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:29:24.934977Z", "time": 612.*************, "request": {"method": "GET", "url": "https://www.google.com/ccm/collect?tid=AW-***********&en=page_view&dr=auth.augmentcode.com&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&scrsrc=www.googletagmanager.com&frm=0&rnd=*********.**********&auid=**********.**********&navt=n&npa=1&_tu=AAg&gtm=45je58c0v9191852910za200zd9191852910&gcd=13l3l3l3l3l1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&tft=*************&tfd=10095&apve=1&apvf=f", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 612.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.934992Z", "time": 432.**************, "request": {"method": "GET", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-F6GPDJDCJY&gtm=45je58c0v9191852910za200zd9191852910&_p=*************&gcd=13l3l3l3l3l1&npa=1&dma=0&tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&cid=*********.**********&ul=en-us&sr=1920x1080&uaa=&uab=&uafvl=&uamb=0&uam=&uap=&uapv=&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AAAAAAQ&ngs=1&_s=1&sid=**********&sct=1&seg=1&dl=https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription&dr=https%3A%2F%2Fauth.augmentcode.com%2F&dt=&_tu=AAg&en=page_view&_ee=1&tfd=10106", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 432.**************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.935005Z", "time": 547.*************, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/i", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 547.*************, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.935021Z", "time": 702, "request": {"method": "GET", "url": "https://api.grdt.augmentcode.com/v1/p", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 702, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.935037Z", "time": 1045.*************, "request": {"method": "GET", "url": "https://analytics.twitter.com/i/adsct?txn_id=pva71&p_id=Twitter&tw_sale_amount=0&tw_order_quantity=0", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 1045.*************, "receive": 0}, "resourceType": "img"}, {"startedDateTime": "2025-08-14T11:29:24.935051Z", "time": 1313.5, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/credits", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 381, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 381}, "cache": {}, "timings": {"send": 0, "wait": 1313.5, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.935088Z", "time": 472.8999999910593, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/payment", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 346, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 346}, "cache": {}, "timings": {"send": 0, "wait": 472.8999999910593, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.935106Z", "time": 284.80000001192093, "request": {"method": "GET", "url": "https://app.augmentcode.com/api/deletions", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 336, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 336}, "cache": {}, "timings": {"send": 0, "wait": 284.80000001192093, "receive": 0}, "resourceType": "fetch"}, {"startedDateTime": "2025-08-14T11:29:24.935124Z", "time": 407.***********023, "request": {"method": "GET", "url": "https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "application/javascript"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 407.***********023, "receive": 0}, "resourceType": "script"}, {"startedDateTime": "2025-08-14T11:29:24.935149Z", "time": 295.**************, "request": {"method": "GET", "url": "https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1755142162393&ver=1.260.0&compression=base64", "httpVersion": "HTTP/1.1", "headers": [], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [], "cookies": [], "content": {"size": 0, "mimeType": "text/html"}, "redirectURL": "", "headersSize": -1, "bodySize": 0}, "cache": {}, "timings": {"send": 0, "wait": 295.**************, "receive": 0}, "resourceType": "fetch"}], "websockets": [], "pages": [{"startedDateTime": "2025-08-14T11:29:31.228384Z", "id": "page_1", "title": "RoxySpider Capture", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}]}}