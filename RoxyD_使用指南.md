# RoxyD.py 改进版使用指南

## 概述

改进后的 RoxyD.py 具有强大的反检测能力和人性化行为模拟，能够有效绕过网站的自动化检测机制。

## 环境准备

### 1. 必需的环境变量

在运行前，请确保设置以下环境变量：

```bash
# 指纹浏览器配置
ROXYBRWOSER_ID=你的浏览器ID
ROXYWORK_ID=你的工作空间ID  
ROXYTOKEN=你的RoxyClient令牌

# 验证码服务配置
CAPTCHA_URL=http://************:5000
```

### 2. 创建 .env 文件

在项目根目录创建 `.env` 文件：

```env
ROXYBRWOSER_ID=1c42949de80fb5f609dc1fcee8fddde3
ROXYWORK_ID=12345
ROXYTOKEN=76b402c778930dd44bacc693cb2fb8d0
CAPTCHA_URL=http://************:5000
```

### 3. 依赖安装

```bash
pip install selenium requests python-dotenv
```

## 使用方法

### 1. 基本运行

```python
from dotenv import load_dotenv
load_dotenv()

# 运行主程序
if __name__ == "__main__":
    from RoxyD import main
    main()
```

### 2. 手动应用反检测（如果需要自定义）

```python
from RoxyD import setup_driver, apply_anti_detection

# 创建driver
driver = setup_driver()

# 应用反检测机制
apply_anti_detection(driver)

# 现在可以正常使用driver
driver.get("https://example.com")
```

### 2. 测试反检测效果

```bash
python test_roxyd_improvements.py
```

### 3. 批量运行

```python
# 在 RoxyD.py 的 if __name__ == "__main__": 部分
while True:
    try:
        main()
        n += 1
        print(f"添加第{n}个")
        time.sleep(random.uniform(10, 20))  # 间隔时间
    except Exception as e:
        print(f"报错: {e}")
```

## 关键改进功能

### 1. 反检测机制

- **CDP 注入**：在页面加载前注入反检测脚本
- **浏览器指纹伪装**：隐藏自动化特征
- **硬件信息伪造**：模拟真实设备
- **网络信息伪装**：伪造连接状态

### 2. 人类行为模拟

- **智能输入**：模拟真实打字速度和停顿
- **鼠标轨迹**：多阶段移动和点击
- **页面交互**：滚动、焦点变化等
- **时间随机化**：所有操作都有随机延迟

### 3. 错误处理

- **自动重试**：关键步骤失败时自动重试
- **资源清理**：确保浏览器正确关闭
- **详细日志**：记录所有操作和错误

## 配置优化

### 1. 时间间隔调整

根据需要调整各种延迟时间：

```python
# 在相关函数中修改这些值
initial_pause = random.uniform(0.8, 1.5)  # 初始停顿
typing_delay = random.uniform(0.03, 0.08)  # 打字延迟
click_pause = random.uniform(0.1, 0.3)     # 点击停顿
```

### 2. 重试次数配置

```python
max_email_attempts = 10        # 邮箱验证码获取重试次数
max_retries = 5               # 注册重试次数
```

### 3. 浏览器参数

可以在 `setup_driver()` 中添加更多浏览器参数：

```python
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')
chrome_options.add_argument('--disable-gpu')
```

## 故障排除

### 1. 常见问题

**问题**: 浏览器启动失败
**解决**: 检查 RoxyClient 配置和环境变量

**问题**: 验证码获取失败
**解决**: 检查验证码服务是否正常运行

**问题**: 页面加载超时
**解决**: 增加超时时间或检查网络连接

### 2. 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 3. 检查反检测效果

访问 https://bot.sannysoft.com/ 检查反检测效果，大部分项目应该显示为绿色。

## 最佳实践

### 1. 运行频率

- 避免过于频繁的操作
- 建议每次运行间隔 10-20 秒
- 可以根据成功率调整间隔

### 2. 监控和维护

- 定期检查成功率
- 关注网站反爬虫策略变化
- 及时更新反检测脚本

### 3. 安全考虑

- 使用代理IP
- 避免在同一IP下大量操作
- 保持适度的操作频率

## 性能优化

### 1. 内存管理

```python
# 确保浏览器正确关闭
try:
    driver.quit()
except:
    pass
```

### 2. 并发控制

避免同时运行多个实例，可能导致资源冲突。

### 3. 日志管理

定期清理日志文件，避免占用过多磁盘空间。

## 更新和维护

### 1. 定期更新

- 关注目标网站的变化
- 更新反检测脚本
- 调整行为模拟参数

### 2. 版本控制

建议使用 Git 管理代码版本，方便回滚和比较。

### 3. 备份配置

定期备份配置文件和环境变量设置。

## 技术支持

如果遇到问题，请：

1. 检查环境变量配置
2. 运行测试脚本验证功能
3. 查看详细日志信息
4. 检查网络连接和服务状态

## 注意事项

1. 请遵守目标网站的使用条款
2. 合理控制操作频率
3. 定期更新反检测机制
4. 保护好账号和配置信息
