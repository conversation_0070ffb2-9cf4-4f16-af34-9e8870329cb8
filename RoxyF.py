#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RoxyF.py - 基于 HAR 包分析的全新 Augment Code 自动化注册脚本
根据实际网络流量分析重构的高效版本
"""

import time
import random
import json
import re
import os
import uuid
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains
from RoxyClient import RoxyClient
from main import get_email, get_email_data, get_captcha, add_session

# 全局配置
DOMAIN = "smartdocker.online"
BROWSER_ID = "1c42949de80fb5f609dc1fcee8fddde3"
ROXY_PORT = 50000
ROXY_TOKEN = "76b402c778930dd44bacc693cb2fb8d0"

# 网络会话
session = requests.Session()

# 配置类
class Config:
    """RoxyF 配置管理"""

    # 基于 HAR 分析的 URL 配置
    LOGIN_URL = "https://login.augmentcode.com/u/login/identifier"
    AUTH_DOMAIN = "login.augmentcode.com"
    APP_DOMAIN = "app.augmentcode.com"

    # 验证码配置
    TURNSTILE_SITEKEY = "0x4AAAAAAAQFNSW6xordsuIq"
    CAPTCHA_SERVICE_URL = "http://192.168.3.10:5000"

    # 时间配置
    PAGE_LOAD_TIMEOUT = 15
    VERIFICATION_TIMEOUT = 30
    DASHBOARD_TIMEOUT = 45

    # 重试配置
    MAX_EMAIL_ATTEMPTS = 10
    MAX_REGISTRATION_ATTEMPTS = 3

    # 延迟配置
    TYPING_DELAY_MIN = 0.05
    TYPING_DELAY_MAX = 0.15
    ACTION_DELAY_MIN = 1.0
    ACTION_DELAY_MAX = 2.0

    @classmethod
    def load_from_file(cls, config_file="roxyf_config.json"):
        """从文件加载配置"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                for key, value in config_data.items():
                    if hasattr(cls, key.upper()):
                        setattr(cls, key.upper(), value)

                print(f"配置已从 {config_file} 加载")
            else:
                print(f"配置文件 {config_file} 不存在，使用默认配置")
        except Exception as e:
            print(f"加载配置文件失败: {e}")

    @classmethod
    def save_to_file(cls, config_file="roxyf_config.json"):
        """保存配置到文件"""
        try:
            config_data = {
                "login_url": cls.LOGIN_URL,
                "auth_domain": cls.AUTH_DOMAIN,
                "app_domain": cls.APP_DOMAIN,
                "turnstile_sitekey": cls.TURNSTILE_SITEKEY,
                "captcha_service_url": cls.CAPTCHA_SERVICE_URL,
                "page_load_timeout": cls.PAGE_LOAD_TIMEOUT,
                "verification_timeout": cls.VERIFICATION_TIMEOUT,
                "dashboard_timeout": cls.DASHBOARD_TIMEOUT,
                "max_email_attempts": cls.MAX_EMAIL_ATTEMPTS,
                "max_registration_attempts": cls.MAX_REGISTRATION_ATTEMPTS
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            print(f"配置已保存到 {config_file}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")

# 日志系统
class Logger:
    """RoxyF 日志系统"""

    def __init__(self, log_file=None):
        if not log_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = f"roxyf_log_{timestamp}.txt"

        self.log_file = log_file
        self.ensure_log_dir()

    def ensure_log_dir(self):
        """确保日志目录存在"""
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        self.log_file = os.path.join(log_dir, os.path.basename(self.log_file))

    def log(self, level, message):
        """记录日志"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] [{level}] {message}\n"

            # 输出到控制台
            print(f"[{level}] {message}")

            # 写入文件
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            print(f"写入日志失败: {e}")

    def info(self, message):
        self.log("INFO", message)

    def warning(self, message):
        self.log("WARNING", message)

    def error(self, message):
        self.log("ERROR", message)

    def success(self, message):
        self.log("SUCCESS", message)

# 全局日志实例
logger = Logger()

class AugmentRegistrationBot:
    """基于 HAR 分析的 Augment Code 注册机器人"""
    
    def __init__(self):
        self.driver = None
        self.email = None
        self.jwt = None
        self.network_logs = []
        
    def setup_driver(self):
        """初始化 RoxyClient 浏览器"""
        try:
            # 初始化 RoxyClient
            client = RoxyClient(port=ROXY_PORT, token=ROXY_TOKEN)
            client.browser_local_cache([BROWSER_ID])
            client.browser_server_cache(22854, [BROWSER_ID])
            client.browser_random_env(22854, [BROWSER_ID])
            
            # 打开浏览器
            rsp = client.browser_open(BROWSER_ID)
            if rsp.get("code") != 0:
                raise Exception(f"浏览器打开失败: {rsp}")
            
            debugger_address = rsp.get("data").get("http")
            driver_path = rsp.get("data").get("driver")
            
            print(f"浏览器已启动: {debugger_address}")
            
            # 连接到浏览器
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_experimental_option("debuggerAddress", debugger_address)
            
            chrome_service = Service(driver_path)
            self.driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
            self.driver.implicitly_wait(10)
            
            # 应用反检测设置
            self.setup_anti_detection()
            
            print("WebDriver 初始化成功")
            return True
            
        except Exception as e:
            print(f"WebDriver 初始化失败: {e}")
            return False
    
    def setup_anti_detection(self):
        """设置反检测脚本"""
        try:
            self.driver.execute_script("""
                // 隐藏 webdriver 属性
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
                
                // 伪造常见属性
                Object.defineProperty(navigator, 'languages', { get: () => ['en-US','en'] });
                Object.defineProperty(navigator, 'platform', { get: () => 'Win32' });
                
                // 硬件指纹伪装
                Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => 8 });
                Object.defineProperty(navigator, 'deviceMemory', { get: () => 8 });
                
                // 网络连接信息
                if (navigator.connection) {
                    Object.defineProperty(navigator.connection, 'downlink', { get: () => 10 });
                    Object.defineProperty(navigator.connection, 'effectiveType', { get: () => '4g' });
                    Object.defineProperty(navigator.connection, 'rtt', { get: () => 50 });
                }
                
                // 权限查询修补
                const originalQuery = navigator.permissions && navigator.permissions.query;
                if (originalQuery) {
                    navigator.permissions.query = (parameters) => (
                        parameters && parameters.name === 'notifications'
                            ? Promise.resolve({ state: Notification.permission })
                            : originalQuery(parameters)
                    );
                }
                
                console.log('反检测脚本已加载');
            """)
            print("反检测设置完成")
        except Exception as e:
            print(f"反检测设置失败: {e}")
    
    def get_email_account(self):
        """获取邮箱账户"""
        try:
            email_jwt = get_email()
            if not email_jwt:
                print("获取邮箱失败")
                return False
            
            self.email, self.jwt = email_jwt
            print(f"获取邮箱成功: {self.email}")
            return True
            
        except Exception as e:
            print(f"获取邮箱出错: {e}")
            return False
    
    def navigate_to_login(self):
        """导航到登录页面"""
        try:
            # 基于 HAR 分析，直接访问登录页面
            login_url = "https://login.augmentcode.com/u/login/identifier"
            
            print(f"导航到登录页面: {login_url}")
            self.driver.get(login_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            print("登录页面加载完成")
            return True
            
        except Exception as e:
            print(f"导航到登录页面失败: {e}")
            return False
    
    def input_email(self):
        """输入邮箱地址"""
        try:
            # 等待邮箱输入框
            email_input = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type='email'], input[name='email'], input[placeholder*='email']"))
            )
            
            # 清空并输入邮箱
            email_input.clear()
            time.sleep(random.uniform(0.5, 1.0))
            
            # 模拟人类打字
            for char in self.email:
                email_input.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            print(f"邮箱输入完成: {self.email}")
            
            # 查找并点击继续按钮
            continue_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit'], button[data-action-button-primary='true'], .auth0-lock-submit"))
            )
            
            time.sleep(random.uniform(1.0, 2.0))
            continue_button.click()
            
            print("点击继续按钮")
            return True
            
        except Exception as e:
            print(f"输入邮箱失败: {e}")
            return False
    
    def handle_email_verification(self):
        """处理邮箱验证 - 基于 HAR 分析优化"""
        try:
            print("开始邮箱验证流程...")

            # 使用专用的邮箱验证处理器
            email_handler = EmailVerificationHandler(self.driver, self.jwt)

            # 等待验证页面
            code_input = email_handler.wait_for_verification_page()
            if not code_input:
                print("未找到验证码输入页面")
                return False

            # 获取验证码
            verification_code = email_handler.get_verification_code()
            if not verification_code:
                return False

            # 输入验证码
            if not email_handler.input_verification_code(code_input, verification_code):
                return False

            # 提交验证
            if not email_handler.submit_verification():
                return False

            print("邮箱验证完成")
            return True

        except Exception as e:
            print(f"邮箱验证失败: {e}")
            return False
    
    def handle_turnstile_challenge(self):
        """处理 Turnstile 验证码 - 基于 HAR 分析优化"""
        try:
            print("检查 Turnstile 验证码...")

            # 使用专用的 Turnstile 处理器
            turnstile_handler = TurnstileHandler(self.driver)

            # 检测 Turnstile
            if not turnstile_handler.detect_turnstile():
                print("未发现 Turnstile 验证码")
                return True

            print("发现 Turnstile 验证码，开始求解...")

            # 求解 Turnstile
            if turnstile_handler.solve_turnstile():
                print("Turnstile 验证码处理成功")
                time.sleep(3)  # 等待验证完成
                return True
            else:
                print("Turnstile 验证码处理失败")
                return False

        except Exception as e:
            print(f"Turnstile 验证码处理失败: {e}")
            return False
    
    def wait_for_dashboard(self):
        """等待进入仪表板 - 基于 HAR 分析的智能检测"""
        try:
            print("等待进入仪表板...")

            # 基于 HAR 分析的成功页面模式
            success_patterns = [
                'dashboard', 'app.augmentcode.com', 'account', 'workspace',
                'home', 'profile', 'settings', 'subscription'
            ]

            # 基于 HAR 分析的错误页面模式
            error_patterns = [
                'error', 'rejected', 'failed', 'denied', 'blocked'
            ]

            start_time = time.time()
            max_wait_time = 45  # 增加等待时间

            while time.time() - start_time < max_wait_time:
                try:
                    current_url = self.driver.current_url.lower()
                    page_title = self.driver.title.lower()

                    # 检查成功模式
                    for pattern in success_patterns:
                        if pattern in current_url or pattern in page_title:
                            print(f"✅ 成功进入: {self.driver.current_url}")
                            print(f"📄 页面标题: {self.driver.title}")

                            # 额外检查页面内容确认成功
                            if self.verify_success_page():
                                return True

                    # 检查错误模式
                    for pattern in error_patterns:
                        if pattern in current_url or pattern in page_title:
                            print(f"❌ 检测到错误页面: {pattern}")
                            return False

                    # 检查页面中的错误元素
                    if self.check_page_errors():
                        return False

                    time.sleep(2)

                except Exception as check_error:
                    print(f"检查页面状态时出错: {check_error}")
                    time.sleep(2)

            print("⏰ 等待仪表板超时")
            return False

        except Exception as e:
            print(f"❌ 等待仪表板失败: {e}")
            return False

    def verify_success_page(self):
        """验证是否为成功页面"""
        try:
            # 检查页面中的成功指标
            success_indicators = self.driver.execute_script("""
                const indicators = {
                    hasUserMenu: false,
                    hasNavigation: false,
                    hasContent: false,
                    noErrors: true
                };

                // 检查用户菜单
                const userMenus = document.querySelectorAll(
                    '.user-menu, .profile-menu, [data-testid*="user"], .avatar'
                );
                indicators.hasUserMenu = userMenus.length > 0;

                // 检查导航
                const navs = document.querySelectorAll(
                    'nav, .navigation, .sidebar, .menu'
                );
                indicators.hasNavigation = navs.length > 0;

                // 检查主要内容
                const content = document.querySelectorAll(
                    'main, .main-content, .dashboard, .workspace'
                );
                indicators.hasContent = content.length > 0;

                // 检查错误信息
                const errors = document.querySelectorAll(
                    '.error, .alert-danger, [role="alert"], .error-message'
                );
                indicators.noErrors = errors.length === 0;

                return indicators;
            """)

            # 至少需要满足2个成功指标
            success_count = sum([
                success_indicators.get('hasUserMenu', False),
                success_indicators.get('hasNavigation', False),
                success_indicators.get('hasContent', False),
                success_indicators.get('noErrors', True)
            ])

            if success_count >= 2:
                print(f"✅ 页面验证成功 (满足 {success_count}/4 个指标)")
                return True
            else:
                print(f"⚠️ 页面验证不足 (仅满足 {success_count}/4 个指标)")
                return False

        except Exception as e:
            print(f"验证成功页面失败: {e}")
            return False

    def check_page_errors(self):
        """检查页面中的错误信息"""
        try:
            # 基于 HAR 分析的错误检测
            error_selectors = [
                ".error", ".alert-danger", "[role='alert']", ".error-message",
                ".notification-error", ".toast-error", ".banner-error"
            ]

            for selector in error_selectors:
                error_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for error in error_elements:
                    error_text = error.text.lower()
                    if any(keyword in error_text for keyword in
                          ['rejected', 'failed', 'denied', 'blocked', 'error']):
                        print(f"❌ 检测到错误信息: {error.text}")
                        return True

            # 检查页面源码中的错误信息
            page_source = self.driver.page_source.lower()
            error_keywords = ['sign-up rejected', 'registration failed', 'account blocked']

            for keyword in error_keywords:
                if keyword in page_source:
                    print(f"❌ 在页面源码中检测到错误: {keyword}")
                    return True

            return False

        except Exception as e:
            print(f"检查页面错误失败: {e}")
            return False
    
    def extract_auth_info(self):
        """提取认证信息"""
        try:
            print("提取认证信息...")
            
            # 尝试从页面中提取有用的认证信息
            auth_info = self.driver.execute_script("""
                const authData = {};
                
                // 检查 localStorage
                try {
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key.includes('auth') || key.includes('token') || key.includes('user')) {
                            authData[key] = localStorage.getItem(key);
                        }
                    }
                } catch (e) {}
                
                // 检查 sessionStorage
                try {
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        if (key.includes('auth') || key.includes('token') || key.includes('user')) {
                            authData[key] = sessionStorage.getItem(key);
                        }
                    }
                } catch (e) {}
                
                // 检查 cookies
                authData.cookies = document.cookie;
                
                return authData;
            """)
            
            if auth_info:
                print("认证信息提取成功")
                # 保存认证信息
                self.save_auth_info(auth_info)
                return True
            else:
                print("未找到认证信息")
                return False
                
        except Exception as e:
            print(f"提取认证信息失败: {e}")
            return False
    
    def save_auth_info(self, auth_info):
        """保存认证信息"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"auth_info_{self.email.replace('@', '_')}_{timestamp}.json"
            
            auth_dir = "auth_data"
            if not os.path.exists(auth_dir):
                os.makedirs(auth_dir)
            
            filepath = os.path.join(auth_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump({
                    "email": self.email,
                    "timestamp": timestamp,
                    "current_url": self.driver.current_url,
                    "auth_data": auth_info
                }, f, indent=2, ensure_ascii=False)
            
            print(f"认证信息已保存: {filepath}")
            
        except Exception as e:
            print(f"保存认证信息失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.driver.quit()
                print("浏览器已关闭")
        except Exception as e:
            print(f"清理资源失败: {e}")
    
    def run_registration(self):
        """执行完整的注册流程 - 基于 HAR 分析优化"""
        network_monitor = None

        try:
            print("=" * 50)
            print("开始 Augment Code 自动注册流程 (RoxyF)")
            print("=" * 50)

            # 1. 初始化浏览器
            if not self.setup_driver():
                return False

            # 2. 启动网络监控
            network_monitor = NetworkMonitor(self.driver)
            network_monitor.start_monitoring()

            # 3. 获取邮箱账户
            if not self.get_email_account():
                return False

            # 4. 导航到登录页面
            if not self.navigate_to_login():
                return False
            network_monitor.detect_auth_requests()

            # 5. 输入邮箱
            if not self.input_email():
                return False
            network_monitor.log_current_page()

            # 6. 处理邮箱验证
            if not self.handle_email_verification():
                return False
            network_monitor.log_current_page()

            # 7. 处理 Turnstile 验证码
            if not self.handle_turnstile_challenge():
                return False
            network_monitor.log_current_page()

            # 8. 等待进入仪表板
            if not self.wait_for_dashboard():
                return False
            network_monitor.log_current_page()

            # 9. 提取认证信息
            self.extract_auth_info()

            print("=" * 50)
            print("✅ 注册流程完成！")
            print(f"📧 邮箱: {self.email}")
            print(f"🌐 当前页面: {self.driver.current_url}")
            print("=" * 50)

            return True

        except Exception as e:
            print(f"❌ 注册流程失败: {e}")
            return False
        finally:
            # 保存网络监控日志
            if network_monitor:
                try:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    log_filename = f"roxyf_network_{self.email.replace('@', '_') if self.email else 'unknown'}_{timestamp}.json"
                    network_monitor.save_network_log(log_filename)
                except Exception as log_error:
                    print(f"保存网络日志失败: {log_error}")

            # 保持浏览器打开一段时间以便检查
            print("保持浏览器打开 30 秒以便检查...")
            time.sleep(30)
            self.cleanup()

def main():
    """主函数"""
    bot = AugmentRegistrationBot()
    
    max_attempts = 3
    for attempt in range(max_attempts):
        print(f"\n第 {attempt + 1}/{max_attempts} 次尝试")
        
        try:
            if bot.run_registration():
                print("注册成功！")
                break
            else:
                print("注册失败，准备重试...")
                bot.cleanup()
                time.sleep(random.uniform(10, 20))
                
        except Exception as e:
            print(f"尝试 {attempt + 1} 失败: {e}")
            bot.cleanup()
            time.sleep(random.uniform(10, 20))
    
    else:
        print("所有尝试均失败")

class NetworkMonitor:
    """网络监控类，基于 HAR 分析的网络请求监控"""

    def __init__(self, driver):
        self.driver = driver
        self.requests_log = []

    def start_monitoring(self):
        """开始网络监控"""
        try:
            # 基于 HAR 分析，监控关键的网络请求
            self.log_current_page()
            print("网络监控已启动")
        except Exception as e:
            print(f"网络监控启动失败: {e}")

    def log_current_page(self):
        """记录当前页面信息"""
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title

            # 获取页面资源信息
            resources = self.driver.execute_script("""
                const resources = [];
                if (window.performance && window.performance.getEntriesByType) {
                    const entries = window.performance.getEntriesByType('resource');
                    entries.forEach(entry => {
                        resources.push({
                            name: entry.name,
                            type: entry.initiatorType,
                            duration: entry.duration,
                            size: entry.transferSize || 0
                        });
                    });
                }
                return resources;
            """)

            request_entry = {
                "timestamp": datetime.now().isoformat(),
                "url": current_url,
                "title": page_title,
                "resources": resources
            }

            self.requests_log.append(request_entry)
            print(f"记录页面: {page_title} ({current_url})")

        except Exception as e:
            print(f"记录页面信息失败: {e}")

    def detect_auth_requests(self):
        """检测认证相关的网络请求"""
        try:
            # 基于 HAR 分析，检测关键的认证请求
            auth_patterns = [
                'login.augmentcode.com',
                'auth0.com',
                'passwordless-email-challenge',
                'identifier',
                'oauth',
                'token'
            ]

            current_url = self.driver.current_url
            for pattern in auth_patterns:
                if pattern in current_url:
                    print(f"检测到认证请求: {pattern}")
                    self.log_current_page()
                    break

        except Exception as e:
            print(f"检测认证请求失败: {e}")

    def save_network_log(self, filename=None):
        """保存网络日志"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"network_log_{timestamp}.json"

            log_dir = "network_logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            filepath = os.path.join(log_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.requests_log, f, indent=2, ensure_ascii=False)

            print(f"网络日志已保存: {filepath}")

        except Exception as e:
            print(f"保存网络日志失败: {e}")

class TurnstileHandler:
    """Turnstile 验证码处理器，基于 HAR 分析优化"""

    def __init__(self, driver):
        self.driver = driver

    def detect_turnstile(self):
        """检测 Turnstile 验证码"""
        try:
            # 基于 HAR 分析的 Turnstile 检测模式
            turnstile_selectors = [
                "iframe[src*='turnstile']",
                ".cf-turnstile",
                "[data-sitekey]",
                "iframe[src*='cloudflare']",
                ".turnstile-wrapper"
            ]

            for selector in turnstile_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"检测到 Turnstile 验证码: {selector}")
                    return True

            # 检查页面源码中的 Turnstile 相关内容
            page_source = self.driver.page_source
            turnstile_keywords = ['turnstile', 'cf-turnstile', 'cloudflare']

            for keyword in turnstile_keywords:
                if keyword in page_source.lower():
                    print(f"在页面源码中检测到 Turnstile: {keyword}")
                    return True

            return False

        except Exception as e:
            print(f"检测 Turnstile 失败: {e}")
            return False

    def solve_turnstile(self):
        """求解 Turnstile 验证码"""
        try:
            current_url = self.driver.current_url
            print(f"开始求解 Turnstile 验证码: {current_url}")

            # 获取 sitekey
            sitekey = self.get_sitekey()
            if not sitekey:
                sitekey = "0x4AAAAAAAQFNSW6xordsuIq"  # 默认 sitekey

            print(f"使用 sitekey: {sitekey}")

            # 调用验证码求解服务
            token = get_captcha(current_url)

            if token:
                print("Turnstile 验证码求解成功")
                self.inject_token(token)
                return True
            else:
                print("Turnstile 验证码求解失败")
                return False

        except Exception as e:
            print(f"求解 Turnstile 失败: {e}")
            return False

    def get_sitekey(self):
        """获取 Turnstile sitekey"""
        try:
            # 从页面中提取 sitekey
            sitekey = self.driver.execute_script("""
                // 查找 data-sitekey 属性
                const elements = document.querySelectorAll('[data-sitekey]');
                if (elements.length > 0) {
                    return elements[0].getAttribute('data-sitekey');
                }

                // 查找 iframe 中的 sitekey
                const iframes = document.querySelectorAll('iframe[src*="turnstile"]');
                for (const iframe of iframes) {
                    const src = iframe.src;
                    const match = src.match(/sitekey=([^&]+)/);
                    if (match) {
                        return match[1];
                    }
                }

                return null;
            """)

            return sitekey

        except Exception as e:
            print(f"获取 sitekey 失败: {e}")
            return None

    def inject_token(self, token):
        """注入 Turnstile token"""
        try:
            self.driver.execute_script(f"""
                // 查找并填充 Turnstile 响应字段
                const responseInputs = document.querySelectorAll(
                    'input[name="cf-turnstile-response"], ' +
                    'input[name*="turnstile"], ' +
                    'textarea[name="cf-turnstile-response"]'
                );

                responseInputs.forEach(input => {{
                    input.value = '{token}';
                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    console.log('Turnstile token 已注入:', input.name);
                }});

                // 触发 Turnstile 回调
                if (window.turnstile && window.turnstile.render) {{
                    console.log('Turnstile API 可用');
                }}

                // 查找并触发提交按钮
                const submitButtons = document.querySelectorAll(
                    'button[type="submit"], ' +
                    'input[type="submit"], ' +
                    '.submit-button, ' +
                    '[data-action-button-primary="true"]'
                );

                if (submitButtons.length > 0) {{
                    console.log('找到提交按钮，准备提交');
                }}
            """)

            print("Turnstile token 注入完成")

        except Exception as e:
            print(f"注入 Turnstile token 失败: {e}")

class EmailVerificationHandler:
    """邮箱验证处理器"""

    def __init__(self, driver, jwt):
        self.driver = driver
        self.jwt = jwt

    def wait_for_verification_page(self):
        """等待邮箱验证页面"""
        try:
            # 基于 HAR 分析，等待特定的验证页面元素
            verification_selectors = [
                "input[type='text'][placeholder*='code']",
                "input[name='code']",
                "input[placeholder*='verification']",
                ".verification-code-input",
                "[data-testid='verification-code']"
            ]

            for selector in verification_selectors:
                try:
                    element = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    print(f"找到验证码输入框: {selector}")
                    return element
                except TimeoutException:
                    continue

            print("未找到验证码输入框")
            return None

        except Exception as e:
            print(f"等待验证页面失败: {e}")
            return None

    def get_verification_code(self, max_attempts=10):
        """获取邮箱验证码"""
        for attempt in range(max_attempts):
            try:
                print(f"获取验证码 (尝试 {attempt + 1}/{max_attempts})")

                code = get_email_data(self.jwt)
                if code and len(code) >= 4:  # 验证码通常至少4位
                    print(f"获取到验证码: {code}")
                    return code

            except Exception as e:
                print(f"获取验证码失败: {e}")

            time.sleep(5)

        print("无法获取验证码")
        return None

    def input_verification_code(self, code_input, code):
        """输入验证码"""
        try:
            # 清空输入框
            code_input.clear()
            time.sleep(0.5)

            # 模拟人类输入
            for char in code:
                code_input.send_keys(char)
                time.sleep(random.uniform(0.1, 0.3))

            print(f"验证码输入完成: {code}")
            return True

        except Exception as e:
            print(f"输入验证码失败: {e}")
            return False

    def submit_verification(self):
        """提交验证码"""
        try:
            # 查找提交按钮
            submit_selectors = [
                "button[type='submit']",
                "button[data-action-button-primary='true']",
                ".auth0-lock-submit",
                ".submit-button",
                "input[type='submit']"
            ]

            for selector in submit_selectors:
                try:
                    button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if button.is_enabled():
                        time.sleep(random.uniform(1.0, 2.0))
                        button.click()
                        print(f"点击提交按钮: {selector}")
                        return True
                except NoSuchElementException:
                    continue

            print("未找到可用的提交按钮")
            return False

        except Exception as e:
            print(f"提交验证失败: {e}")
            return False

def run_continuous_registration():
    """持续运行注册流程"""
    success_count = 0
    total_attempts = 0

    while True:
        total_attempts += 1
        print(f"\n{'='*60}")
        print(f"第 {total_attempts} 次注册尝试")
        print(f"成功次数: {success_count}")
        print(f"{'='*60}")

        bot = AugmentRegistrationBot()

        try:
            if bot.run_registration():
                success_count += 1
                print(f"✅ 注册成功！总成功次数: {success_count}")

                # 记录成功信息
                add_session(bot.email, "success", "RoxyF")

            else:
                print("❌ 注册失败")

        except Exception as e:
            print(f"❌ 注册过程出错: {e}")

        finally:
            bot.cleanup()

        # 等待间隔
        wait_time = random.uniform(30, 60)
        print(f"等待 {wait_time:.1f} 秒后进行下一次尝试...")
        time.sleep(wait_time)

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        run_continuous_registration()
    else:
        main()
