2025-08-10 10:19:44 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57148,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:19:44 - INFO - <EMAIL>
2025-08-10 10:19:44 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:20:00 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:20:03 - INFO - 找到 Turnstile...
2025-08-10 10:20:04 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:20:08 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:20:08 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:20:08 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:20:08 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:20:18 - INFO -    等待 3.71 秒...
2025-08-10 10:20:25 - INFO - 刷新完成，继续下一次尝试。
2025-08-10 10:20:25 - INFO - 
第 2/5 次尝试注册...
2025-08-10 10:20:25 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:20:25 - INFO - 成功！当前在订阅页面。
2025-08-10 10:20:25 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:20:25 - INFO - 代码信息: {
  "codeVerifier": "TwzqdQY5js6MX_-xnY3rDt4pTKYgU6UadwEZpnhIXYg",
  "code_challenge": "y7dHWlv31txPNBIwxFhcdGvbPYWq7E1NE1RCaUE6WyE",
  "state": "55a9f86f-b44c-48b5-be08-381d4e7e3bd1"
}
2025-08-10 10:20:25 - INFO - ==================================================
2025-08-10 10:20:26 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_d06353b7eaeac3696e12fbaa7733d36c&state=55a9f86f-b44c-48b5-be08-381d4e7e3bd1&tenant_url=https%3A%2F%2Fd9.api.augmentcode.com%2F
2025-08-10 10:20:28 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:20:28 - INFO - 添加session: <EMAIL>   0531c9f7cdb5f74342a7fbbce2f71d20ad26b6397fef4db9dc4c11aad07055cb   https://d9.api.augmentcode.com/  2025-08-17T02:20:23Z
2025-08-10 10:20:28 - INFO - email:<EMAIL> === cookie:.eJxNjcEOwiAQRP9lz62BdoHFk39CKCyGpMWmtkaj_rukejCZy0xm3jzBzbxMvnBZ4bguGzeQ_JTHhyt-YjgCNHDONy5_PsfZbVdeXI414Mnn8aXJkpBRMXIX0cY0SEEyiFovlxLqksgQaWFkr6qUIoMN7JidUEklbfdVGoXGdr2hk9KK0BxCivBrfg-pH1CoviWtuUXtbesxdq3uvA1BW0x18P4ADDVAfQ.aJgB6w.aSFadfvh5sO_LtCoCoxLvb8B3ss
2025-08-10 10:20:28 - INFO - 
自动化流程成功完成！
2025-08-10 10:20:28 - INFO - 添加第1个
2025-08-10 10:20:51 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:57302,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:20:52 - INFO - <EMAIL>
2025-08-10 10:20:52 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:21:08 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:21:11 - INFO - 找到 Turnstile...
2025-08-10 10:21:11 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:21:14 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:21:26 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:21:26 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:21:26 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:21:26 - INFO - 检测到 'Verifying you are human...'（首次），使用网络节流模式
2025-08-10 10:21:26 - INFO - 检测到人机验证，执行 CDP 网络节流 10 秒
2025-08-10 10:21:26 - INFO - 执行网络节流前的拟人操作...
2025-08-10 10:21:26 - INFO - 已执行拟人滚动: -107px
2025-08-10 10:21:27 - INFO - 已执行页面焦点操作
2025-08-10 10:21:27 - INFO - 已执行拟人操作完成，总停顿: 1.03秒
2025-08-10 10:21:34 - INFO - CDP: 已将网络设为节流模式（延迟7869ms），保持 10.8 秒……
2025-08-10 10:21:46 - INFO - 执行网络恢复后的拟人操作...
2025-08-10 10:21:46 - INFO - 已执行拟人滚动: 11px
2025-08-10 10:21:47 - INFO - 已执行拟人操作完成，总停顿: 1.10秒
2025-08-10 10:21:47 - INFO - CDP: 网络节流已恢复为正常状态。
2025-08-10 10:21:47 - INFO - 成功！当前在订阅页面。
2025-08-10 10:21:47 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:21:47 - INFO - 代码信息: {
  "codeVerifier": "ngkcCOwfpSG1-qzl5mlcHo5nQwjYxqluXRXSsripyJk",
  "code_challenge": "uyckIM2vdnIUboloO57NKUdh9r46kgxwoj1Cj1DF5Ac",
  "state": "d6508e10-def4-4991-8d65-230be530c459"
}
2025-08-10 10:21:47 - INFO - ==================================================
2025-08-10 10:21:48 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_7aa24c5ae8177843c83b867b754f63ab&state=d6508e10-def4-4991-8d65-230be530c459&tenant_url=https%3A%2F%2Fd4.api.augmentcode.com%2F
2025-08-10 10:21:50 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:21:50 - INFO - 添加session: <EMAIL>   49d698f6ae31b848692deac7d79987e0d079fc68275a8f9a3b7dfb2d0ad8dd96   https://d4.api.augmentcode.com/  2025-08-17T02:21:32Z
2025-08-10 10:21:50 - INFO - email:<EMAIL> === cookie:.eJxNjUsOwjAMRO_idYvi1vm4K25SpY2DIpFQlYLE7-5EFQuWM5r35gXjImv2RcoGw7bepIHoczo_xuKzwADQwCndpfzlFJbxdpV1TKEWkn06v41jpzpkIekCcYgTao7o67xcylzJ3hIxOerJ9uQYTU8N7JrdUE1zfsqGVpPljkgftdGO7GGOAX7L_VBZE-eJpTWeupa0ca1TiO2EHFUFECcFny8Eij-a.aJgCPQ.CXYjG9JnuyMCAOISrIkMzyZB3M4
2025-08-10 10:21:50 - INFO - 
自动化流程成功完成！
2025-08-10 10:21:50 - INFO - 添加第2个
2025-08-10 10:22:05 - INFO - Traceback (most recent call last):

2025-08-10 10:22:05 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m972[0m, in [35m<module>[0m
    [31mtime.sleep[0m[1;31m(random.uniform(10,20))[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-10 10:22:05 - INFO - [1;35mKeyboardInterrupt[0m

