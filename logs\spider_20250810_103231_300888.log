2025-08-10 10:32:37 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:58360,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-10 10:32:38 - INFO - <EMAIL>
2025-08-10 10:32:38 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-10 10:32:54 - INFO - 获取 cap_value 验证码成功...
2025-08-10 10:32:57 - INFO - 找到 Turnstile...
2025-08-10 10:32:59 - INFO - 登录信息已提交，等待验证页面...
2025-08-10 10:33:03 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-10 10:33:03 - INFO - 验证码已提交，等待跳转...
2025-08-10 10:33:03 - INFO - 
第 1/5 次尝试注册...
2025-08-10 10:33:03 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:33:23 - INFO -    等待 4.07 秒...
2025-08-10 10:33:33 - INFO - 刷新完成，继续下一次尝试。
2025-08-10 10:33:33 - INFO - 
第 2/5 次尝试注册...
2025-08-10 10:33:33 - INFO - 步骤 5: 正在勾选复选框并点击注册按钮...
2025-08-10 10:33:33 - INFO - 成功！当前在订阅页面。
2025-08-10 10:33:33 - INFO - 
进入订阅页面，开始获取vscode_url
2025-08-10 10:33:33 - INFO - 代码信息: {
  "codeVerifier": "tfb0COWEeOKu301chtVuTLV_8JePS8-FqNGjE2UArAE",
  "code_challenge": "fPAuiGAVa7GMsH4vB4T_0V7Q6WaF0YlQl2__21h_Ehw",
  "state": "1394dbfc-84be-4dbc-b562-092522b1d4e9"
}
2025-08-10 10:33:33 - INFO - ==================================================
2025-08-10 10:33:33 - INFO - Extracted URL: vscode://augment.vscode-augment/auth/result?code=_e30c4c11312fc8351929ae3c76df23f5&state=1394dbfc-84be-4dbc-b562-092522b1d4e9&tenant_url=https%3A%2F%2Fd2.api.augmentcode.com%2F
2025-08-10 10:33:35 - INFO - 添加session成功: {'status': 'success'}
2025-08-10 10:33:35 - INFO - 添加session: <EMAIL>   c15ef7ef3a4ac20fa9c6227ac989801bd5ae275b69fd8ce75e85c5881dc760d0   https://d2.api.augmentcode.com/  2025-08-17T02:33:17Z
2025-08-10 10:33:35 - INFO - email:<EMAIL> === cookie:.eJxNjcEOgkAMRP-lZzBd6O5ST_4JKbSYTWRVBBOj_rtIPHicyZs3T2gvNo2SLc-wn6fFChhkTKdHm2U02AMUcEx3y3856aVdbja1SdfCRkmnV2i4QdLOyCol1qEj9ij1iudz7tclkUdkZuTgMTQuVljAptkMX9N1FnHRU-TaeXfwwTcUd_2g8CO3Qx3UEXWxVMJYUi1aMldY1qYsErSvVOH9ASPCQPA.aJgE_w.NzcKi77_4S3P2HW3tRf6cRJqKgs
2025-08-10 10:33:35 - INFO - 
自动化流程成功完成！
2025-08-10 10:33:35 - INFO - 添加第1个
2025-08-10 10:33:47 - INFO - Traceback (most recent call last):

2025-08-10 10:33:47 - INFO -   File [35m"e:\Roxyspider\RoxyD.py"[0m, line [35m972[0m, in [35m<module>[0m
    [31mtime.sleep[0m[1;31m(random.uniform(10,20))[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-10 10:33:47 - INFO - [1;35mKeyboardInterrupt[0m

