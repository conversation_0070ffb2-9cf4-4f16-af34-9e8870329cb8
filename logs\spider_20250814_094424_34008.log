2025-08-14 09:44:24 - INFO - 测试 HAR 和 WebSocket 功能...
2025-08-14 09:44:24 - INFO - HAR 文件已保存: har_files\test_har_20250814_094424.har
2025-08-14 09:44:24 - INFO - 捕获了 1 个网络请求
2025-08-14 09:44:24 - INFO - 捕获了 1 个 WebSocket 连接
2025-08-14 09:44:24 - INFO -   WebSocket: wss://app.augmentcode.com/ws - 4 个消息帧
2025-08-14 09:44:24 - INFO - WebSocket 摘要已保存: websocket_logs\test_websocket_20250814_094424.json
2025-08-14 09:44:24 - INFO - HAR 和 WebSocket 功能测试成功！
2025-08-14 09:44:24 - INFO - HAR 文件: har_files\test_har_20250814_094424.har
2025-08-14 09:44:24 - INFO - WebSocket 摘要: websocket_logs\test_websocket_20250814_094424.json
2025-08-14 09:44:24 - INFO - WebSocket 测试统计: {'total_connections': 1, 'active_connections': 0, 'closed_connections': 1, 'total_frames': 4, 'sent_frames': 2, 'received_frames': 2}
2025-08-14 09:44:24 - INFO - HAR 和 WebSocket 功能测试完成
2025-08-14 09:44:24 - INFO - 当前网络日志数量: 1
2025-08-14 09:44:24 - INFO - 当前 WebSocket 日志数量: 1
2025-08-14 09:44:24 - INFO - ==================================================
2025-08-14 09:44:26 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:53509,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 09:44:27 - INFO - WebDriver 创建成功（简化模式）
2025-08-14 09:44:27 - INFO - 网络监控已启用，开始捕获 HAR 数据和 WebSocket 连接...
2025-08-14 09:44:27 - INFO - 启动网络监控（基础模式 - 不依赖性能日志）
2025-08-14 09:44:27 - INFO - 记录页面访问: http://127.0.0.1:45535/dashboard?id=1c42949de80fb5f609dc1fcee8fddde3 (标题: 工作台)
2025-08-14 09:44:27 - INFO - 记录了 34 个页面资源
2025-08-14 09:44:27 - INFO - 页面支持 WebSocket
2025-08-14 09:44:27 - INFO - 网络事件监听器已启动（基础模式）
2025-08-14 09:44:27 - INFO - <EMAIL>
2025-08-14 09:44:27 - INFO - 网络捕获: 主函数开始
2025-08-14 09:44:27 - INFO - 记录了 34 个页面资源
2025-08-14 09:44:27 - INFO - 页面支持 WebSocket
2025-08-14 09:44:27 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 09:44:41 - INFO - 获取 cap_value 验证码成功...
2025-08-14 09:44:45 - INFO - 找到 Turnstile...
2025-08-14 09:44:47 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 09:44:50 - INFO - 网络捕获: 登录完成后
2025-08-14 09:44:50 - INFO - 记录页面访问: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBMenY5Ry1GNjRpUFhIUnBwNUpMN2NSUEhONG55NnI3cqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDJ3Mkc5ZkoybE1rOXhtYm81cXFUb05pbHBNUUNVS3REo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE (标题: Augment Code)
2025-08-14 09:44:50 - INFO - 记录了 21 个页面资源
2025-08-14 09:44:50 - INFO - 页面支持 WebSocket
2025-08-14 09:44:50 - INFO - 检测到页面包含 AJAX 代码
2025-08-14 09:44:50 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 09:44:50 - INFO - CDP:启用节流模式
2025-08-14 09:44:50 - INFO - 验证码已提交，等待跳转...
2025-08-14 09:44:50 - INFO - 网络捕获: 邮箱验证完成后
2025-08-14 09:45:03 - INFO - 记录了 19 个页面资源
2025-08-14 09:45:03 - INFO - 页面支持 WebSocket
2025-08-14 09:45:03 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 09:45:03 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 09:45:03 - INFO - ReCAPTCHA Token: 
2025-08-14 09:45:03 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 09:45:07 - INFO - HAR 文件已保存: har_files\final_lseot1755135864_smartdocker.online_20250814_094507.har
2025-08-14 09:45:07 - INFO - 捕获了 68 个网络请求
2025-08-14 09:45:07 - INFO - 捕获了 0 个 WebSocket 连接
2025-08-14 09:45:07 - INFO - WebSocket 摘要已保存: websocket_logs\final_lseot1755135864_smartdocker.online_20250814_094507_ws.json
2025-08-14 09:45:07 - INFO - Traceback (most recent call last):

2025-08-14 09:45:07 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m2271[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-14 09:45:07 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m2045[0m, in [35mmain[0m
    [31mbypass_human_verification[0m[1;31m(driver, duration=30)[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 09:45:07 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1104[0m, in [35mbypass_human_verification[0m
    [31mtime.sleep[0m[1;31m(random.uniform(0.1, 0.5))[0m
    [31m~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 09:45:07 - INFO - [1;35mKeyboardInterrupt[0m

