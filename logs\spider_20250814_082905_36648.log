2025-08-14 08:29:07 - INFO - 浏览器打开成功,debuggerAddress:127.0.0.1:63881,driverPath:C:\Users\<USER>\AppData\Roaming\RoxyBrowser\chrome-bin\138\chromedriver.exe
2025-08-14 08:29:08 - INFO - <EMAIL>
2025-08-14 08:29:08 - INFO - 步骤 1 & 2: 正在打开登录页面并输入用户名...
2025-08-14 08:29:20 - INFO - 获取 cap_value 验证码成功...
2025-08-14 08:29:24 - INFO - 找到 Turnstile...
2025-08-14 08:29:26 - INFO - 登录信息已提交，等待验证页面...
2025-08-14 08:29:30 - INFO - 步骤 3 & 4: 正在输入邮箱验证码...
2025-08-14 08:29:30 - INFO - CDP:启用节流模式
2025-08-14 08:29:30 - INFO - 验证码已提交，等待跳转...
2025-08-14 08:29:30 - INFO - 页面加载完成，等待 ReCAPTCHA 令牌生成...
2025-08-14 08:29:42 - INFO - 令牌为空，可能页面已经跳转或加载失败。
2025-08-14 08:29:42 - INFO - ReCAPTCHA Token: 
2025-08-14 08:29:42 - INFO - 检测到人类验证页面，开始执行强制鼠标操作...
2025-08-14 08:29:45 - INFO - Traceback (most recent call last):

2025-08-14 08:29:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1430[0m, in [35mgetresponse[0m
    [31mresponse.begin[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m331[0m, in [35mbegin[0m
    version, status, reason = [31mself._read_status[0m[1;31m()[0m
                              [31m~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m292[0m, in [35m_read_status[0m
    line = str([31mself.fp.readline[0m[1;31m(_MAXLINE + 1)[0m, "iso-8859-1")
               [31m~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\socket.py"[0m, line [35m719[0m, in [35mreadinto[0m
    return [31mself._sock.recv_into[0m[1;31m(b)[0m
           [31m~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^[0m

2025-08-14 08:29:45 - INFO - [1;35mConnectionResetError[0m: [35m[WinError 10054] 远程主机强迫关闭了一个现有的连接。[0m

2025-08-14 08:29:45 - INFO - 
During handling of the above exception, another exception occurred:


2025-08-14 08:29:45 - INFO - Traceback (most recent call last):

2025-08-14 08:29:45 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1243[0m, in [35m<module>[0m
    [31mmain[0m[1;31m()[0m
    [31m~~~~[0m[1;31m^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m1212[0m, in [35mmain[0m
    [31mbypass_human_verification[0m[1;31m(driver, duration=30)[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"e:\Roxyspider\RoxyE.py"[0m, line [35m743[0m, in [35mbypass_human_verification[0m
    [31mactions.move_by_offset(next_x - start_x, next_y - start_y).perform[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\action_chains.py"[0m, line [35m93[0m, in [35mperform[0m
    [31mself.w3c_actions.perform[0m[1;31m()[0m
    [31m~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\common\actions\action_builder.py"[0m, line [35m168[0m, in [35mperform[0m
    [31mself.driver.execute[0m[1;31m(Command.W3C_ACTIONS, enc)[0m
    [31m~~~~~~~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py"[0m, line [35m451[0m, in [35mexecute[0m
    response = cast(RemoteConnection, self.command_executor).execute(driver_command, params)

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m404[0m, in [35mexecute[0m
    return [31mself._request[0m[1;31m(command_info[0], url, body=data)[0m
           [31m~~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\selenium\webdriver\remote\remote_connection.py"[0m, line [35m428[0m, in [35m_request[0m
    response = self._conn.request(method, url, body=body, headers=headers, timeout=self._client_config.timeout)

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m143[0m, in [35mrequest[0m
    return [31mself.request_encode_body[0m[1;31m([0m
           [31m~~~~~~~~~~~~~~~~~~~~~~~~[0m[1;31m^[0m
        [1;31mmethod, url, fields=fields, headers=headers, **urlopen_kw[0m
        [1;31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
    [1;31m)[0m
    [1;31m^[0m

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\_request_methods.py"[0m, line [35m278[0m, in [35mrequest_encode_body[0m
    return [31mself.urlopen[0m[1;31m(method, url, **extra_kw)[0m
           [31m~~~~~~~~~~~~[0m[1;31m^^^^^^^^^^^^^^^^^^^^^^^^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\poolmanager.py"[0m, line [35m459[0m, in [35murlopen[0m
    response = conn.urlopen(method, u.request_uri, **kw)

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m787[0m, in [35murlopen[0m
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connectionpool.py"[0m, line [35m534[0m, in [35m_make_request[0m
    response = conn.getresponse()

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m565[0m, in [35mgetresponse[0m
    httplib_response = super().getresponse()

2025-08-14 08:29:45 - INFO -   File [35m"C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\http\client.py"[0m, line [35m1432[0m, in [35mgetresponse[0m
    [31mself.close[0m[1;31m()[0m
    [31m~~~~~~~~~~[0m[1;31m^^[0m

2025-08-14 08:29:45 - INFO -   File [35m"E:\Roxyspider\.venv\Lib\site-packages\urllib3\connection.py"[0m, line [35m369[0m, in [35mclose[0m
    def close(self) -> None:
    

2025-08-14 08:29:45 - INFO - [1;35mKeyboardInterrupt[0m

