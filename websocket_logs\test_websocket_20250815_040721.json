{"captureTime": "2025-08-15T04:07:21.814568Z", "totalConnections": 1, "connections": [{"url": "wss://app.augmentcode.com/ws", "status": "closed", "createdDateTime": "2025-08-15T04:07:21.813318Z", "closedDateTime": "2025-08-15T04:07:21.813325Z", "totalFrames": 4, "sentFrames": 2, "receivedFrames": 2, "handshakeResponse": {"status": 101, "statusText": "Switching Protocols", "headers": {"Upgrade": "websocket", "Connection": "Upgrade", "Sec-WebSocket-Accept": "test-accept-key"}, "timestamp": 1755202041.8133218}, "frames": [{"type": "sent", "timestamp": 1755202041.8133225, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"auth\",\"token\":\"test-token\"}", "payloadLength": 35}, {"type": "received", "timestamp": 1755202041.9133232, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"auth_success\",\"user_id\":\"12345\"}", "payloadLength": 40}, {"type": "sent", "timestamp": 1755202042.8133242, "opcode": 1, "mask": true, "payloadData": "{\"type\":\"ping\"}", "payloadLength": 15}, {"type": "received", "timestamp": 1755202042.9133246, "opcode": 1, "mask": false, "payloadData": "{\"type\":\"pong\"}", "payloadLength": 15}]}]}